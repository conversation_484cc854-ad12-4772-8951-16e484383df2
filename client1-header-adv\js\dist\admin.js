(function(i,I,c){"use strict";const a={SAVE_DEBOUNCE_DELAY:500,DEFAULT_MAX_SLIDES:30,EMPTY_SLIDES_COUNT:0},g=1,D=1,E=-1,l=1;class T extends I{constructor(){super(...arguments),this.slides=[],this.loading=!1,this.nextId=g,this.timeouts={}}oninit(t){super.oninit(t),this.loadExistingSlides()}loadExistingSlides(){const{extensionId:t,maxSlides:e=a.DEFAULT_MAX_SLIDES}=this.attrs,n=[];for(let s=g;s<=e;s+=l){const r=`${t}.Link${s}`,o=`${t}.Image${s}`,d=i.data.settings[r]||"",h=i.data.settings[o]||"";(d||h)&&(n.push({id:s,link:d,image:h}),this.nextId=Math.max(this.nextId,s+l))}this.slides=n,n.length===a.EMPTY_SLIDES_COUNT&&this.addSlide()}addSlide(){const t={id:this.nextId,link:"",image:""};this.nextId+=1,this.slides.push(t),m.redraw()}removeSlide(t){const{extensionId:e}=this.attrs,n=this.slides.findIndex(r=>r.id===t);if(n===E)return;const s=this.slides[n];this.saveSetting(`${e}.Link${s.id}`,""),this.saveSetting(`${e}.Image${s.id}`,""),this.slides.splice(n,l),this.slides.length===a.EMPTY_SLIDES_COUNT&&this.addSlide(),m.redraw()}updateSlide(t,e,n){const{extensionId:s}=this.attrs,r=this.slides.find(d=>d.id===t);if(!r)return;r[e]=n;let o="";e==="link"?o=`${s}.Link${r.id}`:o=`${s}.Image${r.id}`,this.saveSetting(o,n)}saveSetting(t,e){const n=`saveTimeout_${t}`;clearTimeout(this.timeouts[n]),this.timeouts[n]=setTimeout(()=>{i.data.settings[t]=e,i.request({method:"POST",url:i.forum.attribute("apiUrl")+"/settings",body:{[t]:e}}).catch(()=>{})},a.SAVE_DEBOUNCE_DELAY)}view(){return m("div.Form-group",[m("label.FormLabel",i.translator.trans("wusong8899-client1.admin.SlideSettings")),m("div.helpText",i.translator.trans("wusong8899-client1.admin.SlideSettingsHelp")),m("div.DynamicSlideSettings",[this.slides.map((t,e)=>this.renderSlide(t,e)),m("div.DynamicSlideSettings-addButton",[m(c,{className:"Button Button--primary",icon:"fas fa-plus",onclick:()=>this.addSlide()},i.translator.trans("wusong8899-client1.admin.AddSlide"))])])])}renderSlide(t,e){return m("div.DynamicSlideSettings-slide",{key:t.id},[m("div.DynamicSlideSettings-slideHeader",[m("h4",i.translator.trans("wusong8899-client1.admin.SlideNumber",{number:e+l})),m(c,{className:"Button Button--danger",icon:"fas fa-trash",onclick:()=>this.removeSlide(t.id),disabled:this.slides.length===D},i.translator.trans("wusong8899-client1.admin.DeleteSlide"))]),m("div.DynamicSlideSettings-slideFields",[m("div.Form-group",[m("label.FormLabel",i.translator.trans("wusong8899-client1.admin.SlideLink")),m("input.FormControl",{type:"url",placeholder:"https://example.com",value:t.link,oninput:n=>{const s=n.target;this.updateSlide(t.id,"link",s.value)}})]),m("div.Form-group",[m("label.FormLabel",i.translator.trans("wusong8899-client1.admin.SlideImage")),m("input.FormControl",{type:"url",placeholder:"https://example.com/image.jpg",value:t.image,oninput:n=>{const s=n.target;this.updateSlide(t.id,"image",s.value)}})])])])}}class x{constructor(t){this.extensionId=t,this.extensionData=i.extensionData.for(t)}registerTransitionTimeSetting(){return this.extensionData.registerSetting({setting:`${this.extensionId}.TransitionTime`,type:"number",label:i.translator.trans("wusong8899-client1.admin.TransitionTime")}),this}registerHeaderIconUrlSetting(){return this.extensionData.registerSetting({setting:`${this.extensionId}.HeaderIconUrl`,type:"url",label:i.translator.trans("wusong8899-client1.admin.HeaderIconUrl"),help:i.translator.trans("wusong8899-client1.admin.HeaderIconUrlHelp")}),this}registerSocialMediaSettings(){const t=["Kick","Facebook","Twitter","YouTube","Instagram"];for(const e of t)this.extensionData.registerSetting({setting:`${this.extensionId}.Social${e}Url`,type:"url",label:i.translator.trans(`wusong8899-client1.admin.Social${e}Url`),help:i.translator.trans(`wusong8899-client1.admin.Social${e}UrlHelp`)}),this.extensionData.registerSetting({setting:`${this.extensionId}.Social${e}Icon`,type:"text",label:i.translator.trans(`wusong8899-client1.admin.Social${e}Icon`),help:i.translator.trans(`wusong8899-client1.admin.Social${e}IconHelp`)});return this}registerSlideSettings(t=a.DEFAULT_MAX_SLIDES){return this.extensionData.registerSetting(()=>m(T,{extensionId:this.extensionId,maxSlides:t})),this}registerAllSettings(t=a.DEFAULT_MAX_SLIDES){return this.registerTransitionTimeSetting().registerHeaderIconUrlSetting().registerSocialMediaSettings().registerSlideSettings(t)}}const u={EXTENSION_ID:"wusong8899-client1-header-adv",MAX_SLIDES:a.DEFAULT_MAX_SLIDES},_=(S=u.EXTENSION_ID,t=u.MAX_SLIDES)=>{new x(S).registerAllSettings(t)};i.initializers.add("wusong8899/client1-header-adv",()=>{_()})})(flarum.core.compat["admin/app"],flarum.core.compat["common/Component"],flarum.core.compat["common/components/Button"]);
//# sourceMappingURL=admin.js.map

module.exports={};