{"version": 3, "file": "admin.js", "sources": ["../src/common/config/constants.ts", "../src/admin/components/dynamic-slide-settings-component.tsx", "../src/admin/settings-generator.ts", "../src/admin/index.ts"], "sourcesContent": ["/**\n * Application constants to eliminate magic numbers\n */\n\n// Mobile detection constants\nexport const M<PERSON><PERSON><PERSON>_DETECTION = {\n  USER_AGENT_SUBSTR_START: 0,\n  USER_AGENT_SUBSTR_LENGTH: 4,\n} as const;\n\n// Swiper configuration constants\nexport const SWIPER_CONFIG = {\n  MOBILE: {\n    SPACE_BETWEEN: 90,\n    SLIDES_PER_VIEW: 2,\n    TAG_SPACE_BETWEEN: 80,\n    TAG_SLIDES_PER_VIEW: 4,\n  },\n  DESKTOP: {\n    SPACE_BETWEEN: 10,\n    SLIDES_PER_VIEW: 7,\n    TAG_SPACE_BETWEEN: 10,\n    TAG_SLIDES_PER_VIEW: 7,\n  },\n  AUTOPLAY_DELAY: 3000,\n} as const;\n\n// Error handling constants\nexport const ERROR_HANDLING = {\n  MAX_ERROR_LOG_ENTRIES: 50,\n  DOM_READY_TIMEOUT: 5000,\n  SLIDE_NUMBER_MIN: 1,\n  SLIDE_NUMBER_MAX: 30,\n  TRANSITION_TIME_MIN: 1000,\n  TRANSITION_TIME_MAX: 30_000,\n  CONFIG_MAX_SLIDES_MIN: 1,\n  CONFIG_MAX_SLIDES_MAX: 50,\n} as const;\n\n// Admin component constants\nexport const ADMIN_CONSTANTS = {\n  SAVE_DEBOUNCE_DELAY: 500,\n  DEFAULT_MAX_SLIDES: 30,\n  EMPTY_SLIDES_COUNT: 0,\n} as const;\n\n// UI styling constants\nexport const UI_STYLES = {\n  HEADER_ICON_HEIGHT: 24,\n  HEADER_ICON_MARGIN_TOP: 8,\n  MOBILE_BUTTON_FONT_SIZE: 14,\n  MOBILE_BUTTON_WORD_SPACING: -1,\n  SOCIAL_ICON_WIDTH: 32,\n  SOCIAL_ICON_MARGIN_LEFT: 20,\n  TAG_TEXT_FONT_SIZE: 14,\n  TAG_CONTAINER_PADDING_TOP: 10,\n  TAG_CONTAINER_MARGIN_TOP: 5,\n} as const;\n\n// Mobile layout constants\nexport const MOBILE_LAYOUT = {\n  SCREEN_WIDTH_MULTIPLIER: 2,\n  SCREEN_WIDTH_OFFSET: 50,\n  CONTAINER_MARGIN_MULTIPLIER: 0.254,\n} as const;\n\n// Slideshow constants\nexport const SLIDESHOW_CONSTANTS = {\n  SLIDE_INCREMENT: 1,\n  INITIAL_SLIDE_INDEX: 1,\n  VALIDATION_ERRORS_EMPTY: 0,\n} as const;\n\n// Array and index constants\nexport const ARRAY_CONSTANTS = {\n  EMPTY_LENGTH: 0,\n  FIRST_INDEX: 0,\n  NOT_FOUND_INDEX: -1,\n  NEXT_ITEM_OFFSET: 1,\n  LAST_ITEM_OFFSET: -1,\n} as const;\n\n// JSON formatting constants\nexport const JSON_CONSTANTS = {\n  INDENT_SIZE: 2,\n} as const;\n\n// Timing constants\nexport const TIMING = {\n  CHECK_INTERVAL: 10,\n  DATA_CHECK_INTERVAL: 100,\n  DEFAULT_TRANSITION_TIME: 5000,\n} as const;\n\n// DOM element constants\nexport const DOM_ELEMENTS = {\n  SWIPER_AD_CONTAINER_ID: 'swiperAdContainer',\n  SWIPER_TAG_CONTAINER_ID: 'swiperTagContainer',\n  SWIPER_TAG_WRAPPER_ID: 'swiperTagWrapper',\n  HEADER_ICON_ID: 'wusong8899Client1HeaderIcon',\n} as const;\n\n// CSS class constants\nexport const CSS_CLASSES = {\n  SWIPER: 'swiper',\n  SWIPER_WRAPPER: 'swiper-wrapper',\n  SWIPER_SLIDE: 'swiper-slide',\n  SWIPER_SLIDE_TAG: 'swiper-slide-tag',\n  SWIPER_SLIDE_TAG_INNER: 'swiper-slide-tag-inner',\n  SWIPER_SLIDE_TAG_INNER_MOBILE: 'swiper-slide-tag-inner-mobile',\n  SWIPER_BUTTON_NEXT: 'swiper-button-next',\n  SWIPER_BUTTON_PREV: 'swiper-button-prev',\n  SWIPER_PAGINATION: 'swiper-pagination',\n  AD_SWIPER: 'adSwiper',\n  TAG_SWIPER: 'tagSwiper',\n  TAG_TILES: 'TagTiles',\n  TAG_TILE: 'TagTile',\n  TAG_TILE_NAME: 'TagTile-name',\n  TAG_TILE_DESCRIPTION: 'TagTile-description',\n  TAG_TEXT_OUTER_CONTAINER: 'TagTextOuterContainer',\n  TAG_TEXT_CONTAINER: 'TagTextContainer',\n  TAG_TEXT_ICON: 'TagTextIcon',\n  BUTTON_REGISTER: 'buttonRegister',\n} as const;\n\n// CSS selector constants\nexport const CSS_SELECTORS = {\n  APP_NAVIGATION_BACK_CONTROL: '#app-navigation .App-backControl',\n  CONTENT_CONTAINER: '#content .container',\n  TAGS_PAGE_CONTENT: '#content .container .TagsPage-content',\n  NEW_DISCUSSION_BUTTON: '.item-newDiscussion .Button-label',\n  NEW_DISCUSSION_ICON: '.item-newDiscussion i',\n  NAV_ITEMS: '.item-nav',\n  APP_CONTENT: '.App-content',\n  SWIPER_PAGINATION_EL: '.swiper-pagination',\n  SWIPER_BUTTON_NEXT_EL: '.swiper-button-next',\n  SWIPER_BUTTON_PREV_EL: '.swiper-button-prev',\n} as const;\n\n// Extension configuration constants\nexport const EXTENSION_CONFIG = {\n  ID: 'wusong8899-client1-header-adv',\n  TRANSLATION_PREFIX: 'wusong8899-client1',\n  MAX_SLIDES: 30,\n  HEADER_ICON_URL: 'https://ex.cc/assets/files/date/test.png',\n} as const;\n\n// Social media platform constants\nexport const SOCIAL_PLATFORMS = [\n  'Kick',\n  'Facebook',\n  'Twitter',\n  'YouTube',\n  'Instagram'\n] as const;\n\nexport type SocialPlatform = typeof SOCIAL_PLATFORMS[number];\n", "import app from 'flarum/admin/app';\nimport Component from 'flarum/common/Component';\nimport Button from 'flarum/common/components/Button';\nimport { ADMIN_CONSTANTS } from '../../common/config/constants';\nimport type {\n  DynamicSlideSettingsComponentAttrs,\n  SlideDataInternal,\n  FlarumVnode\n} from '../../common/config/types';\n\n// Constants\nconst INITIAL_SLIDE_ID = 1; // Initial slide ID\nconst MINIMUM_SLIDES = 1; // Minimum number of slides required\nconst SLIDE_NOT_FOUND = -1; // Return value when slide is not found\nconst SLIDE_INCREMENT = 1; // Increment value for slide operations\n\n/**\n * Dynamic component for managing advertisement slide settings with add/delete functionality\n */\nexport default class DynamicSlideSettingsComponent extends Component<DynamicSlideSettingsComponentAttrs> {\n  private slides: SlideDataInternal[] = [];\n  private loading = false;\n  private nextId = INITIAL_SLIDE_ID;\n  private timeouts: Record<string, NodeJS.Timeout> = {};\n\n  oninit(vnode: FlarumVnode): void {\n    super.oninit(vnode);\n    this.loadExistingSlides();\n  }\n\n  /**\n   * Load existing slides from settings\n   */\n  private loadExistingSlides(): void {\n    const { extensionId, maxSlides = ADMIN_CONSTANTS.DEFAULT_MAX_SLIDES } = this.attrs;\n    const slides: SlideDataInternal[] = [];\n\n    // Load existing slides from settings\n    for (let slideIndex = INITIAL_SLIDE_ID; slideIndex <= maxSlides; slideIndex += SLIDE_INCREMENT) {\n      const linkKey = `${extensionId}.Link${slideIndex}`;\n      const imageKey = `${extensionId}.Image${slideIndex}`;\n      const link = app.data.settings[linkKey] || '';\n      const image = app.data.settings[imageKey] || '';\n\n      // Only include slides that have at least one field filled\n      if (link || image) {\n        slides.push({\n          id: slideIndex,\n          link,\n          image\n        });\n        this.nextId = Math.max(this.nextId, slideIndex + SLIDE_INCREMENT);\n      }\n    }\n\n    this.slides = slides;\n\n    // If no slides exist, add one empty slide to start with\n    if (slides.length === ADMIN_CONSTANTS.EMPTY_SLIDES_COUNT) {\n      this.addSlide();\n    }\n  }\n\n  /**\n   * Add a new slide\n   */\n  private addSlide(): void {\n    const newSlide: SlideDataInternal = {\n      id: this.nextId,\n      link: '',\n      image: ''\n    };\n\n    this.nextId += 1;\n    this.slides.push(newSlide);\n    m.redraw();\n  }\n\n  /**\n   * Remove a slide\n   */\n  private removeSlide(slideId: number): void {\n    const { extensionId } = this.attrs;\n    const slideIndex = this.slides.findIndex(slide => slide.id === slideId);\n    \n    if (slideIndex === SLIDE_NOT_FOUND) {\n      return;\n    }\n    \n    const slide = this.slides[slideIndex];\n    \n    // Remove from backend\n    this.saveSetting(`${extensionId}.Link${slide.id}`, '');\n    this.saveSetting(`${extensionId}.Image${slide.id}`, '');\n    \n    // Remove from local state\n    this.slides.splice(slideIndex, SLIDE_INCREMENT);\n\n    // Ensure at least one slide exists\n    if (this.slides.length === ADMIN_CONSTANTS.EMPTY_SLIDES_COUNT) {\n      this.addSlide();\n    }\n    \n    m.redraw();\n  }\n\n  /**\n   * Update slide data\n   */\n  private updateSlide(slideId: number, field: 'link' | 'image', value: string): void {\n    const { extensionId } = this.attrs;\n    const slide = this.slides.find(slideItem => slideItem.id === slideId);\n\n    if (!slide) {\n      return;\n    }\n\n    slide[field] = value;\n\n    // Save to backend\n    let settingKey = '';\n    if (field === 'link') {\n      settingKey = `${extensionId}.Link${slide.id}`;\n    } else {\n      settingKey = `${extensionId}.Image${slide.id}`;\n    }\n\n    this.saveSetting(settingKey, value);\n  }\n\n  /**\n   * Save setting to backend with debouncing\n   */\n  private saveSetting(key: string, value: string): void {\n    // Clear existing timeout for this key\n    const timeoutKey = `saveTimeout_${key}`;\n    clearTimeout(this.timeouts[timeoutKey]);\n\n    // Set new timeout\n    this.timeouts[timeoutKey] = setTimeout(() => {\n      app.data.settings[key] = value;\n\n      app.request({\n        method: 'POST',\n        url: app.forum.attribute('apiUrl') + '/settings',\n        body: {\n          [key]: value\n        }\n      }).catch(() => {\n        // Handle save error silently for now\n      });\n    }, ADMIN_CONSTANTS.SAVE_DEBOUNCE_DELAY);\n  }\n\n  view(): unknown {\n    return m('div.Form-group', [\n      m('label.FormLabel',\n        app.translator.trans('wusong8899-client1.admin.SlideSettings')\n      ),\n      m('div.helpText',\n        app.translator.trans('wusong8899-client1.admin.SlideSettingsHelp')\n      ),\n\n      m('div.DynamicSlideSettings', [\n        // Slides list\n        this.slides.map((slide, slideIndex) => this.renderSlide(slide, slideIndex)),\n\n        // Add button\n        m('div.DynamicSlideSettings-addButton', [\n          m(Button, {\n            className: 'Button Button--primary',\n            icon: 'fas fa-plus',\n            onclick: () => this.addSlide()\n          }, app.translator.trans('wusong8899-client1.admin.AddSlide'))\n        ])\n      ])\n    ]);\n  }\n\n  /**\n   * Render a single slide\n   */\n  private renderSlide(slide: SlideDataInternal, slideIndex: number): unknown {\n    return m('div.DynamicSlideSettings-slide', {\n      key: slide.id\n    }, [\n      m('div.DynamicSlideSettings-slideHeader', [\n        m('h4', app.translator.trans('wusong8899-client1.admin.SlideNumber', { number: slideIndex + SLIDE_INCREMENT })),\n        m(Button, {\n          className: 'Button Button--danger',\n          icon: 'fas fa-trash',\n          onclick: () => this.removeSlide(slide.id),\n          disabled: this.slides.length === MINIMUM_SLIDES\n        }, app.translator.trans('wusong8899-client1.admin.DeleteSlide'))\n      ]),\n\n      m('div.DynamicSlideSettings-slideFields', [\n        // Link URL field\n        m('div.Form-group', [\n          m('label.FormLabel',\n            app.translator.trans('wusong8899-client1.admin.SlideLink')\n          ),\n          m('input.FormControl', {\n            type: 'url',\n            placeholder: 'https://example.com',\n            value: slide.link,\n            oninput: (event: Event) => {\n              const target = event.target as HTMLInputElement;\n              this.updateSlide(slide.id, 'link', target.value);\n            }\n          })\n        ]),\n\n        // Image URL field\n        m('div.Form-group', [\n          m('label.FormLabel',\n            app.translator.trans('wusong8899-client1.admin.SlideImage')\n          ),\n          m('input.FormControl', {\n            type: 'url',\n            placeholder: 'https://example.com/image.jpg',\n            value: slide.image,\n            oninput: (event: Event) => {\n              const target = event.target as HTMLInputElement;\n              this.updateSlide(slide.id, 'image', target.value);\n            }\n          })\n        ])\n      ])\n    ]);\n  }\n}\n", "import app from 'flarum/admin/app';\nimport DynamicSlideSettingsComponent from './components/dynamic-slide-settings-component';\nimport { ADMIN_CONSTANTS } from '../common/config/constants';\nimport type { ExtensionData } from '../common/config/types';\n\n/**\n * Settings generator utility for admin interface\n */\nexport class SettingsGenerator {\n    private extensionId: string;\n    private extensionData: ExtensionData;\n\n    constructor(extensionId: string) {\n        this.extensionId = extensionId;\n        this.extensionData = app.extensionData.for(extensionId) as ExtensionData;\n    }\n\n    /**\n     * Register transition time setting\n     */\n    registerTransitionTimeSetting(): this {\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.TransitionTime`,\n            type: 'number',\n            label: app.translator.trans('wusong8899-client1.admin.TransitionTime'),\n        });\n        return this;\n    }\n\n    /**\n     * Register header icon URL setting\n     */\n    registerHeaderIconUrlSetting(): this {\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.HeaderIconUrl`,\n            type: 'url',\n            label: app.translator.trans('wusong8899-client1.admin.HeaderIconUrl'),\n            help: app.translator.trans('wusong8899-client1.admin.HeaderIconUrlHelp'),\n        });\n        return this;\n    }\n\n    /**\n     * Register social media settings\n     */\n    registerSocialMediaSettings(): this {\n        const socialPlatforms = ['Kick', 'Facebook', 'Twitter', 'YouTube', 'Instagram'];\n\n        for (const platform of socialPlatforms) {\n            // URL setting\n            this.extensionData.registerSetting({\n                setting: `${this.extensionId}.Social${platform}Url`,\n                type: 'url',\n                label: app.translator.trans(`wusong8899-client1.admin.Social${platform}Url`),\n                help: app.translator.trans(`wusong8899-client1.admin.Social${platform}UrlHelp`),\n            });\n\n            // Icon setting\n            this.extensionData.registerSetting({\n                setting: `${this.extensionId}.Social${platform}Icon`,\n                type: 'text',\n                label: app.translator.trans(`wusong8899-client1.admin.Social${platform}Icon`),\n                help: app.translator.trans(`wusong8899-client1.admin.Social${platform}IconHelp`),\n            });\n        }\n\n        return this;\n    }\n\n    /**\n     * Register dynamic slide settings component\n     * @param maxSlides - Maximum number of slides to configure\n     */\n    registerSlideSettings(maxSlides = ADMIN_CONSTANTS.DEFAULT_MAX_SLIDES): this {\n        this.extensionData.registerSetting(() =>\n            m(DynamicSlideSettingsComponent, {\n                extensionId: this.extensionId,\n                maxSlides: maxSlides\n            })\n        );\n        return this;\n    }\n\n    /**\n     * Register all settings for the extension\n     * @param maxSlides - Maximum number of slides to configure\n     */\n    registerAllSettings(maxSlides = ADMIN_CONSTANTS.DEFAULT_MAX_SLIDES): this {\n        return this\n            .registerTransitionTimeSetting()\n            .registerHeaderIconUrlSetting()\n            .registerSocialMediaSettings()\n            .registerSlideSettings(maxSlides);\n    }\n}\n\n/**\n * Configuration constants\n */\n// Centralized config is in js/src/common/config.\n// Kept for backward compatibility; prefer importing from '../../common/config'.\nexport const EXTENSION_CONFIG = {\n    EXTENSION_ID: 'wusong8899-client1-header-adv',\n    MAX_SLIDES: ADMIN_CONSTANTS.DEFAULT_MAX_SLIDES,\n    DEFAULT_TRANSITION_TIME: 5000,\n};\n\n/**\n * Initialize admin settings\n * @param extensionId - The extension identifier\n * @param maxSlides - Maximum number of slides\n */\nexport const initializeAdminSettings = (\n    extensionId = EXTENSION_CONFIG.EXTENSION_ID,\n    maxSlides = EXTENSION_CONFIG.MAX_SLIDES\n): void => {\n    const generator = new SettingsGenerator(extensionId);\n    generator.registerAllSettings(maxSlides);\n};\n", "import app from 'flarum/admin/app';\r\nimport { initializeAdminSettings } from './settings-generator';\r\n\r\napp.initializers.add('wusong8899/client1-header-adv', (): void => {\r\n    initializeAdminSettings();\r\n});\r\n"], "names": ["ADMIN_CONSTANTS", "INITIAL_SLIDE_ID", "MINIMUM_SLIDES", "SLIDE_NOT_FOUND", "SLIDE_INCREMENT", "DynamicSlideSettingsComponent", "Component", "vnode", "extensionId", "maxSlides", "slides", "slideIndex", "linkKey", "image<PERSON>ey", "link", "app", "image", "newSlide", "slideId", "slide", "field", "value", "slideItem", "<PERSON><PERSON><PERSON>", "key", "timeout<PERSON><PERSON>", "<PERSON><PERSON>", "event", "target", "SettingsGenerator", "socialPlatforms", "platform", "EXTENSION_CONFIG", "initializeAdminSettings"], "mappings": "8BAwCO,MAAMA,EAAkB,CAC7B,oBAAqB,IACrB,mBAAoB,GACpB,mBAAoB,CACtB,ECjCMC,EAAmB,EACnBC,EAAiB,EACjBC,EAAkB,GAClBC,EAAkB,EAKxB,MAAqBC,UAAsCC,CAA8C,CAAzG,aAAA,CAAA,MAAA,GAAA,SAAA,EACE,KAAQ,OAA8B,CAAA,EACtC,KAAQ,QAAU,GAClB,KAAQ,OAASL,EACjB,KAAQ,SAA2C,CAAA,CAAC,CAEpD,OAAOM,EAA0B,CAC/B,MAAM,OAAOA,CAAK,EAClB,KAAK,mBAAA,CACP,CAKQ,oBAA2B,CACjC,KAAM,CAAE,YAAAC,EAAa,UAAAC,EAAYT,EAAgB,kBAAA,EAAuB,KAAK,MACvEU,EAA8B,CAAA,EAGpC,QAASC,EAAaV,EAAkBU,GAAcF,EAAWE,GAAcP,EAAiB,CAC9F,MAAMQ,EAAU,GAAGJ,CAAW,QAAQG,CAAU,GAC1CE,EAAW,GAAGL,CAAW,SAASG,CAAU,GAC5CG,EAAOC,EAAI,KAAK,SAASH,CAAO,GAAK,GACrCI,EAAQD,EAAI,KAAK,SAASF,CAAQ,GAAK,IAGzCC,GAAQE,KACVN,EAAO,KAAK,CACV,GAAIC,EACJ,KAAAG,EACA,MAAAE,CAAA,CACD,EACD,KAAK,OAAS,KAAK,IAAI,KAAK,OAAQL,EAAaP,CAAe,EAEpE,CAEA,KAAK,OAASM,EAGVA,EAAO,SAAWV,EAAgB,oBACpC,KAAK,SAAA,CAET,CAKQ,UAAiB,CACvB,MAAMiB,EAA8B,CAClC,GAAI,KAAK,OACT,KAAM,GACN,MAAO,EAAA,EAGT,KAAK,QAAU,EACf,KAAK,OAAO,KAAKA,CAAQ,EACzB,EAAE,OAAA,CACJ,CAKQ,YAAYC,EAAuB,CACzC,KAAM,CAAE,YAAAV,GAAgB,KAAK,MACvBG,EAAa,KAAK,OAAO,UAAUQ,GAASA,EAAM,KAAOD,CAAO,EAEtE,GAAIP,IAAeR,EACjB,OAGF,MAAMgB,EAAQ,KAAK,OAAOR,CAAU,EAGpC,KAAK,YAAY,GAAGH,CAAW,QAAQW,EAAM,EAAE,GAAI,EAAE,EACrD,KAAK,YAAY,GAAGX,CAAW,SAASW,EAAM,EAAE,GAAI,EAAE,EAGtD,KAAK,OAAO,OAAOR,EAAYP,CAAe,EAG1C,KAAK,OAAO,SAAWJ,EAAgB,oBACzC,KAAK,SAAA,EAGP,EAAE,OAAA,CACJ,CAKQ,YAAYkB,EAAiBE,EAAyBC,EAAqB,CACjF,KAAM,CAAE,YAAAb,GAAgB,KAAK,MACvBW,EAAQ,KAAK,OAAO,KAAKG,GAAaA,EAAU,KAAOJ,CAAO,EAEpE,GAAI,CAACC,EACH,OAGFA,EAAMC,CAAK,EAAIC,EAGf,IAAIE,EAAa,GACbH,IAAU,OACZG,EAAa,GAAGf,CAAW,QAAQW,EAAM,EAAE,GAE3CI,EAAa,GAAGf,CAAW,SAASW,EAAM,EAAE,GAG9C,KAAK,YAAYI,EAAYF,CAAK,CACpC,CAKQ,YAAYG,EAAaH,EAAqB,CAEpD,MAAMI,EAAa,eAAeD,CAAG,GACrC,aAAa,KAAK,SAASC,CAAU,CAAC,EAGtC,KAAK,SAASA,CAAU,EAAI,WAAW,IAAM,CAC3CV,EAAI,KAAK,SAASS,CAAG,EAAIH,EAEzBN,EAAI,QAAQ,CACV,OAAQ,OACR,IAAKA,EAAI,MAAM,UAAU,QAAQ,EAAI,YACrC,KAAM,CACJ,CAACS,CAAG,EAAGH,CAAA,CACT,CACD,EAAE,MAAM,IAAM,CAEf,CAAC,CACH,EAAGrB,EAAgB,mBAAmB,CACxC,CAEA,MAAgB,CACd,OAAO,EAAE,iBAAkB,CACzB,EAAE,kBACAe,EAAI,WAAW,MAAM,wCAAwC,CAAA,EAE/D,EAAE,eACAA,EAAI,WAAW,MAAM,4CAA4C,CAAA,EAGnE,EAAE,2BAA4B,CAE5B,KAAK,OAAO,IAAI,CAACI,EAAOR,IAAe,KAAK,YAAYQ,EAAOR,CAAU,CAAC,EAG1E,EAAE,qCAAsC,CACtC,EAAEe,EAAQ,CACR,UAAW,yBACX,KAAM,cACN,QAAS,IAAM,KAAK,SAAA,CAAS,EAC5BX,EAAI,WAAW,MAAM,mCAAmC,CAAC,CAAA,CAC7D,CAAA,CACF,CAAA,CACF,CACH,CAKQ,YAAYI,EAA0BR,EAA6B,CACzE,OAAO,EAAE,iCAAkC,CACzC,IAAKQ,EAAM,EAAA,EACV,CACD,EAAE,uCAAwC,CACxC,EAAE,KAAMJ,EAAI,WAAW,MAAM,uCAAwC,CAAE,OAAQJ,EAAaP,CAAA,CAAiB,CAAC,EAC9G,EAAEsB,EAAQ,CACR,UAAW,wBACX,KAAM,eACN,QAAS,IAAM,KAAK,YAAYP,EAAM,EAAE,EACxC,SAAU,KAAK,OAAO,SAAWjB,CAAA,EAChCa,EAAI,WAAW,MAAM,sCAAsC,CAAC,CAAA,CAChE,EAED,EAAE,uCAAwC,CAExC,EAAE,iBAAkB,CAClB,EAAE,kBACAA,EAAI,WAAW,MAAM,oCAAoC,CAAA,EAE3D,EAAE,oBAAqB,CACrB,KAAM,MACN,YAAa,sBACb,MAAOI,EAAM,KACb,QAAUQ,GAAiB,CACzB,MAAMC,EAASD,EAAM,OACrB,KAAK,YAAYR,EAAM,GAAI,OAAQS,EAAO,KAAK,CACjD,CAAA,CACD,CAAA,CACF,EAGD,EAAE,iBAAkB,CAClB,EAAE,kBACAb,EAAI,WAAW,MAAM,qCAAqC,CAAA,EAE5D,EAAE,oBAAqB,CACrB,KAAM,MACN,YAAa,gCACb,MAAOI,EAAM,MACb,QAAUQ,GAAiB,CACzB,MAAMC,EAASD,EAAM,OACrB,KAAK,YAAYR,EAAM,GAAI,QAASS,EAAO,KAAK,CAClD,CAAA,CACD,CAAA,CACF,CAAA,CACF,CAAA,CACF,CACH,CACF,CC/NO,MAAMC,CAAkB,CAI3B,YAAYrB,EAAqB,CAC7B,KAAK,YAAcA,EACnB,KAAK,cAAgBO,EAAI,cAAc,IAAIP,CAAW,CAC1D,CAKA,+BAAsC,CAClC,YAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,kBAC5B,KAAM,SACN,MAAOO,EAAI,WAAW,MAAM,yCAAyC,CAAA,CACxE,EACM,IACX,CAKA,8BAAqC,CACjC,YAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,iBAC5B,KAAM,MACN,MAAOA,EAAI,WAAW,MAAM,wCAAwC,EACpE,KAAMA,EAAI,WAAW,MAAM,4CAA4C,CAAA,CAC1E,EACM,IACX,CAKA,6BAAoC,CAChC,MAAMe,EAAkB,CAAC,OAAQ,WAAY,UAAW,UAAW,WAAW,EAE9E,UAAWC,KAAYD,EAEnB,KAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,UAAUC,CAAQ,MAC9C,KAAM,MACN,MAAOhB,EAAI,WAAW,MAAM,kCAAkCgB,CAAQ,KAAK,EAC3E,KAAMhB,EAAI,WAAW,MAAM,kCAAkCgB,CAAQ,SAAS,CAAA,CACjF,EAGD,KAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,UAAUA,CAAQ,OAC9C,KAAM,OACN,MAAOhB,EAAI,WAAW,MAAM,kCAAkCgB,CAAQ,MAAM,EAC5E,KAAMhB,EAAI,WAAW,MAAM,kCAAkCgB,CAAQ,UAAU,CAAA,CAClF,EAGL,OAAO,IACX,CAMA,sBAAsBtB,EAAYT,EAAgB,mBAA0B,CACxE,YAAK,cAAc,gBAAgB,IAC/B,EAAEK,EAA+B,CAC7B,YAAa,KAAK,YAClB,UAAAI,CAAA,CACH,CAAA,EAEE,IACX,CAMA,oBAAoBA,EAAYT,EAAgB,mBAA0B,CACtE,OAAO,KACF,gCACA,6BAAA,EACA,4BAAA,EACA,sBAAsBS,CAAS,CACxC,CACJ,CAOO,MAAMuB,EAAmB,CAC5B,aAAc,gCACd,WAAYhC,EAAgB,kBAEhC,EAOaiC,EAA0B,CACnCzB,EAAcwB,EAAiB,aAC/BvB,EAAYuB,EAAiB,aACtB,CACW,IAAIH,EAAkBrB,CAAW,EACzC,oBAAoBC,CAAS,CAC3C,ECnHAM,EAAI,aAAa,IAAI,gCAAiC,IAAY,CAC9DkB,EAAA,CACJ,CAAC"}