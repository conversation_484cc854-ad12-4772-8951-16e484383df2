{"version": 3, "file": "forum.js", "sources": ["../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/ssr-window.esm.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/utils.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/swiper-core.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/create-element-if-not-defined.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/modules/navigation.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/classes-to-selector.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/modules/pagination.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/modules/autoplay.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/effect-init.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/effect-target.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/create-shadow.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/modules/effect-coverflow.mjs", "../src/common/config/constants.ts", "../src/forum/utils/dom-utils.ts", "../src/forum/utils/mobile-detection.ts", "../src/common/config/defaults.ts", "../src/forum/components/slideshow-manager.ts", "../src/forum/components/ui-manager.ts", "../src/forum/utils/config-manager.ts", "../src/forum/utils/error-handler.ts", "../src/forum/index.ts"], "sourcesContent": ["/**\n * SSR Window 5.0.1\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2025, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: June 27, 2025\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n  return obj !== null && typeof obj === 'object' && 'constructor' in obj && obj.constructor === Object;\n}\nfunction extend(target, src) {\n  if (target === void 0) {\n    target = {};\n  }\n  if (src === void 0) {\n    src = {};\n  }\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  Object.keys(src).filter(key => noExtend.indexOf(key) < 0).forEach(key => {\n    if (typeof target[key] === 'undefined') target[key] = src[key];else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n      extend(target[key], src[key]);\n    }\n  });\n}\nconst ssrDocument = {\n  body: {},\n  addEventListener() {},\n  removeEventListener() {},\n  activeElement: {\n    blur() {},\n    nodeName: ''\n  },\n  querySelector() {\n    return null;\n  },\n  querySelectorAll() {\n    return [];\n  },\n  getElementById() {\n    return null;\n  },\n  createEvent() {\n    return {\n      initEvent() {}\n    };\n  },\n  createElement() {\n    return {\n      children: [],\n      childNodes: [],\n      style: {},\n      setAttribute() {},\n      getElementsByTagName() {\n        return [];\n      }\n    };\n  },\n  createElementNS() {\n    return {};\n  },\n  importNode() {\n    return null;\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  }\n};\nfunction getDocument() {\n  const doc = typeof document !== 'undefined' ? document : {};\n  extend(doc, ssrDocument);\n  return doc;\n}\nconst ssrWindow = {\n  document: ssrDocument,\n  navigator: {\n    userAgent: ''\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  },\n  history: {\n    replaceState() {},\n    pushState() {},\n    go() {},\n    back() {}\n  },\n  CustomEvent: function CustomEvent() {\n    return this;\n  },\n  addEventListener() {},\n  removeEventListener() {},\n  getComputedStyle() {\n    return {\n      getPropertyValue() {\n        return '';\n      }\n    };\n  },\n  Image() {},\n  Date() {},\n  screen: {},\n  setTimeout() {},\n  clearTimeout() {},\n  matchMedia() {\n    return {};\n  },\n  requestAnimationFrame(callback) {\n    if (typeof setTimeout === 'undefined') {\n      callback();\n      return null;\n    }\n    return setTimeout(callback, 0);\n  },\n  cancelAnimationFrame(id) {\n    if (typeof setTimeout === 'undefined') {\n      return;\n    }\n    clearTimeout(id);\n  }\n};\nfunction getWindow() {\n  const win = typeof window !== 'undefined' ? window : {};\n  extend(win, ssrWindow);\n  return win;\n}\n\nexport { getWindow as a, getDocument as g };\n", "import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\n\nfunction classesToTokens(classes) {\n  if (classes === void 0) {\n    classes = '';\n  }\n  return classes.trim().split(' ').filter(c => !!c.trim());\n}\n\nfunction deleteProps(obj) {\n  const object = obj;\n  Object.keys(object).forEach(key => {\n    try {\n      object[key] = null;\n    } catch (e) {\n      // no getter for object\n    }\n    try {\n      delete object[key];\n    } catch (e) {\n      // something got wrong\n    }\n  });\n}\nfunction nextTick(callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  return setTimeout(callback, delay);\n}\nfunction now() {\n  return Date.now();\n}\nfunction getComputedStyle(el) {\n  const window = getWindow();\n  let style;\n  if (window.getComputedStyle) {\n    style = window.getComputedStyle(el, null);\n  }\n  if (!style && el.currentStyle) {\n    style = el.currentStyle;\n  }\n  if (!style) {\n    style = el.style;\n  }\n  return style;\n}\nfunction getTranslate(el, axis) {\n  if (axis === void 0) {\n    axis = 'x';\n  }\n  const window = getWindow();\n  let matrix;\n  let curTransform;\n  let transformMatrix;\n  const curStyle = getComputedStyle(el);\n  if (window.WebKitCSSMatrix) {\n    curTransform = curStyle.transform || curStyle.webkitTransform;\n    if (curTransform.split(',').length > 6) {\n      curTransform = curTransform.split(', ').map(a => a.replace(',', '.')).join(', ');\n    }\n    // Some old versions of Webkit choke when 'none' is passed; pass\n    // empty string instead in this case\n    transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n  } else {\n    transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n    matrix = transformMatrix.toString().split(',');\n  }\n  if (axis === 'x') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m41;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[12]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[4]);\n  }\n  if (axis === 'y') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m42;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[13]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[5]);\n  }\n  return curTransform || 0;\n}\nfunction isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object';\n}\nfunction isNode(node) {\n  // eslint-disable-next-line\n  if (typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined') {\n    return node instanceof HTMLElement;\n  }\n  return node && (node.nodeType === 1 || node.nodeType === 11);\n}\nfunction extend() {\n  const to = Object(arguments.length <= 0 ? undefined : arguments[0]);\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  for (let i = 1; i < arguments.length; i += 1) {\n    const nextSource = i < 0 || arguments.length <= i ? undefined : arguments[i];\n    if (nextSource !== undefined && nextSource !== null && !isNode(nextSource)) {\n      const keysArray = Object.keys(Object(nextSource)).filter(key => noExtend.indexOf(key) < 0);\n      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n        const nextKey = keysArray[nextIndex];\n        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n        if (desc !== undefined && desc.enumerable) {\n          if (isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else if (!isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            to[nextKey] = {};\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else {\n            to[nextKey] = nextSource[nextKey];\n          }\n        }\n      }\n    }\n  }\n  return to;\n}\nfunction setCSSProperty(el, varName, varValue) {\n  el.style.setProperty(varName, varValue);\n}\nfunction animateCSSModeScroll(_ref) {\n  let {\n    swiper,\n    targetPosition,\n    side\n  } = _ref;\n  const window = getWindow();\n  const startPosition = -swiper.translate;\n  let startTime = null;\n  let time;\n  const duration = swiper.params.speed;\n  swiper.wrapperEl.style.scrollSnapType = 'none';\n  window.cancelAnimationFrame(swiper.cssModeFrameID);\n  const dir = targetPosition > startPosition ? 'next' : 'prev';\n  const isOutOfBound = (current, target) => {\n    return dir === 'next' && current >= target || dir === 'prev' && current <= target;\n  };\n  const animate = () => {\n    time = new Date().getTime();\n    if (startTime === null) {\n      startTime = time;\n    }\n    const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n    const easeProgress = 0.5 - Math.cos(progress * Math.PI) / 2;\n    let currentPosition = startPosition + easeProgress * (targetPosition - startPosition);\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      currentPosition = targetPosition;\n    }\n    swiper.wrapperEl.scrollTo({\n      [side]: currentPosition\n    });\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.scrollSnapType = '';\n      setTimeout(() => {\n        swiper.wrapperEl.style.overflow = '';\n        swiper.wrapperEl.scrollTo({\n          [side]: currentPosition\n        });\n      });\n      window.cancelAnimationFrame(swiper.cssModeFrameID);\n      return;\n    }\n    swiper.cssModeFrameID = window.requestAnimationFrame(animate);\n  };\n  animate();\n}\nfunction getSlideTransformEl(slideEl) {\n  return slideEl.querySelector('.swiper-slide-transform') || slideEl.shadowRoot && slideEl.shadowRoot.querySelector('.swiper-slide-transform') || slideEl;\n}\nfunction elementChildren(element, selector) {\n  if (selector === void 0) {\n    selector = '';\n  }\n  const window = getWindow();\n  const children = [...element.children];\n  if (window.HTMLSlotElement && element instanceof HTMLSlotElement) {\n    children.push(...element.assignedElements());\n  }\n  if (!selector) {\n    return children;\n  }\n  return children.filter(el => el.matches(selector));\n}\nfunction elementIsChildOfSlot(el, slot) {\n  // Breadth-first search through all parent's children and assigned elements\n  const elementsQueue = [slot];\n  while (elementsQueue.length > 0) {\n    const elementToCheck = elementsQueue.shift();\n    if (el === elementToCheck) {\n      return true;\n    }\n    elementsQueue.push(...elementToCheck.children, ...(elementToCheck.shadowRoot ? elementToCheck.shadowRoot.children : []), ...(elementToCheck.assignedElements ? elementToCheck.assignedElements() : []));\n  }\n}\nfunction elementIsChildOf(el, parent) {\n  const window = getWindow();\n  let isChild = parent.contains(el);\n  if (!isChild && window.HTMLSlotElement && parent instanceof HTMLSlotElement) {\n    const children = [...parent.assignedElements()];\n    isChild = children.includes(el);\n    if (!isChild) {\n      isChild = elementIsChildOfSlot(el, parent);\n    }\n  }\n  return isChild;\n}\nfunction showWarning(text) {\n  try {\n    console.warn(text);\n    return;\n  } catch (err) {\n    // err\n  }\n}\nfunction createElement(tag, classes) {\n  if (classes === void 0) {\n    classes = [];\n  }\n  const el = document.createElement(tag);\n  el.classList.add(...(Array.isArray(classes) ? classes : classesToTokens(classes)));\n  return el;\n}\nfunction elementOffset(el) {\n  const window = getWindow();\n  const document = getDocument();\n  const box = el.getBoundingClientRect();\n  const body = document.body;\n  const clientTop = el.clientTop || body.clientTop || 0;\n  const clientLeft = el.clientLeft || body.clientLeft || 0;\n  const scrollTop = el === window ? window.scrollY : el.scrollTop;\n  const scrollLeft = el === window ? window.scrollX : el.scrollLeft;\n  return {\n    top: box.top + scrollTop - clientTop,\n    left: box.left + scrollLeft - clientLeft\n  };\n}\nfunction elementPrevAll(el, selector) {\n  const prevEls = [];\n  while (el.previousElementSibling) {\n    const prev = el.previousElementSibling; // eslint-disable-line\n    if (selector) {\n      if (prev.matches(selector)) prevEls.push(prev);\n    } else prevEls.push(prev);\n    el = prev;\n  }\n  return prevEls;\n}\nfunction elementNextAll(el, selector) {\n  const nextEls = [];\n  while (el.nextElementSibling) {\n    const next = el.nextElementSibling; // eslint-disable-line\n    if (selector) {\n      if (next.matches(selector)) nextEls.push(next);\n    } else nextEls.push(next);\n    el = next;\n  }\n  return nextEls;\n}\nfunction elementStyle(el, prop) {\n  const window = getWindow();\n  return window.getComputedStyle(el, null).getPropertyValue(prop);\n}\nfunction elementIndex(el) {\n  let child = el;\n  let i;\n  if (child) {\n    i = 0;\n    // eslint-disable-next-line\n    while ((child = child.previousSibling) !== null) {\n      if (child.nodeType === 1) i += 1;\n    }\n    return i;\n  }\n  return undefined;\n}\nfunction elementParents(el, selector) {\n  const parents = []; // eslint-disable-line\n  let parent = el.parentElement; // eslint-disable-line\n  while (parent) {\n    if (selector) {\n      if (parent.matches(selector)) parents.push(parent);\n    } else {\n      parents.push(parent);\n    }\n    parent = parent.parentElement;\n  }\n  return parents;\n}\nfunction elementTransitionEnd(el, callback) {\n  function fireCallBack(e) {\n    if (e.target !== el) return;\n    callback.call(el, e);\n    el.removeEventListener('transitionend', fireCallBack);\n  }\n  if (callback) {\n    el.addEventListener('transitionend', fireCallBack);\n  }\n}\nfunction elementOuterSize(el, size, includeMargins) {\n  const window = getWindow();\n  if (includeMargins) {\n    return el[size === 'width' ? 'offsetWidth' : 'offsetHeight'] + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-right' : 'margin-top')) + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-left' : 'margin-bottom'));\n  }\n  return el.offsetWidth;\n}\nfunction makeElementsArray(el) {\n  return (Array.isArray(el) ? el : [el]).filter(e => !!e);\n}\nfunction getRotateFix(swiper) {\n  return v => {\n    if (Math.abs(v) > 0 && swiper.browser && swiper.browser.need3dFix && Math.abs(v) % 90 === 0) {\n      return v + 0.001;\n    }\n    return v;\n  };\n}\nfunction setInnerHTML(el, html) {\n  if (html === void 0) {\n    html = '';\n  }\n  if (typeof trustedTypes !== 'undefined') {\n    el.innerHTML = trustedTypes.createPolicy('html', {\n      createHTML: s => s\n    }).createHTML(html);\n  } else {\n    el.innerHTML = html;\n  }\n}\n\nexport { setCSSProperty as a, elementParents as b, createElement as c, elementOffset as d, elementChildren as e, now as f, getSlideTransformEl as g, elementOuterSize as h, elementIndex as i, classesToTokens as j, getTranslate as k, elementTransitionEnd as l, makeElementsArray as m, nextTick as n, isObject as o, getRotateFix as p, elementStyle as q, elementNextAll as r, setInnerHTML as s, elementPrevAll as t, animateCSSModeScroll as u, showWarning as v, elementIsChildOf as w, extend as x, deleteProps as y };\n", "import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\nimport { b as elementParents, q as elementStyle, e as elementChildren, a as setCSSProperty, h as elementOuterSize, r as elementNextAll, t as elementPrevAll, k as getTranslate, u as animateCSSModeScroll, n as nextTick, v as showWarning, c as createElement, w as elementIsChildOf, f as now, x as extend, i as elementIndex, y as deleteProps } from './utils.mjs';\n\nlet support;\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && document.documentElement.style && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch)\n  };\n}\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\n\nlet deviceCached;\nfunction calcDevice(_temp) {\n  let {\n    userAgent\n  } = _temp === void 0 ? {} : _temp;\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\nfunction getDevice(overrides) {\n  if (overrides === void 0) {\n    overrides = {};\n  }\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\n\nlet browser;\nfunction calcBrowser() {\n  const window = getWindow();\n  const device = getDevice();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua.split('Version/')[1].split(' ')[0].split('.').map(num => Number(num));\n      needPerspectiveFix = major < 16 || major === 16 && minor < 2;\n    }\n  }\n  const isWebView = /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent);\n  const isSafariBrowser = isSafari();\n  const need3dFix = isSafariBrowser || isWebView && device.ios;\n  return {\n    isSafari: needPerspectiveFix || isSafariBrowser,\n    needPerspectiveFix,\n    need3dFix,\n    isWebView\n  };\n}\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\n\nfunction Resize(_ref) {\n  let {\n    swiper,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(_ref2 => {\n          let {\n            contentBoxSize,\n            contentRect,\n            target\n          } = _ref2;\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}\n\nfunction Observer(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const observers = [];\n  const window = getWindow();\n  const attach = function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: swiper.isElement || (typeof options.childList === 'undefined' ? true : options).childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.hostEl);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.hostEl, {\n      childList: swiper.params.observeSlideChildren\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, {\n      attributes: false\n    });\n  };\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}\n\n/* eslint-disable no-underscore-dangle */\n\nvar eventsEmitter = {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler() {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n  emit() {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n};\n\nfunction updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt(elementStyle(el, 'padding-left') || 0, 10) - parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height = height - parseInt(elementStyle(el, 'padding-top') || 0, 10) - parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}\n\nfunction updateSlides() {\n  const swiper = this;\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(swiper.getDirectionLabel(label)) || 0);\n  }\n  const params = swiper.params;\n  const {\n    wrapperEl,\n    slidesEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach(slideEl => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slides);\n  } else if (swiper.grid) {\n    swiper.grid.unsetSlides();\n  }\n\n  // Calc slides\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slides);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[swiper.getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? elementOuterSize(slide, 'width', true) : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide;\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n      if (slides[i]) {\n        slides[i].style[swiper.getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[swiper.getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil((swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup);\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n  if (spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : swiper.getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode || params.loop) return true;\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n      return true;\n    }).forEach(slideEl => {\n      slideEl.style[key] = `${spaceBetween}px`;\n    });\n  }\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const maxSnap = allSlidesSize > swiperSize ? allSlidesSize - swiperSize : 0;\n    snapGrid = snapGrid.map(snap => {\n      if (snap <= 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const offsetSize = (params.slidesOffsetBefore || 0) + (params.slidesOffsetAfter || 0);\n    if (allSlidesSize + offsetSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize - offsetSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`);\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  swiper.emit('slidesUpdated');\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}\n\nfunction updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.slides[swiper.getSlideIndexByData(index)];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}\n\nfunction updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement ? swiper.isHorizontal() ? swiper.wrapperEl.offsetLeft : swiper.wrapperEl.offsetTop : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset - swiper.cssOverflowAdjustment();\n  }\n}\n\nconst toggleSlideClasses$1 = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesProgress(translate) {\n  if (translate === void 0) {\n    translate = this && this.translate || 0;\n  }\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n  let spaceBetween = params.spaceBetween;\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isFullyVisible = slideBefore >= 0 && slideBefore <= swiper.size - swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n    }\n    toggleSlideClasses$1(slide, isVisible, params.slideVisibleClass);\n    toggleSlideClasses$1(slide, isFullyVisible, params.slideFullyVisibleClass);\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}\n\nfunction updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd,\n    progressLoop\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n  swiper.emit('progress', progress);\n}\n\nconst toggleSlideClasses = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    slidesEl,\n    activeIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const getFilteredSlide = selector => {\n    return elementChildren(slidesEl, `.${params.slideClass}${selector}, swiper-slide${selector}`)[0];\n  };\n  let activeSlide;\n  let prevSlide;\n  let nextSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    if (gridEnabled) {\n      activeSlide = slides.find(slideEl => slideEl.column === activeIndex);\n      nextSlide = slides.find(slideEl => slideEl.column === activeIndex + 1);\n      prevSlide = slides.find(slideEl => slideEl.column === activeIndex - 1);\n    } else {\n      activeSlide = slides[activeIndex];\n    }\n  }\n  if (activeSlide) {\n    if (!gridEnabled) {\n      // Next Slide\n      nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !nextSlide) {\n        nextSlide = slides[0];\n      }\n\n      // Prev Slide\n      prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !prevSlide === 0) {\n        prevSlide = slides[slides.length - 1];\n      }\n    }\n  }\n  slides.forEach(slideEl => {\n    toggleSlideClasses(slideEl, slideEl === activeSlide, params.slideActiveClass);\n    toggleSlideClasses(slideEl, slideEl === nextSlide, params.slideNextClass);\n    toggleSlideClasses(slideEl, slideEl === prevSlide, params.slidePrevClass);\n  });\n  swiper.emitSlidesClasses();\n}\n\nconst processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    let lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (!lazyEl && swiper.isElement) {\n      if (slideEl.shadowRoot) {\n        lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n      } else {\n        // init later\n        requestAnimationFrame(() => {\n          if (slideEl.shadowRoot) {\n            lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n            if (lazyEl) lazyEl.remove();\n          }\n        });\n      }\n    }\n    if (lazyEl) lazyEl.remove();\n  }\n};\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\nconst preload = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView = swiper.params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  if (swiper.params.grid && swiper.params.grid.rows > 1) {\n    const activeColumn = activeIndex;\n    const preloadColumns = [activeColumn - amount];\n    preloadColumns.push(...Array.from({\n      length: amount\n    }).map((_, i) => {\n      return activeColumn + slidesPerView + i;\n    }));\n    swiper.slides.forEach((slideEl, i) => {\n      if (preloadColumns.includes(slideEl.column)) unlazy(swiper, i);\n    });\n    return;\n  }\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind || swiper.params.loop) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = (i % len + len) % len;\n      if (realIndex < activeIndex || realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (let i = Math.max(activeIndex - amount, 0); i <= Math.min(slideIndexLastInView + amount, len - 1); i += 1) {\n      if (i !== activeIndex && (i > slideIndexLastInView || i < activeIndex)) {\n        unlazy(swiper, i);\n      }\n    }\n  }\n};\n\nfunction getActiveIndexByTranslate(swiper) {\n  const {\n    slidesGrid,\n    params\n  } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nfunction updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  const getVirtualRealIndex = aIndex => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex && !swiper.params.loop) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    return;\n  }\n  if (activeIndex === previousIndex && swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n    swiper.realIndex = getVirtualRealIndex(activeIndex);\n    return;\n  }\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (gridEnabled) {\n    const firstSlideInColumn = swiper.slides.find(slideEl => slideEl.column === activeIndex);\n    let activeSlideIndex = parseInt(firstSlideInColumn.getAttribute('data-swiper-slide-index'), 10);\n    if (Number.isNaN(activeSlideIndex)) {\n      activeSlideIndex = Math.max(swiper.slides.indexOf(firstSlideInColumn), 0);\n    }\n    realIndex = Math.floor(activeSlideIndex / params.grid.rows);\n  } else if (swiper.slides[activeIndex]) {\n    const slideIndex = swiper.slides[activeIndex].getAttribute('data-swiper-slide-index');\n    if (slideIndex) {\n      realIndex = parseInt(slideIndex, 10);\n    } else {\n      realIndex = activeIndex;\n    }\n  } else {\n    realIndex = activeIndex;\n  }\n  Object.assign(swiper, {\n    previousSnapIndex,\n    snapIndex,\n    previousRealIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    swiper.emit('slideChange');\n  }\n}\n\nfunction updateClickedSlide(el, path) {\n  const swiper = this;\n  const params = swiper.params;\n  let slide = el.closest(`.${params.slideClass}, swiper-slide`);\n  if (!slide && swiper.isElement && path && path.length > 1 && path.includes(el)) {\n    [...path.slice(path.indexOf(el) + 1, path.length)].forEach(pathEl => {\n      if (!slide && pathEl.matches && pathEl.matches(`.${params.slideClass}, swiper-slide`)) {\n        slide = pathEl;\n      }\n    });\n  }\n  let slideFound = false;\n  let slideIndex;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}\n\nvar update = {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide\n};\n\nfunction getSwiperTranslate(axis) {\n  if (axis === void 0) {\n    axis = this.isHorizontal() ? 'x' : 'y';\n  }\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    wrapperEl\n  } = swiper;\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  currentTranslate += swiper.cssOverflowAdjustment();\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}\n\nfunction setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    if (swiper.isHorizontal()) {\n      x -= swiper.cssOverflowAdjustment();\n    } else {\n      y -= swiper.cssOverflowAdjustment();\n    }\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, byController);\n}\n\nfunction minTranslate() {\n  return -this.snapGrid[0];\n}\n\nfunction maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}\n\nfunction translateTo(translate, speed, runCallbacks, translateBounds, internal) {\n  if (translate === void 0) {\n    translate = 0;\n  }\n  if (speed === void 0) {\n    speed = this.params.speed;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (translateBounds === void 0) {\n    translateBounds = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          swiper.animating = false;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n  return true;\n}\n\nvar translate = {\n  getTranslate: getSwiperTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};\n\nfunction setTransition(duration, byController) {\n  const swiper = this;\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n    swiper.wrapperEl.style.transitionDelay = duration === 0 ? `0ms` : '';\n  }\n  swiper.emit('setTransition', duration, byController);\n}\n\nfunction transitionEmit(_ref) {\n  let {\n    swiper,\n    runCallbacks,\n    direction,\n    step\n  } = _ref;\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n  swiper.emit(`transition${step}`);\n  if (runCallbacks && dir === 'reset') {\n    swiper.emit(`slideResetTransition${step}`);\n  } else if (runCallbacks && activeIndex !== previousIndex) {\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}\n\nfunction transitionStart(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}\n\nfunction transitionEnd(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}\n\nvar transition = {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};\n\nfunction slideTo(index, speed, runCallbacks, internal, initial) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n  if (!enabled && !internal && !initial || swiper.destroyed || swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && (rtl ? translate > swiper.translate && translate > swiper.minTranslate() : translate < swiper.translate && translate < swiper.minTranslate())) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset';\n\n  // initial virtual\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  const isInitialVirtual = isVirtual && initial;\n  // Update Index\n  if (!isInitialVirtual && (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate)) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  const browser = getBrowser();\n  const isSafari = browser.isSafari;\n  if (isVirtual && !initial && isSafari && swiper.isElement) {\n    swiper.virtual.update(false, false, slideIndex);\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n  return true;\n}\n\nfunction slideToLoop(index, speed, runCallbacks, internal) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n    index = indexAsNumber;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const gridEnabled = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      let targetSlideIndex;\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        targetSlideIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        targetSlideIndex = swiper.getSlideIndexByData(newIndex);\n      }\n      const cols = gridEnabled ? Math.ceil(swiper.slides.length / swiper.params.grid.rows) : swiper.slides.length;\n      const {\n        centeredSlides\n      } = swiper.params;\n      let slidesPerView = swiper.params.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = swiper.slidesPerViewDynamic();\n      } else {\n        slidesPerView = Math.ceil(parseFloat(swiper.params.slidesPerView, 10));\n        if (centeredSlides && slidesPerView % 2 === 0) {\n          slidesPerView = slidesPerView + 1;\n        }\n      }\n      let needLoopFix = cols - targetSlideIndex < slidesPerView;\n      if (centeredSlides) {\n        needLoopFix = needLoopFix || targetSlideIndex < Math.ceil(slidesPerView / 2);\n      }\n      if (internal && centeredSlides && swiper.params.slidesPerView !== 'auto' && !gridEnabled) {\n        needLoopFix = false;\n      }\n      if (needLoopFix) {\n        const direction = centeredSlides ? targetSlideIndex < swiper.activeIndex ? 'prev' : 'next' : targetSlideIndex - swiper.activeIndex - 1 < swiper.params.slidesPerView ? 'next' : 'prev';\n        swiper.loopFix({\n          direction,\n          slideTo: true,\n          activeSlideIndex: direction === 'next' ? targetSlideIndex + 1 : targetSlideIndex - cols + 1,\n          slideRealIndex: direction === 'next' ? swiper.realIndex : undefined\n        });\n      }\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        newIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        newIndex = swiper.getSlideIndexByData(newIndex);\n      }\n    }\n  }\n  requestAnimationFrame(() => {\n    swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  });\n  return swiper;\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideNext(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    enabled,\n    params,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'next'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n    if (swiper.activeIndex === swiper.slides.length - 1 && params.cssMode) {\n      requestAnimationFrame(() => {\n        swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n      });\n      return true;\n    }\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slidePrev(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'prev'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  const isFreeMode = params.freeMode && params.freeMode.enabled;\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && (params.cssMode || isFreeMode)) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = isFreeMode ? snapGrid[prevSnapIndex] : snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  } else if (params.loop && swiper.activeIndex === 0 && params.cssMode) {\n    requestAnimationFrame(() => {\n      swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n    });\n    return true;\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideReset(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideToClosest(speed, runCallbacks, internal, threshold) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (threshold === void 0) {\n    threshold = 0.5;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}\n\nfunction slideToClickedSlide() {\n  const swiper = this;\n  if (swiper.destroyed) return;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.getSlideIndexWhenGrid(swiper.clickedIndex);\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  const isGrid = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      swiper.slideToLoop(realIndex);\n    } else if (slideToIndex > (isGrid ? (swiper.slides.length - slidesPerView) / 2 - (swiper.params.grid.rows - 1) : swiper.slides.length - slidesPerView)) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}\n\nvar slide = {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide\n};\n\nfunction loopCreate(slideRealIndex, initial) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  const initSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    slides.forEach((el, index) => {\n      el.setAttribute('data-swiper-slide-index', index);\n    });\n  };\n  const clearBlankSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideBlankClass}`);\n    slides.forEach(el => {\n      el.remove();\n    });\n    if (slides.length > 0) {\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    }\n  };\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (params.loopAddBlankSlides && (params.slidesPerGroup > 1 || gridEnabled)) {\n    clearBlankSlides();\n  }\n  const slidesPerGroup = params.slidesPerGroup * (gridEnabled ? params.grid.rows : 1);\n  const shouldFillGroup = swiper.slides.length % slidesPerGroup !== 0;\n  const shouldFillGrid = gridEnabled && swiper.slides.length % params.grid.rows !== 0;\n  const addBlankSlides = amountOfSlides => {\n    for (let i = 0; i < amountOfSlides; i += 1) {\n      const slideEl = swiper.isElement ? createElement('swiper-slide', [params.slideBlankClass]) : createElement('div', [params.slideClass, params.slideBlankClass]);\n      swiper.slidesEl.append(slideEl);\n    }\n  };\n  if (shouldFillGroup) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = slidesPerGroup - swiper.slides.length % slidesPerGroup;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else if (shouldFillGrid) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = params.grid.rows - swiper.slides.length % params.grid.rows;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else {\n    initSlides();\n  }\n  swiper.loopFix({\n    slideRealIndex,\n    direction: params.centeredSlides ? undefined : 'next',\n    initial\n  });\n}\n\nfunction loopFix(_temp) {\n  let {\n    slideRealIndex,\n    slideTo = true,\n    direction,\n    setTranslate,\n    activeSlideIndex,\n    initial,\n    byController,\n    byMousewheel\n  } = _temp === void 0 ? {} : _temp;\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n  const {\n    slides,\n    allowSlidePrev,\n    allowSlideNext,\n    slidesEl,\n    params\n  } = swiper;\n  const {\n    centeredSlides,\n    initialSlide\n  } = params;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n  let slidesPerView = params.slidesPerView;\n  if (slidesPerView === 'auto') {\n    slidesPerView = swiper.slidesPerViewDynamic();\n  } else {\n    slidesPerView = Math.ceil(parseFloat(params.slidesPerView, 10));\n    if (centeredSlides && slidesPerView % 2 === 0) {\n      slidesPerView = slidesPerView + 1;\n    }\n  }\n  const slidesPerGroup = params.slidesPerGroupAuto ? slidesPerView : params.slidesPerGroup;\n  let loopedSlides = centeredSlides ? Math.max(slidesPerGroup, Math.ceil(slidesPerView / 2)) : slidesPerGroup;\n  if (loopedSlides % slidesPerGroup !== 0) {\n    loopedSlides += slidesPerGroup - loopedSlides % slidesPerGroup;\n  }\n  loopedSlides += params.loopAdditionalSlides;\n  swiper.loopedSlides = loopedSlides;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (slides.length < slidesPerView + loopedSlides || swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    showWarning('Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters');\n  } else if (gridEnabled && params.grid.fill === 'row') {\n    showWarning('Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`');\n  }\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n  const cols = gridEnabled ? Math.ceil(slides.length / params.grid.rows) : slides.length;\n  const isInitialOverflow = initial && cols - initialSlide < slidesPerView && !centeredSlides;\n  let activeIndex = isInitialOverflow ? initialSlide : swiper.activeIndex;\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(slides.find(el => el.classList.contains(params.slideActiveClass)));\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  const activeColIndex = gridEnabled ? slides[activeSlideIndex].column : activeSlideIndex;\n  const activeColIndexWithShift = activeColIndex + (centeredSlides && typeof setTranslate === 'undefined' ? -slidesPerView / 2 + 0.5 : 0);\n  // prepend last slides before start\n  if (activeColIndexWithShift < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeColIndexWithShift, slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeColIndexWithShift; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        const colIndexToPrepend = cols - index - 1;\n        for (let i = slides.length - 1; i >= 0; i -= 1) {\n          if (slides[i].column === colIndexToPrepend) prependSlidesIndexes.push(i);\n        }\n        // slides.forEach((slide, slideIndex) => {\n        //   if (slide.column === colIndexToPrepend) prependSlidesIndexes.push(slideIndex);\n        // });\n      } else {\n        prependSlidesIndexes.push(cols - index - 1);\n      }\n    }\n  } else if (activeColIndexWithShift + slidesPerView > cols - loopedSlides) {\n    slidesAppended = Math.max(activeColIndexWithShift - (cols - loopedSlides * 2), slidesPerGroup);\n    if (isInitialOverflow) {\n      slidesAppended = Math.max(slidesAppended, slidesPerView - cols + initialSlide + 1);\n    }\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        slides.forEach((slide, slideIndex) => {\n          if (slide.column === index) appendSlidesIndexes.push(slideIndex);\n        });\n      } else {\n        appendSlidesIndexes.push(index);\n      }\n    }\n  }\n  swiper.__preventObserver__ = true;\n  requestAnimationFrame(() => {\n    swiper.__preventObserver__ = false;\n  });\n  if (swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    if (appendSlidesIndexes.includes(activeSlideIndex)) {\n      appendSlidesIndexes.splice(appendSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n    if (prependSlidesIndexes.includes(activeSlideIndex)) {\n      prependSlidesIndexes.splice(prependSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n  }\n  if (isPrev) {\n    prependSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.prepend(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.append(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  swiper.recalcSlides();\n  if (params.slidesPerView === 'auto') {\n    swiper.updateSlides();\n  } else if (gridEnabled && (prependSlidesIndexes.length > 0 && isPrev || appendSlidesIndexes.length > 0 && isNext)) {\n    swiper.slides.forEach((slide, slideIndex) => {\n      swiper.grid.updateSlide(slideIndex, slide, swiper.slides);\n    });\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + Math.ceil(slidesPrepended), 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          const shift = gridEnabled ? prependSlidesIndexes.length / params.grid.rows : prependSlidesIndexes.length;\n          swiper.slideTo(swiper.activeIndex + shift, 0, false, true);\n          swiper.touchEventsData.currentTranslate = swiper.translate;\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        const shift = gridEnabled ? appendSlidesIndexes.length / params.grid.rows : appendSlidesIndexes.length;\n        swiper.slideTo(swiper.activeIndex - shift, 0, false, true);\n      }\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach(c => {\n        if (!c.destroyed && c.params.loop) c.loopFix({\n          ...loopParams,\n          slideTo: c.params.slidesPerView === params.slidesPerView ? slideTo : false\n        });\n      });\n    } else if (swiper.controller.control instanceof swiper.constructor && swiper.controller.control.params.loop) {\n      swiper.controller.control.loopFix({\n        ...loopParams,\n        slideTo: swiper.controller.control.params.slidesPerView === params.slidesPerView ? slideTo : false\n      });\n    }\n  }\n  swiper.emit('loopFix');\n}\n\nfunction loopDestroy() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || !slidesEl || swiper.virtual && swiper.params.virtual.enabled) return;\n  swiper.recalcSlides();\n  const newSlidesOrder = [];\n  swiper.slides.forEach(slideEl => {\n    const index = typeof slideEl.swiperSlideIndex === 'undefined' ? slideEl.getAttribute('data-swiper-slide-index') * 1 : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach(slideEl => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach(slideEl => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}\n\nvar loop = {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};\n\nfunction setGrabCursor(moving) {\n  const swiper = this;\n  if (!swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nfunction unsetGrabCursor() {\n  const swiper = this;\n  if (swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nvar grabCursor = {\n  setGrabCursor,\n  unsetGrabCursor\n};\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base) {\n  if (base === void 0) {\n    base = this;\n  }\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\nfunction preventEdgeSwipe(swiper, event, startX) {\n  const window = getWindow();\n  const {\n    params\n  } = swiper;\n  const edgeSwipeDetection = params.edgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold;\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  const data = swiper.touchEventsData;\n  if (e.type === 'pointerdown') {\n    if (data.pointerId !== null && data.pointerId !== e.pointerId) {\n      return;\n    }\n    data.pointerId = e.pointerId;\n  } else if (e.type === 'touchstart' && e.targetTouches.length === 1) {\n    data.touchId = e.targetTouches[0].identifier;\n  }\n  if (e.type === 'touchstart') {\n    // don't proceed touch event\n    preventEdgeSwipe(swiper, e, e.targetTouches[0].pageX);\n    return;\n  }\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let targetEl = e.target;\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!elementIsChildOf(targetEl, swiper.wrapperEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = e.composedPath ? e.composedPath() : e.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, targetEl) : targetEl.closest(noSwipingSelector))) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  if (!preventEdgeSwipe(swiper, e, startX)) {\n    return;\n  }\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== targetEl && (e.pointerType === 'mouse' || e.pointerType !== 'mouse' && !targetEl.matches(data.focusableElements))) {\n    document.activeElement.blur();\n  }\n  const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !targetEl.isContentEditable) {\n    e.preventDefault();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}\n\nfunction onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (e.type === 'pointermove') {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    const id = e.pointerId;\n    if (id !== data.pointerId) return;\n  }\n  let targetTouch;\n  if (e.type === 'touchmove') {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  } else {\n    targetTouch = e;\n  }\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (rtl && (pageX > touches.startX && -swiper.translate <= swiper.maxTranslate() || pageX < touches.startX && -swiper.translate >= swiper.minTranslate())) {\n      return;\n    } else if (!rtl && (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate())) {\n      return;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== e.target && e.pointerType !== 'mouse') {\n    document.activeElement.blur();\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  touches.previousX = touches.currentX;\n  touches.previousY = touches.currentY;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling || e.type === 'touchmove' && data.preventTouchMoveFromPointerMove) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal() ? touches.currentX - touches.previousX : touches.currentY - touches.previousY;\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n  const isLoop = swiper.params.loop && !params.cssMode;\n  const allowLoopFix = swiper.touchesDirection === 'next' && swiper.allowSlideNext || swiper.touchesDirection === 'prev' && swiper.allowSlidePrev;\n  if (!data.isMoved) {\n    if (isLoop && allowLoopFix) {\n      swiper.loopFix({\n        direction: swiper.swipeDirection\n      });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true,\n        detail: {\n          bySwiperTouchMove: true\n        }\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  new Date().getTime();\n  if (params._loopSwapReset !== false && data.isMoved && data.allowThresholdMove && prevTouchesDirection !== swiper.touchesDirection && isLoop && allowLoopFix && Math.abs(diff) >= 1) {\n    Object.assign(touches, {\n      startX: pageX,\n      startY: pageY,\n      currentX: pageX,\n      currentY: pageY,\n      startTranslate: data.currentTranslate\n    });\n    data.loopSwapReset = true;\n    data.startTranslate = data.currentTranslate;\n    return;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate > (params.centeredSlides ? swiper.minTranslate() - swiper.slidesSizesGrid[swiper.activeIndex + 1] - (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.activeIndex + 1] + swiper.params.spaceBetween : 0) - swiper.params.spaceBetween : swiper.minTranslate())) {\n      swiper.loopFix({\n        direction: 'prev',\n        setTranslate: true,\n        activeSlideIndex: 0\n      });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate < (params.centeredSlides ? swiper.maxTranslate() + swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween + (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween : 0) : swiper.maxTranslate())) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex: swiper.slides.length - (params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10)))\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}\n\nfunction onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetTouch;\n  const isTouchEvent = e.type === 'touchend' || e.type === 'touchcancel';\n  if (!isTouchEvent) {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    if (e.pointerId !== data.pointerId) return;\n    targetTouch = e;\n  } else {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave', 'contextmenu'].includes(e.type)) {\n    const proceed = ['pointercancel', 'contextmenu'].includes(e.type) && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n  data.pointerId = null;\n  data.touchId = null;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target, pathTree);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 && !data.loopSwapReset || data.currentTranslate === data.startTranslate && !data.loopSwapReset) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n  if (params.cssMode) {\n    return;\n  }\n  if (params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  }\n\n  // Find current slide\n  const swipeToLast = currentPos >= -swiper.maxTranslate() && !swiper.params.loop;\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (swipeToLast || currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (swipeToLast || currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = params.virtual && params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}\n\nfunction onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper;\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides && !isVirtualLoop) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(swiper.autoplay.resizeTimeout);\n    swiper.autoplay.resizeTimeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}\n\nfunction onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}\n\nfunction onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, false);\n}\n\nfunction onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  if (swiper.params.cssMode || swiper.params.slidesPerView !== 'auto' && !swiper.params.autoHeight) {\n    return;\n  }\n  swiper.update();\n}\n\nfunction onDocumentTouchStart() {\n  const swiper = this;\n  if (swiper.documentTouchHandlerProceeded) return;\n  swiper.documentTouchHandlerProceeded = true;\n  if (swiper.params.touchReleaseOnEdges) {\n    swiper.el.style.touchAction = 'auto';\n  }\n}\n\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    el,\n    wrapperEl,\n    device\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n  if (!el || typeof el === 'string') return;\n\n  // Touch Events\n  document[domMethod]('touchstart', swiper.onDocumentTouchStart, {\n    passive: false,\n    capture\n  });\n  el[domMethod]('touchstart', swiper.onTouchStart, {\n    passive: false\n  });\n  el[domMethod]('pointerdown', swiper.onTouchStart, {\n    passive: false\n  });\n  document[domMethod]('touchmove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('pointermove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('touchend', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerup', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('touchcancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerout', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('contextmenu', swiper.onTouchEnd, {\n    passive: true\n  });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, {\n    capture: true\n  });\n};\nfunction attachEvents() {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  swiper.onDocumentTouchStart = onDocumentTouchStart.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n  events(swiper, 'on');\n}\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\nvar events$1 = {\n  attachEvents,\n  detachEvents\n};\n\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\nfunction setBreakpoint() {\n  const swiper = this;\n  const {\n    realIndex,\n    initialized,\n    params,\n    el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return;\n  const document = getDocument();\n\n  // Get breakpoint for window/container width and update parameters\n  const breakpointsBase = params.breakpointsBase === 'window' || !params.breakpointsBase ? params.breakpointsBase : 'container';\n  const breakpointContainer = ['window', 'container'].includes(params.breakpointsBase) || !params.breakpointsBase ? swiper.el : document.querySelector(params.breakpointsBase);\n  const breakpoint = swiper.getBreakpoint(breakpoints, breakpointsBase, breakpointContainer);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasGrabCursor = swiper.params.grabCursor;\n  const isGrabCursor = breakpointParams.grabCursor;\n  const wasEnabled = params.enabled;\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n  if (wasGrabCursor && !isGrabCursor) {\n    swiper.unsetGrabCursor();\n  } else if (!wasGrabCursor && isGrabCursor) {\n    swiper.setGrabCursor();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    if (typeof breakpointParams[prop] === 'undefined') return;\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n  const wasLoop = params.loop;\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  const hasLoop = swiper.params.loop;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n  if (initialized) {\n    if (needsReLoop) {\n      swiper.loopDestroy();\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (!wasLoop && hasLoop) {\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (wasLoop && !hasLoop) {\n      swiper.loopDestroy();\n    }\n  }\n  swiper.emit('breakpoint', breakpointParams);\n}\n\nfunction getBreakpoint(breakpoints, base, containerEl) {\n  if (base === void 0) {\n    base = 'window';\n  }\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}\n\nvar breakpoints = {\n  setBreakpoint,\n  getBreakpoint\n};\n\nfunction prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\nfunction addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    el,\n    device\n  } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}\n\nfunction removeClasses() {\n  const swiper = this;\n  const {\n    el,\n    classNames\n  } = swiper;\n  if (!el || typeof el === 'string') return;\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}\n\nvar classes = {\n  addClasses,\n  removeClasses\n};\n\nfunction checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\nvar checkOverflow$1 = {\n  checkOverflow\n};\n\nvar defaults = {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  swiperElementNodeName: 'SWIPER-CONTAINER',\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  eventsPrefix: 'swiper',\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // loop\n  loop: false,\n  loopAddBlankSlides: true,\n  loopAdditionalSlides: 0,\n  loopPreventsSliding: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideBlankClass: 'swiper-slide-blank',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideFullyVisibleClass: 'swiper-slide-fully-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};\n\nfunction moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj) {\n    if (obj === void 0) {\n      obj = {};\n    }\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n    if (moduleParamName === 'navigation' && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].prevEl && !params[moduleParamName].nextEl) {\n      params[moduleParamName].auto = true;\n    }\n    if (['pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].el) {\n      params[moduleParamName].auto = true;\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}\n\n/* eslint no-param-reassign: \"off\" */\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events: events$1,\n  breakpoints,\n  checkOverflow: checkOverflow$1,\n  classes\n};\nconst extendedDefaults = {};\nclass Swiper {\n  constructor() {\n    let el;\n    let params;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n    const document = getDocument();\n    if (params.el && typeof params.el === 'string' && document.querySelectorAll(params.el).length > 1) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      cssOverflowAdjustment() {\n        // Returns 0 unless `translate` is > 2**23\n        // Should be subtracted from css values to prevent overflow\n        return Math.trunc(this.translate / 2 ** 23) * 2 ** 23;\n      },\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        pointerId: null,\n        touchId: null\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n  getDirectionLabel(property) {\n    if (this.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n  getSlideIndex(slideEl) {\n    const {\n      slidesEl,\n      params\n    } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(this.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === index));\n  }\n  getSlideIndexWhenGrid(index) {\n    if (this.grid && this.params.grid && this.params.grid.rows > 1) {\n      if (this.params.grid.fill === 'column') {\n        index = Math.floor(index / this.params.grid.rows);\n      } else if (this.params.grid.fill === 'row') {\n        index = index % Math.ceil(this.slides.length / this.params.grid.rows);\n      }\n    }\n    return index;\n  }\n  recalcSlides() {\n    const swiper = this;\n    const {\n      slidesEl,\n      params\n    } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n  slidesPerViewDynamic(view, exact) {\n    if (view === void 0) {\n      view = 'current';\n    }\n    if (exact === void 0) {\n      exact = false;\n    }\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n    if (typeof params.slidesPerView === 'number') return params.slidesPerView;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex] ? Math.ceil(slides[activeIndex].swiperSlideSize) : 0;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += Math.ceil(slides[i].swiperSlideSize);\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (params.freeMode && params.freeMode.enabled && !params.cssMode) {\n      setTranslate();\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !params.centeredSlides) {\n        const slides = swiper.virtual && params.virtual.enabled ? swiper.virtual.slides : swiper.slides;\n        translated = swiper.slideTo(slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n  changeDirection(newDirection, needUpdate) {\n    if (needUpdate === void 0) {\n      needUpdate = true;\n    }\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.forEach(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n    el.swiper = swiper;\n    if (el.parentNode && el.parentNode.host && el.parentNode.host.nodeName === swiper.params.swiperElementNodeName.toUpperCase()) {\n      swiper.isElement = true;\n    }\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach(slideEl => {\n        wrapperEl.append(slideEl);\n      });\n    }\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement && !el.parentNode.host.slideSlots ? el.parentNode.host : wrapperEl,\n      hostEl: swiper.isElement ? el.parentNode.host : el,\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box'\n    });\n    return true;\n  }\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.virtual.slidesBefore, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate(undefined, true);\n    }\n\n    // Attach events\n    swiper.attachEvents();\n    const lazyElements = [...swiper.el.querySelectorAll('[loading=\"lazy\"]')];\n    if (swiper.isElement) {\n      lazyElements.push(...swiper.hostEl.querySelectorAll('[loading=\"lazy\"]'));\n    }\n    lazyElements.forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', e => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n  destroy(deleteInstance, cleanStyles) {\n    if (deleteInstance === void 0) {\n      deleteInstance = true;\n    }\n    if (cleanStyles === void 0) {\n      cleanStyles = true;\n    }\n    const swiper = this;\n    const {\n      params,\n      el,\n      wrapperEl,\n      slides\n    } = swiper;\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      if (el && typeof el !== 'string') {\n        el.removeAttribute('style');\n      }\n      if (wrapperEl) {\n        wrapperEl.removeAttribute('style');\n      }\n      if (slides && slides.length) {\n        slides.forEach(slideEl => {\n          slideEl.classList.remove(params.slideVisibleClass, params.slideFullyVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n    if (deleteInstance !== false) {\n      if (swiper.el && typeof swiper.el !== 'string') {\n        swiper.el.swiper = null;\n      }\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n    return null;\n  }\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n  static get defaults() {\n    return defaults;\n  }\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\n\nexport { Swiper as S, defaults as d };\n", "import { e as elementChildren, c as createElement } from './utils.mjs';\n\nfunction createElementIfNotDefined(swiper, originalParams, params, checkProps) {\n  if (swiper.params.createElements) {\n    Object.keys(checkProps).forEach(key => {\n      if (!params[key] && params.auto === true) {\n        let element = elementChildren(swiper.el, `.${checkProps[key]}`)[0];\n        if (!element) {\n          element = createElement('div', checkProps[key]);\n          element.className = checkProps[key];\n          swiper.el.append(element);\n        }\n        params[key] = element;\n        originalParams[key] = element;\n      }\n    });\n  }\n  return params;\n}\n\nexport { createElementIfNotDefined as c };\n", "import { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { m as makeElementsArray } from '../shared/utils.mjs';\n\nfunction Navigation(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  extendParams({\n    navigation: {\n      nextEl: null,\n      prevEl: null,\n      hideOnClick: false,\n      disabledClass: 'swiper-button-disabled',\n      hiddenClass: 'swiper-button-hidden',\n      lockClass: 'swiper-button-lock',\n      navigationDisabledClass: 'swiper-navigation-disabled'\n    }\n  });\n  swiper.navigation = {\n    nextEl: null,\n    prevEl: null\n  };\n  function getEl(el) {\n    let res;\n    if (el && typeof el === 'string' && swiper.isElement) {\n      res = swiper.el.querySelector(el) || swiper.hostEl.querySelector(el);\n      if (res) return res;\n    }\n    if (el) {\n      if (typeof el === 'string') res = [...document.querySelectorAll(el)];\n      if (swiper.params.uniqueNavElements && typeof el === 'string' && res && res.length > 1 && swiper.el.querySelectorAll(el).length === 1) {\n        res = swiper.el.querySelector(el);\n      } else if (res && res.length === 1) {\n        res = res[0];\n      }\n    }\n    if (el && !res) return el;\n    // if (Array.isArray(res) && res.length === 1) res = res[0];\n    return res;\n  }\n  function toggleEl(el, disabled) {\n    const params = swiper.params.navigation;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (subEl) {\n        subEl.classList[disabled ? 'add' : 'remove'](...params.disabledClass.split(' '));\n        if (subEl.tagName === 'BUTTON') subEl.disabled = disabled;\n        if (swiper.params.watchOverflow && swiper.enabled) {\n          subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n        }\n      }\n    });\n  }\n  function update() {\n    // Update Navigation Buttons\n    const {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    if (swiper.params.loop) {\n      toggleEl(prevEl, false);\n      toggleEl(nextEl, false);\n      return;\n    }\n    toggleEl(prevEl, swiper.isBeginning && !swiper.params.rewind);\n    toggleEl(nextEl, swiper.isEnd && !swiper.params.rewind);\n  }\n  function onPrevClick(e) {\n    e.preventDefault();\n    if (swiper.isBeginning && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slidePrev();\n    emit('navigationPrev');\n  }\n  function onNextClick(e) {\n    e.preventDefault();\n    if (swiper.isEnd && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slideNext();\n    emit('navigationNext');\n  }\n  function init() {\n    const params = swiper.params.navigation;\n    swiper.params.navigation = createElementIfNotDefined(swiper, swiper.originalParams.navigation, swiper.params.navigation, {\n      nextEl: 'swiper-button-next',\n      prevEl: 'swiper-button-prev'\n    });\n    if (!(params.nextEl || params.prevEl)) return;\n    let nextEl = getEl(params.nextEl);\n    let prevEl = getEl(params.prevEl);\n    Object.assign(swiper.navigation, {\n      nextEl,\n      prevEl\n    });\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const initButton = (el, dir) => {\n      if (el) {\n        el.addEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      }\n      if (!swiper.enabled && el) {\n        el.classList.add(...params.lockClass.split(' '));\n      }\n    };\n    nextEl.forEach(el => initButton(el, 'next'));\n    prevEl.forEach(el => initButton(el, 'prev'));\n  }\n  function destroy() {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const destroyButton = (el, dir) => {\n      el.removeEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      el.classList.remove(...swiper.params.navigation.disabledClass.split(' '));\n    };\n    nextEl.forEach(el => destroyButton(el, 'next'));\n    prevEl.forEach(el => destroyButton(el, 'prev'));\n  }\n  on('init', () => {\n    if (swiper.params.navigation.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      update();\n    }\n  });\n  on('toEdge fromEdge lock unlock', () => {\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (swiper.enabled) {\n      update();\n      return;\n    }\n    [...nextEl, ...prevEl].filter(el => !!el).forEach(el => el.classList.add(swiper.params.navigation.lockClass));\n  });\n  on('click', (_s, e) => {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const targetEl = e.target;\n    let targetIsButton = prevEl.includes(targetEl) || nextEl.includes(targetEl);\n    if (swiper.isElement && !targetIsButton) {\n      const path = e.path || e.composedPath && e.composedPath();\n      if (path) {\n        targetIsButton = path.find(pathEl => nextEl.includes(pathEl) || prevEl.includes(pathEl));\n      }\n    }\n    if (swiper.params.navigation.hideOnClick && !targetIsButton) {\n      if (swiper.pagination && swiper.params.pagination && swiper.params.pagination.clickable && (swiper.pagination.el === targetEl || swiper.pagination.el.contains(targetEl))) return;\n      let isHidden;\n      if (nextEl.length) {\n        isHidden = nextEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      } else if (prevEl.length) {\n        isHidden = prevEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      }\n      if (isHidden === true) {\n        emit('navigationShow');\n      } else {\n        emit('navigationHide');\n      }\n      [...nextEl, ...prevEl].filter(el => !!el).forEach(el => el.classList.toggle(swiper.params.navigation.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    init();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    destroy();\n  };\n  Object.assign(swiper.navigation, {\n    enable,\n    disable,\n    update,\n    init,\n    destroy\n  });\n}\n\nexport { Navigation as default };\n", "function classesToSelector(classes) {\n  if (classes === void 0) {\n    classes = '';\n  }\n  return `.${classes.trim().replace(/([\\.:!+\\/()[\\]])/g, '\\\\$1') // eslint-disable-line\n  .replace(/ /g, '.')}`;\n}\n\nexport { classesToSelector as c };\n", "import { c as classesToSelector } from '../shared/classes-to-selector.mjs';\nimport { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { m as makeElementsArray, h as elementOuterSize, i as elementIndex, s as setInnerHTML, b as elementParents } from '../shared/utils.mjs';\n\nfunction Pagination(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const pfx = 'swiper-pagination';\n  extendParams({\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets',\n      // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: number => number,\n      formatFractionTotal: number => number,\n      bulletClass: `${pfx}-bullet`,\n      bulletActiveClass: `${pfx}-bullet-active`,\n      modifierClass: `${pfx}-`,\n      currentClass: `${pfx}-current`,\n      totalClass: `${pfx}-total`,\n      hiddenClass: `${pfx}-hidden`,\n      progressbarFillClass: `${pfx}-progressbar-fill`,\n      progressbarOppositeClass: `${pfx}-progressbar-opposite`,\n      clickableClass: `${pfx}-clickable`,\n      lockClass: `${pfx}-lock`,\n      horizontalClass: `${pfx}-horizontal`,\n      verticalClass: `${pfx}-vertical`,\n      paginationDisabledClass: `${pfx}-disabled`\n    }\n  });\n  swiper.pagination = {\n    el: null,\n    bullets: []\n  };\n  let bulletSize;\n  let dynamicBulletIndex = 0;\n  function isPaginationDisabled() {\n    return !swiper.params.pagination.el || !swiper.pagination.el || Array.isArray(swiper.pagination.el) && swiper.pagination.el.length === 0;\n  }\n  function setSideBullets(bulletEl, position) {\n    const {\n      bulletActiveClass\n    } = swiper.params.pagination;\n    if (!bulletEl) return;\n    bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n    if (bulletEl) {\n      bulletEl.classList.add(`${bulletActiveClass}-${position}`);\n      bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n      if (bulletEl) {\n        bulletEl.classList.add(`${bulletActiveClass}-${position}-${position}`);\n      }\n    }\n  }\n  function getMoveDirection(prevIndex, nextIndex, length) {\n    prevIndex = prevIndex % length;\n    nextIndex = nextIndex % length;\n    if (nextIndex === prevIndex + 1) {\n      return 'next';\n    } else if (nextIndex === prevIndex - 1) {\n      return 'previous';\n    }\n    return;\n  }\n  function onBulletClick(e) {\n    const bulletEl = e.target.closest(classesToSelector(swiper.params.pagination.bulletClass));\n    if (!bulletEl) {\n      return;\n    }\n    e.preventDefault();\n    const index = elementIndex(bulletEl) * swiper.params.slidesPerGroup;\n    if (swiper.params.loop) {\n      if (swiper.realIndex === index) return;\n      const moveDirection = getMoveDirection(swiper.realIndex, index, swiper.slides.length);\n      if (moveDirection === 'next') {\n        swiper.slideNext();\n      } else if (moveDirection === 'previous') {\n        swiper.slidePrev();\n      } else {\n        swiper.slideToLoop(index);\n      }\n    } else {\n      swiper.slideTo(index);\n    }\n  }\n  function update() {\n    // Render || Update Pagination bullets/items\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    // Current/Total\n    let current;\n    let previousIndex;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    const total = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n    if (swiper.params.loop) {\n      previousIndex = swiper.previousRealIndex || 0;\n      current = swiper.params.slidesPerGroup > 1 ? Math.floor(swiper.realIndex / swiper.params.slidesPerGroup) : swiper.realIndex;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n      previousIndex = swiper.previousSnapIndex;\n    } else {\n      previousIndex = swiper.previousIndex || 0;\n      current = swiper.activeIndex || 0;\n    }\n    // Types\n    if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n      if (params.dynamicBullets) {\n        bulletSize = elementOuterSize(bullets[0], swiper.isHorizontal() ? 'width' : 'height', true);\n        el.forEach(subEl => {\n          subEl.style[swiper.isHorizontal() ? 'width' : 'height'] = `${bulletSize * (params.dynamicMainBullets + 4)}px`;\n        });\n        if (params.dynamicMainBullets > 1 && previousIndex !== undefined) {\n          dynamicBulletIndex += current - (previousIndex || 0);\n          if (dynamicBulletIndex > params.dynamicMainBullets - 1) {\n            dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (dynamicBulletIndex < 0) {\n            dynamicBulletIndex = 0;\n          }\n        }\n        firstIndex = Math.max(current - dynamicBulletIndex, 0);\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n      bullets.forEach(bulletEl => {\n        const classesToRemove = [...['', '-next', '-next-next', '-prev', '-prev-prev', '-main'].map(suffix => `${params.bulletActiveClass}${suffix}`)].map(s => typeof s === 'string' && s.includes(' ') ? s.split(' ') : s).flat();\n        bulletEl.classList.remove(...classesToRemove);\n      });\n      if (el.length > 1) {\n        bullets.forEach(bullet => {\n          const bulletIndex = elementIndex(bullet);\n          if (bulletIndex === current) {\n            bullet.classList.add(...params.bulletActiveClass.split(' '));\n          } else if (swiper.isElement) {\n            bullet.setAttribute('part', 'bullet');\n          }\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              bullet.classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n            if (bulletIndex === firstIndex) {\n              setSideBullets(bullet, 'prev');\n            }\n            if (bulletIndex === lastIndex) {\n              setSideBullets(bullet, 'next');\n            }\n          }\n        });\n      } else {\n        const bullet = bullets[current];\n        if (bullet) {\n          bullet.classList.add(...params.bulletActiveClass.split(' '));\n        }\n        if (swiper.isElement) {\n          bullets.forEach((bulletEl, bulletIndex) => {\n            bulletEl.setAttribute('part', bulletIndex === current ? 'bullet-active' : 'bullet');\n          });\n        }\n        if (params.dynamicBullets) {\n          const firstDisplayedBullet = bullets[firstIndex];\n          const lastDisplayedBullet = bullets[lastIndex];\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            if (bullets[i]) {\n              bullets[i].classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n          }\n          setSideBullets(firstDisplayedBullet, 'prev');\n          setSideBullets(lastDisplayedBullet, 'next');\n        }\n      }\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset = (bulletSize * dynamicBulletsLength - bulletSize) / 2 - midIndex * bulletSize;\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.forEach(bullet => {\n          bullet.style[swiper.isHorizontal() ? offsetProp : 'top'] = `${bulletsOffset}px`;\n        });\n      }\n    }\n    el.forEach((subEl, subElIndex) => {\n      if (params.type === 'fraction') {\n        subEl.querySelectorAll(classesToSelector(params.currentClass)).forEach(fractionEl => {\n          fractionEl.textContent = params.formatFractionCurrent(current + 1);\n        });\n        subEl.querySelectorAll(classesToSelector(params.totalClass)).forEach(totalEl => {\n          totalEl.textContent = params.formatFractionTotal(total);\n        });\n      }\n      if (params.type === 'progressbar') {\n        let progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        const scale = (current + 1) / total;\n        let scaleX = 1;\n        let scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        subEl.querySelectorAll(classesToSelector(params.progressbarFillClass)).forEach(progressEl => {\n          progressEl.style.transform = `translate3d(0,0,0) scaleX(${scaleX}) scaleY(${scaleY})`;\n          progressEl.style.transitionDuration = `${swiper.params.speed}ms`;\n        });\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        setInnerHTML(subEl, params.renderCustom(swiper, current + 1, total));\n        if (subElIndex === 0) emit('paginationRender', subEl);\n      } else {\n        if (subElIndex === 0) emit('paginationRender', subEl);\n        emit('paginationUpdate', subEl);\n      }\n      if (swiper.params.watchOverflow && swiper.enabled) {\n        subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n      }\n    });\n  }\n  function render() {\n    // Render Container\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.grid && swiper.params.grid.rows > 1 ? swiper.slides.length / Math.ceil(swiper.params.grid.rows) : swiper.slides.length;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    let paginationHTML = '';\n    if (params.type === 'bullets') {\n      let numberOfBullets = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.freeMode && swiper.params.freeMode.enabled && numberOfBullets > slidesLength) {\n        numberOfBullets = slidesLength;\n      }\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          // prettier-ignore\n          paginationHTML += `<${params.bulletElement} ${swiper.isElement ? 'part=\"bullet\"' : ''} class=\"${params.bulletClass}\"></${params.bulletElement}>`;\n        }\n      }\n    }\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML = `<span class=\"${params.currentClass}\"></span>` + ' / ' + `<span class=\"${params.totalClass}\"></span>`;\n      }\n    }\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = `<span class=\"${params.progressbarFillClass}\"></span>`;\n      }\n    }\n    swiper.pagination.bullets = [];\n    el.forEach(subEl => {\n      if (params.type !== 'custom') {\n        setInnerHTML(subEl, paginationHTML || '');\n      }\n      if (params.type === 'bullets') {\n        swiper.pagination.bullets.push(...subEl.querySelectorAll(classesToSelector(params.bulletClass)));\n      }\n    });\n    if (params.type !== 'custom') {\n      emit('paginationRender', el[0]);\n    }\n  }\n  function init() {\n    swiper.params.pagination = createElementIfNotDefined(swiper, swiper.originalParams.pagination, swiper.params.pagination, {\n      el: 'swiper-pagination'\n    });\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = [...document.querySelectorAll(params.el)];\n    }\n    if (!el) {\n      el = params.el;\n    }\n    if (!el || el.length === 0) return;\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && Array.isArray(el) && el.length > 1) {\n      el = [...swiper.el.querySelectorAll(params.el)];\n      // check if it belongs to another nested Swiper\n      if (el.length > 1) {\n        el = el.find(subEl => {\n          if (elementParents(subEl, '.swiper')[0] !== swiper.el) return false;\n          return true;\n        });\n      }\n    }\n    if (Array.isArray(el) && el.length === 1) el = el[0];\n    Object.assign(swiper.pagination, {\n      el\n    });\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (params.type === 'bullets' && params.clickable) {\n        subEl.classList.add(...(params.clickableClass || '').split(' '));\n      }\n      subEl.classList.add(params.modifierClass + params.type);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        subEl.classList.add(`${params.modifierClass}${params.type}-dynamic`);\n        dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        subEl.classList.add(params.progressbarOppositeClass);\n      }\n      if (params.clickable) {\n        subEl.addEventListener('click', onBulletClick);\n      }\n      if (!swiper.enabled) {\n        subEl.classList.add(params.lockClass);\n      }\n    });\n  }\n  function destroy() {\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => {\n        subEl.classList.remove(params.hiddenClass);\n        subEl.classList.remove(params.modifierClass + params.type);\n        subEl.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n        if (params.clickable) {\n          subEl.classList.remove(...(params.clickableClass || '').split(' '));\n          subEl.removeEventListener('click', onBulletClick);\n        }\n      });\n    }\n    if (swiper.pagination.bullets) swiper.pagination.bullets.forEach(subEl => subEl.classList.remove(...params.bulletActiveClass.split(' ')));\n  }\n  on('changeDirection', () => {\n    if (!swiper.pagination || !swiper.pagination.el) return;\n    const params = swiper.params.pagination;\n    let {\n      el\n    } = swiper.pagination;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.classList.remove(params.horizontalClass, params.verticalClass);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    });\n  });\n  on('init', () => {\n    if (swiper.params.pagination.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      render();\n      update();\n    }\n  });\n  on('activeIndexChange', () => {\n    if (typeof swiper.snapIndex === 'undefined') {\n      update();\n    }\n  });\n  on('snapIndexChange', () => {\n    update();\n  });\n  on('snapGridLengthChange', () => {\n    render();\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.pagination.lockClass));\n    }\n  });\n  on('lock unlock', () => {\n    update();\n  });\n  on('click', (_s, e) => {\n    const targetEl = e.target;\n    const el = makeElementsArray(swiper.pagination.el);\n    if (swiper.params.pagination.el && swiper.params.pagination.hideOnClick && el && el.length > 0 && !targetEl.classList.contains(swiper.params.pagination.bulletClass)) {\n      if (swiper.navigation && (swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl || swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl)) return;\n      const isHidden = el[0].classList.contains(swiper.params.pagination.hiddenClass);\n      if (isHidden === true) {\n        emit('paginationShow');\n      } else {\n        emit('paginationHide');\n      }\n      el.forEach(subEl => subEl.classList.toggle(swiper.params.pagination.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.remove(swiper.params.pagination.paginationDisabledClass));\n    }\n    init();\n    render();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.add(swiper.params.pagination.paginationDisabledClass));\n    }\n    destroy();\n  };\n  Object.assign(swiper.pagination, {\n    enable,\n    disable,\n    render,\n    update,\n    init,\n    destroy\n  });\n}\n\nexport { Pagination as default };\n", "import { g as getDocument } from '../shared/ssr-window.esm.mjs';\n\n/* eslint no-underscore-dangle: \"off\" */\n/* eslint no-use-before-define: \"off\" */\nfunction Autoplay(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit,\n    params\n  } = _ref;\n  swiper.autoplay = {\n    running: false,\n    paused: false,\n    timeLeft: 0\n  };\n  extendParams({\n    autoplay: {\n      enabled: false,\n      delay: 3000,\n      waitForTransition: true,\n      disableOnInteraction: false,\n      stopOnLastSlide: false,\n      reverseDirection: false,\n      pauseOnMouseEnter: false\n    }\n  });\n  let timeout;\n  let raf;\n  let autoplayDelayTotal = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayDelayCurrent = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayTimeLeft;\n  let autoplayStartTime = new Date().getTime();\n  let wasPaused;\n  let isTouched;\n  let pausedByTouch;\n  let touchStartTimeout;\n  let slideChanged;\n  let pausedByInteraction;\n  let pausedByPointerEnter;\n  function onTransitionEnd(e) {\n    if (!swiper || swiper.destroyed || !swiper.wrapperEl) return;\n    if (e.target !== swiper.wrapperEl) return;\n    swiper.wrapperEl.removeEventListener('transitionend', onTransitionEnd);\n    if (pausedByPointerEnter || e.detail && e.detail.bySwiperTouchMove) {\n      return;\n    }\n    resume();\n  }\n  const calcTimeLeft = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.autoplay.paused) {\n      wasPaused = true;\n    } else if (wasPaused) {\n      autoplayDelayCurrent = autoplayTimeLeft;\n      wasPaused = false;\n    }\n    const timeLeft = swiper.autoplay.paused ? autoplayTimeLeft : autoplayStartTime + autoplayDelayCurrent - new Date().getTime();\n    swiper.autoplay.timeLeft = timeLeft;\n    emit('autoplayTimeLeft', timeLeft, timeLeft / autoplayDelayTotal);\n    raf = requestAnimationFrame(() => {\n      calcTimeLeft();\n    });\n  };\n  const getSlideDelay = () => {\n    let activeSlideEl;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      activeSlideEl = swiper.slides.find(slideEl => slideEl.classList.contains('swiper-slide-active'));\n    } else {\n      activeSlideEl = swiper.slides[swiper.activeIndex];\n    }\n    if (!activeSlideEl) return undefined;\n    const currentSlideDelay = parseInt(activeSlideEl.getAttribute('data-swiper-autoplay'), 10);\n    return currentSlideDelay;\n  };\n  const run = delayForce => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    cancelAnimationFrame(raf);\n    calcTimeLeft();\n    let delay = typeof delayForce === 'undefined' ? swiper.params.autoplay.delay : delayForce;\n    autoplayDelayTotal = swiper.params.autoplay.delay;\n    autoplayDelayCurrent = swiper.params.autoplay.delay;\n    const currentSlideDelay = getSlideDelay();\n    if (!Number.isNaN(currentSlideDelay) && currentSlideDelay > 0 && typeof delayForce === 'undefined') {\n      delay = currentSlideDelay;\n      autoplayDelayTotal = currentSlideDelay;\n      autoplayDelayCurrent = currentSlideDelay;\n    }\n    autoplayTimeLeft = delay;\n    const speed = swiper.params.speed;\n    const proceed = () => {\n      if (!swiper || swiper.destroyed) return;\n      if (swiper.params.autoplay.reverseDirection) {\n        if (!swiper.isBeginning || swiper.params.loop || swiper.params.rewind) {\n          swiper.slidePrev(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(swiper.slides.length - 1, speed, true, true);\n          emit('autoplay');\n        }\n      } else {\n        if (!swiper.isEnd || swiper.params.loop || swiper.params.rewind) {\n          swiper.slideNext(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, speed, true, true);\n          emit('autoplay');\n        }\n      }\n      if (swiper.params.cssMode) {\n        autoplayStartTime = new Date().getTime();\n        requestAnimationFrame(() => {\n          run();\n        });\n      }\n    };\n    if (delay > 0) {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        proceed();\n      }, delay);\n    } else {\n      requestAnimationFrame(() => {\n        proceed();\n      });\n    }\n\n    // eslint-disable-next-line\n    return delay;\n  };\n  const start = () => {\n    autoplayStartTime = new Date().getTime();\n    swiper.autoplay.running = true;\n    run();\n    emit('autoplayStart');\n  };\n  const stop = () => {\n    swiper.autoplay.running = false;\n    clearTimeout(timeout);\n    cancelAnimationFrame(raf);\n    emit('autoplayStop');\n  };\n  const pause = (internal, reset) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    clearTimeout(timeout);\n    if (!internal) {\n      pausedByInteraction = true;\n    }\n    const proceed = () => {\n      emit('autoplayPause');\n      if (swiper.params.autoplay.waitForTransition) {\n        swiper.wrapperEl.addEventListener('transitionend', onTransitionEnd);\n      } else {\n        resume();\n      }\n    };\n    swiper.autoplay.paused = true;\n    if (reset) {\n      if (slideChanged) {\n        autoplayTimeLeft = swiper.params.autoplay.delay;\n      }\n      slideChanged = false;\n      proceed();\n      return;\n    }\n    const delay = autoplayTimeLeft || swiper.params.autoplay.delay;\n    autoplayTimeLeft = delay - (new Date().getTime() - autoplayStartTime);\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop) return;\n    if (autoplayTimeLeft < 0) autoplayTimeLeft = 0;\n    proceed();\n  };\n  const resume = () => {\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop || swiper.destroyed || !swiper.autoplay.running) return;\n    autoplayStartTime = new Date().getTime();\n    if (pausedByInteraction) {\n      pausedByInteraction = false;\n      run(autoplayTimeLeft);\n    } else {\n      run();\n    }\n    swiper.autoplay.paused = false;\n    emit('autoplayResume');\n  };\n  const onVisibilityChange = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    const document = getDocument();\n    if (document.visibilityState === 'hidden') {\n      pausedByInteraction = true;\n      pause(true);\n    }\n    if (document.visibilityState === 'visible') {\n      resume();\n    }\n  };\n  const onPointerEnter = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByInteraction = true;\n    pausedByPointerEnter = true;\n    if (swiper.animating || swiper.autoplay.paused) return;\n    pause(true);\n  };\n  const onPointerLeave = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByPointerEnter = false;\n    if (swiper.autoplay.paused) {\n      resume();\n    }\n  };\n  const attachMouseEvents = () => {\n    if (swiper.params.autoplay.pauseOnMouseEnter) {\n      swiper.el.addEventListener('pointerenter', onPointerEnter);\n      swiper.el.addEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const detachMouseEvents = () => {\n    if (swiper.el && typeof swiper.el !== 'string') {\n      swiper.el.removeEventListener('pointerenter', onPointerEnter);\n      swiper.el.removeEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const attachDocumentEvents = () => {\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n  };\n  const detachDocumentEvents = () => {\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n  };\n  on('init', () => {\n    if (swiper.params.autoplay.enabled) {\n      attachMouseEvents();\n      attachDocumentEvents();\n      start();\n    }\n  });\n  on('destroy', () => {\n    detachMouseEvents();\n    detachDocumentEvents();\n    if (swiper.autoplay.running) {\n      stop();\n    }\n  });\n  on('_freeModeStaticRelease', () => {\n    if (pausedByTouch || pausedByInteraction) {\n      resume();\n    }\n  });\n  on('_freeModeNoMomentumRelease', () => {\n    if (!swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('beforeTransitionStart', (_s, speed, internal) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (internal || !swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('sliderFirstMove', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.params.autoplay.disableOnInteraction) {\n      stop();\n      return;\n    }\n    isTouched = true;\n    pausedByTouch = false;\n    pausedByInteraction = false;\n    touchStartTimeout = setTimeout(() => {\n      pausedByInteraction = true;\n      pausedByTouch = true;\n      pause(true);\n    }, 200);\n  });\n  on('touchEnd', () => {\n    if (swiper.destroyed || !swiper.autoplay.running || !isTouched) return;\n    clearTimeout(touchStartTimeout);\n    clearTimeout(timeout);\n    if (swiper.params.autoplay.disableOnInteraction) {\n      pausedByTouch = false;\n      isTouched = false;\n      return;\n    }\n    if (pausedByTouch && swiper.params.cssMode) resume();\n    pausedByTouch = false;\n    isTouched = false;\n  });\n  on('slideChange', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    slideChanged = true;\n  });\n  Object.assign(swiper.autoplay, {\n    start,\n    stop,\n    pause,\n    resume\n  });\n}\n\nexport { Autoplay as default };\n", "function effectInit(params) {\n  const {\n    effect,\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams,\n    perspective,\n    recreateShadows,\n    getEffectParams\n  } = params;\n  on('beforeInit', () => {\n    if (swiper.params.effect !== effect) return;\n    swiper.classNames.push(`${swiper.params.containerModifierClass}${effect}`);\n    if (perspective && perspective()) {\n      swiper.classNames.push(`${swiper.params.containerModifierClass}3d`);\n    }\n    const overwriteParamsResult = overwriteParams ? overwriteParams() : {};\n    Object.assign(swiper.params, overwriteParamsResult);\n    Object.assign(swiper.originalParams, overwriteParamsResult);\n  });\n  on('setTranslate _virtualUpdated', () => {\n    if (swiper.params.effect !== effect) return;\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    if (swiper.params.effect !== effect) return;\n    setTransition(duration);\n  });\n  on('transitionEnd', () => {\n    if (swiper.params.effect !== effect) return;\n    if (recreateShadows) {\n      if (!getEffectParams || !getEffectParams().slideShadows) return;\n      // remove shadows\n      swiper.slides.forEach(slideEl => {\n        slideEl.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => shadowEl.remove());\n      });\n      // create new one\n      recreateShadows();\n    }\n  });\n  let requireUpdateOnVirtual;\n  on('virtualUpdate', () => {\n    if (swiper.params.effect !== effect) return;\n    if (!swiper.slides.length) {\n      requireUpdateOnVirtual = true;\n    }\n    requestAnimationFrame(() => {\n      if (requireUpdateOnVirtual && swiper.slides && swiper.slides.length) {\n        setTranslate();\n        requireUpdateOnVirtual = false;\n      }\n    });\n  });\n}\n\nexport { effectInit as e };\n", "import { g as getSlideTransformEl } from './utils.mjs';\n\nfunction effectTarget(effectParams, slideEl) {\n  const transformEl = getSlideTransformEl(slideEl);\n  if (transformEl !== slideEl) {\n    transformEl.style.backfaceVisibility = 'hidden';\n    transformEl.style['-webkit-backface-visibility'] = 'hidden';\n  }\n  return transformEl;\n}\n\nexport { effectTarget as e };\n", "import { g as getSlideTransformEl, c as createElement } from './utils.mjs';\n\nfunction createShadow(suffix, slideEl, side) {\n  const shadowClass = `swiper-slide-shadow${side ? `-${side}` : ''}${suffix ? ` swiper-slide-shadow-${suffix}` : ''}`;\n  const shadowContainer = getSlideTransformEl(slideEl);\n  let shadowEl = shadowContainer.querySelector(`.${shadowClass.split(' ').join('.')}`);\n  if (!shadowEl) {\n    shadowEl = createElement('div', shadowClass.split(' '));\n    shadowContainer.append(shadowEl);\n  }\n  return shadowEl;\n}\n\nexport { createShadow as c };\n", "import { c as createShadow } from '../shared/create-shadow.mjs';\nimport { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { g as getSlideTransformEl, p as getRotateFix } from '../shared/utils.mjs';\n\nfunction EffectCoverflow(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    coverflowEffect: {\n      rotate: 50,\n      stretch: 0,\n      depth: 100,\n      scale: 1,\n      modifier: 1,\n      slideShadows: true\n    }\n  });\n  const setTranslate = () => {\n    const {\n      width: swiperWidth,\n      height: swiperHeight,\n      slides,\n      slidesSizesGrid\n    } = swiper;\n    const params = swiper.params.coverflowEffect;\n    const isHorizontal = swiper.isHorizontal();\n    const transform = swiper.translate;\n    const center = isHorizontal ? -transform + swiperWidth / 2 : -transform + swiperHeight / 2;\n    const rotate = isHorizontal ? params.rotate : -params.rotate;\n    const translate = params.depth;\n    const r = getRotateFix(swiper);\n    // Each slide offset from center\n    for (let i = 0, length = slides.length; i < length; i += 1) {\n      const slideEl = slides[i];\n      const slideSize = slidesSizesGrid[i];\n      const slideOffset = slideEl.swiperSlideOffset;\n      const centerOffset = (center - slideOffset - slideSize / 2) / slideSize;\n      const offsetMultiplier = typeof params.modifier === 'function' ? params.modifier(centerOffset) : centerOffset * params.modifier;\n      let rotateY = isHorizontal ? rotate * offsetMultiplier : 0;\n      let rotateX = isHorizontal ? 0 : rotate * offsetMultiplier;\n      // var rotateZ = 0\n      let translateZ = -translate * Math.abs(offsetMultiplier);\n      let stretch = params.stretch;\n      // Allow percentage to make a relative stretch for responsive sliders\n      if (typeof stretch === 'string' && stretch.indexOf('%') !== -1) {\n        stretch = parseFloat(params.stretch) / 100 * slideSize;\n      }\n      let translateY = isHorizontal ? 0 : stretch * offsetMultiplier;\n      let translateX = isHorizontal ? stretch * offsetMultiplier : 0;\n      let scale = 1 - (1 - params.scale) * Math.abs(offsetMultiplier);\n\n      // Fix for ultra small values\n      if (Math.abs(translateX) < 0.001) translateX = 0;\n      if (Math.abs(translateY) < 0.001) translateY = 0;\n      if (Math.abs(translateZ) < 0.001) translateZ = 0;\n      if (Math.abs(rotateY) < 0.001) rotateY = 0;\n      if (Math.abs(rotateX) < 0.001) rotateX = 0;\n      if (Math.abs(scale) < 0.001) scale = 0;\n      const slideTransform = `translate3d(${translateX}px,${translateY}px,${translateZ}px)  rotateX(${r(rotateX)}deg) rotateY(${r(rotateY)}deg) scale(${scale})`;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = slideTransform;\n      slideEl.style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n      if (params.slideShadows) {\n        // Set shadows\n        let shadowBeforeEl = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n        let shadowAfterEl = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n        if (!shadowBeforeEl) {\n          shadowBeforeEl = createShadow('coverflow', slideEl, isHorizontal ? 'left' : 'top');\n        }\n        if (!shadowAfterEl) {\n          shadowAfterEl = createShadow('coverflow', slideEl, isHorizontal ? 'right' : 'bottom');\n        }\n        if (shadowBeforeEl) shadowBeforeEl.style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0;\n        if (shadowAfterEl) shadowAfterEl.style.opacity = -offsetMultiplier > 0 ? -offsetMultiplier : 0;\n      }\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n  };\n  effectInit({\n    effect: 'coverflow',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => true,\n    overwriteParams: () => ({\n      watchSlidesProgress: true\n    })\n  });\n}\n\nexport { EffectCoverflow as default };\n", "/**\n * Application constants to eliminate magic numbers\n */\n\n// Mobile detection constants\nexport const M<PERSON><PERSON><PERSON>_DETECTION = {\n  USER_AGENT_SUBSTR_START: 0,\n  USER_AGENT_SUBSTR_LENGTH: 4,\n} as const;\n\n// Swiper configuration constants\nexport const SWIPER_CONFIG = {\n  MOBILE: {\n    SPACE_BETWEEN: 90,\n    SLIDES_PER_VIEW: 2,\n    TAG_SPACE_BETWEEN: 80,\n    TAG_SLIDES_PER_VIEW: 4,\n  },\n  DESKTOP: {\n    SPACE_BETWEEN: 10,\n    SLIDES_PER_VIEW: 7,\n    TAG_SPACE_BETWEEN: 10,\n    TAG_SLIDES_PER_VIEW: 7,\n  },\n  AUTOPLAY_DELAY: 3000,\n} as const;\n\n// Error handling constants\nexport const ERROR_HANDLING = {\n  MAX_ERROR_LOG_ENTRIES: 50,\n  DOM_READY_TIMEOUT: 5000,\n  SLIDE_NUMBER_MIN: 1,\n  SLIDE_NUMBER_MAX: 30,\n  TRANSITION_TIME_MIN: 1000,\n  TRANSITION_TIME_MAX: 30_000,\n  CONFIG_MAX_SLIDES_MIN: 1,\n  CONFIG_MAX_SLIDES_MAX: 50,\n} as const;\n\n// Admin component constants\nexport const ADMIN_CONSTANTS = {\n  SAVE_DEBOUNCE_DELAY: 500,\n  DEFAULT_MAX_SLIDES: 30,\n  EMPTY_SLIDES_COUNT: 0,\n} as const;\n\n// UI styling constants\nexport const UI_STYLES = {\n  HEADER_ICON_HEIGHT: 24,\n  HEADER_ICON_MARGIN_TOP: 8,\n  MOBILE_BUTTON_FONT_SIZE: 14,\n  MOBILE_BUTTON_WORD_SPACING: -1,\n  SOCIAL_ICON_WIDTH: 32,\n  SOCIAL_ICON_MARGIN_LEFT: 20,\n  TAG_TEXT_FONT_SIZE: 14,\n  TAG_CONTAINER_PADDING_TOP: 10,\n  TAG_CONTAINER_MARGIN_TOP: 5,\n} as const;\n\n// Mobile layout constants\nexport const MOBILE_LAYOUT = {\n  SCREEN_WIDTH_MULTIPLIER: 2,\n  SCREEN_WIDTH_OFFSET: 50,\n  CONTAINER_MARGIN_MULTIPLIER: 0.254,\n} as const;\n\n// Slideshow constants\nexport const SLIDESHOW_CONSTANTS = {\n  SLIDE_INCREMENT: 1,\n  INITIAL_SLIDE_INDEX: 1,\n  VALIDATION_ERRORS_EMPTY: 0,\n} as const;\n\n// Array and index constants\nexport const ARRAY_CONSTANTS = {\n  EMPTY_LENGTH: 0,\n  FIRST_INDEX: 0,\n  NOT_FOUND_INDEX: -1,\n  NEXT_ITEM_OFFSET: 1,\n  LAST_ITEM_OFFSET: -1,\n} as const;\n\n// JSON formatting constants\nexport const JSON_CONSTANTS = {\n  INDENT_SIZE: 2,\n} as const;\n\n// Timing constants\nexport const TIMING = {\n  CHECK_INTERVAL: 10,\n  DATA_CHECK_INTERVAL: 100,\n  DEFAULT_TRANSITION_TIME: 5000,\n} as const;\n\n// DOM element constants\nexport const DOM_ELEMENTS = {\n  SWIPER_AD_CONTAINER_ID: 'swiperAdContainer',\n  SWIPER_TAG_CONTAINER_ID: 'swiperTagContainer',\n  SWIPER_TAG_WRAPPER_ID: 'swiperTagWrapper',\n  HEADER_ICON_ID: 'wusong8899Client1HeaderIcon',\n} as const;\n\n// CSS class constants\nexport const CSS_CLASSES = {\n  SWIPER: 'swiper',\n  SWIPER_WRAPPER: 'swiper-wrapper',\n  SWIPER_SLIDE: 'swiper-slide',\n  SWIPER_SLIDE_TAG: 'swiper-slide-tag',\n  SWIPER_SLIDE_TAG_INNER: 'swiper-slide-tag-inner',\n  SWIPER_SLIDE_TAG_INNER_MOBILE: 'swiper-slide-tag-inner-mobile',\n  SWIPER_BUTTON_NEXT: 'swiper-button-next',\n  SWIPER_BUTTON_PREV: 'swiper-button-prev',\n  SWIPER_PAGINATION: 'swiper-pagination',\n  AD_SWIPER: 'adSwiper',\n  TAG_SWIPER: 'tagSwiper',\n  TAG_TILES: 'TagTiles',\n  TAG_TILE: 'TagTile',\n  TAG_TILE_NAME: 'TagTile-name',\n  TAG_TILE_DESCRIPTION: 'TagTile-description',\n  TAG_TEXT_OUTER_CONTAINER: 'TagTextOuterContainer',\n  TAG_TEXT_CONTAINER: 'TagTextContainer',\n  TAG_TEXT_ICON: 'TagTextIcon',\n  BUTTON_REGISTER: 'buttonRegister',\n} as const;\n\n// CSS selector constants\nexport const CSS_SELECTORS = {\n  APP_NAVIGATION_BACK_CONTROL: '#app-navigation .App-backControl',\n  CONTENT_CONTAINER: '#content .container',\n  TAGS_PAGE_CONTENT: '#content .container .TagsPage-content',\n  NEW_DISCUSSION_BUTTON: '.item-newDiscussion .Button-label',\n  NEW_DISCUSSION_ICON: '.item-newDiscussion i',\n  NAV_ITEMS: '.item-nav',\n  APP_CONTENT: '.App-content',\n  SWIPER_PAGINATION_EL: '.swiper-pagination',\n  SWIPER_BUTTON_NEXT_EL: '.swiper-button-next',\n  SWIPER_BUTTON_PREV_EL: '.swiper-button-prev',\n} as const;\n\n// Extension configuration constants\nexport const EXTENSION_CONFIG = {\n  ID: 'wusong8899-client1-header-adv',\n  TRANSLATION_PREFIX: 'wusong8899-client1',\n  MAX_SLIDES: 30,\n  HEADER_ICON_URL: 'https://ex.cc/assets/files/date/test.png',\n} as const;\n\n// Social media platform constants\nexport const SOCIAL_PLATFORMS = [\n  'Kick',\n  'Facebook',\n  'Twitter',\n  'YouTube',\n  'Instagram'\n] as const;\n\nexport type SocialPlatform = typeof SOCIAL_PLATFORMS[number];\n", "/**\n * DOM manipulation utilities - functional approach\n */\n\nimport { ERROR_HANDLING } from '../../common/config/constants';\n\n/**\n * Create a DOM element with specified attributes\n * @param tagName - HTML tag name\n * @param attributes - Element attributes\n * @param innerHTML - Inner HTML content\n * @returns Created element\n */\nexport const createElement = (\n    tagName: string,\n    attributes: Record<string, string> = {},\n    innerHTML = ''\n): HTMLElement => {\n    const element = document.createElement(tagName);\n\n    for (const [key, value] of Object.entries(attributes)) {\n        if (key === 'className') {\n            element.className = value;\n        } else if (key === 'style') {\n            element.setAttribute('style', value);\n        } else {\n            element.setAttribute(key, value);\n        }\n    }\n\n    if (innerHTML) {\n        element.innerHTML = innerHTML;\n    }\n\n    return element;\n};\n\n/**\n * Safely get element by ID\n * @param id - Element ID\n * @returns Element or null if not found\n */\nexport const getElementById = (id: string): HTMLElement | null =>\n    document.getElementById(id);\n\n/**\n * Safely query selector\n * @param selector - CSS selector\n * @param parent - Parent element (default: document)\n * @returns Element or null if not found\n */\nexport const querySelector = (selector: string, parent: Element | Document = document): Element | null => {\n    try {\n        if (!parent || !selector) {\n            throw new Error('Invalid selector or parent');\n        }\n        return parent.querySelector(selector);\n    } catch {\n        return document.querySelector('') as null; // Return null safely\n    }\n};\n\n/**\n * Safely query all elements\n * @param selector - CSS selector\n * @param parent - Parent element (default: document)\n * @returns NodeList of elements\n */\nexport const querySelectorAll = (selector: string, parent: Element | Document = document): NodeListOf<Element> => {\n    try {\n        if (!parent || !selector) {\n            return document.querySelectorAll(''); // Return empty NodeList\n        }\n        return parent.querySelectorAll(selector);\n    } catch {\n        return document.querySelectorAll(''); // Return empty NodeList\n    }\n};\n\n/**\n * Add event listener with error handling\n * @param element - Target element\n * @param event - Event type\n * @param handler - Event handler\n */\nexport const addEventListener = (\n    element: Element,\n    event: string,\n    handler: EventListener\n): void => {\n    try {\n        if (element && event && handler) {\n            element.addEventListener(event, handler);\n        }\n    } catch {\n        // Silently handle event listener errors\n    }\n};\n\n/**\n * Remove event listener with error handling\n * @param element - Target element\n * @param event - Event type\n * @param handler - Event handler\n */\nexport const removeEventListener = (\n    element: Element,\n    event: string,\n    handler: EventListener\n): void => {\n    try {\n        if (element && event && handler) {\n            element.removeEventListener(event, handler);\n        }\n    } catch {\n        // Silently handle event listener removal errors\n    }\n};\n\n/**\n * Set CSS styles on element\n * @param element - Target element\n * @param styles - Style properties\n */\nexport const setStyles = (element: HTMLElement, styles: Record<string, string>): void => {\n    if (!element || !styles) {\n        return;\n    }\n    for (const [property, value] of Object.entries(styles)) {\n        try {\n            element.style.setProperty(property, value);\n        } catch {\n            // Silently handle style setting errors\n        }\n    }\n};\n\n/**\n * Append element to parent with error handling\n * @param parent - Parent element\n * @param child - Child element to append\n */\nexport const appendChild = (parent: Element, child: Element): void => {\n    try {\n        if (parent && child) {\n            parent.appendChild(child);\n        }\n    } catch {\n        // Silently handle append errors\n    }\n};\n\n/**\n * Prepend element to parent with error handling\n * @param parent - Parent element\n * @param child - Child element to prepend\n */\nexport const prependChild = (parent: Element, child: Element): void => {\n    try {\n        if (parent && child && parent.firstChild) {\n            parent.firstChild.before(child);\n        }\n    } catch {\n        // Silently handle prepend errors\n    }\n};\n\n/**\n * Remove element safely\n * @param element - Element to remove\n */\nexport const removeElement = (element: Element): void => {\n    try {\n        if (element && element.parentNode) {\n            element.parentNode.removeChild(element);\n        }\n    } catch {\n        // Silently handle element removal errors\n    }\n};\n\n/**\n * Check if element is visible\n * @param element - Element to check\n * @returns True if element is visible\n */\nexport const isElementVisible = (element: Element): boolean => {\n    try {\n        if (!element) {\n            return false;\n        }\n        const style = globalThis.getComputedStyle(element);\n        return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';\n    } catch {\n        return false;\n    }\n};\n\n/**\n * Get element dimensions safely\n * @param element - Element to measure\n * @returns Element dimensions or undefined\n */\nexport const getElementDimensions = (element: Element): { width: number; height: number } | undefined => {\n    try {\n        if (!element) {\n            throw new Error('Invalid element');\n        }\n        const rect = element.getBoundingClientRect();\n        return {\n            width: rect.width,\n            height: rect.height\n        };\n    } catch {\n        return; // Return undefined implicitly\n    }\n};\n\n/**\n * Scroll element into view safely\n * @param element - Element to scroll to\n * @param behavior - Scroll behavior\n */\nexport const scrollIntoView = (element: Element, behavior: ScrollBehavior = 'smooth'): void => {\n    try {\n        if (element && element.scrollIntoView) {\n            element.scrollIntoView({ behavior, block: 'nearest' });\n        }\n    } catch {\n        // Silently handle scroll errors\n    }\n};\n\n/**\n * Wait for element to appear in DOM\n * @param selector - CSS selector\n * @param timeout - Timeout in milliseconds\n * @returns Promise that resolves with element or null\n */\nexport const waitForElement = (selector: string, timeout = ERROR_HANDLING.DOM_READY_TIMEOUT): Promise<Element | null> =>\n    new Promise((resolve) => {\n        const element = querySelector(selector);\n        if (element) {\n            resolve(element);\n            return;\n        }\n\n        const observer = new MutationObserver(() => {\n            const foundElement = querySelector(selector);\n            if (foundElement) {\n                observer.disconnect();\n                resolve(foundElement);\n            }\n        });\n\n        observer.observe(document.body, {\n            childList: true,\n            subtree: true\n        });\n\n        setTimeout(() => {\n            observer.disconnect();\n            resolve(document.querySelector('') as null); // Return null safely\n        }, timeout);\n    });\n", "import type { EventType, MobileConfig } from '../../common/config/types';\nimport { MOBILE_DETECTION, SWIPER_CONFIG } from '../../common/config/constants';\n\n/**\n * Mobile device detection utility - functional approach\n */\n\n// Cache for mobile detection result\nconst mobileDetectionState = {\n    cached: false,\n    isMobile: false\n};\n\n/**\n * Check if the current device is mobile\n * @returns True if mobile device\n */\nexport const isMobileDevice = (): boolean => {\n    if (mobileDetectionState.cached) {\n        return mobileDetectionState.isMobile;\n    }\n\n    let check = false;\n    const userAgent = navigator.userAgent || navigator.vendor || (globalThis as unknown as { opera?: string }).opera;\n\n    // Mobile detection regex\n    const mobileRegex = /(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i;\n\n    const mobileRegex2 = /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i;\n\n    const userAgentSubstr = userAgent.substr(\n        MOBILE_DETECTION.USER_AGENT_SUBSTR_START,\n        MOBILE_DETECTION.USER_AGENT_SUBSTR_LENGTH\n    );\n\n    if (mobileRegex.test(userAgent) || mobileRegex2.test(userAgentSubstr)) {\n        check = true;\n    }\n\n    mobileDetectionState.isMobile = check;\n    mobileDetectionState.cached = true;\n    return check;\n};\n\n/**\n * Get event type based on device\n * @returns 'touchend' for mobile, 'click' for desktop\n */\nexport const getEventType = (): EventType => {\n    if (isMobileDevice()) {\n        return 'touchend';\n    }\n    return 'click';\n};\n\n/**\n * Get responsive configuration for Swiper\n * @returns Configuration object with mobile-specific settings\n */\nexport const getSwiperConfig = (): MobileConfig => {\n    const isMobile = isMobileDevice();\n\n    if (isMobile) {\n        return {\n            spaceBetween: SWIPER_CONFIG.MOBILE.SPACE_BETWEEN,\n            slidesPerView: SWIPER_CONFIG.MOBILE.SLIDES_PER_VIEW,\n        };\n    }\n\n    return {\n        spaceBetween: SWIPER_CONFIG.DESKTOP.SPACE_BETWEEN,\n        slidesPerView: SWIPER_CONFIG.DESKTOP.SLIDES_PER_VIEW,\n    };\n};\n\n/**\n * Get responsive configuration for tag Swiper\n * @returns Configuration object with mobile-specific settings\n */\nexport const getTagSwiperConfig = (): MobileConfig => {\n    const isMobile = isMobileDevice();\n\n    if (isMobile) {\n        return {\n            spaceBetween: SWIPER_CONFIG.MOBILE.TAG_SPACE_BETWEEN,\n            slidesPerView: SWIPER_CONFIG.MOBILE.TAG_SLIDES_PER_VIEW,\n        };\n    }\n\n    return {\n        spaceBetween: SWIPER_CONFIG.DESKTOP.TAG_SPACE_BETWEEN,\n        slidesPerView: SWIPER_CONFIG.DESKTOP.TAG_SLIDES_PER_VIEW,\n    };\n};\n\n/**\n * Reset mobile detection cache (useful for testing)\n */\nexport const resetMobileDetectionCache = (): void => {\n    mobileDetectionState.cached = false;\n    mobileDetectionState.isMobile = false;\n};\n", "import type { RootConfig, Environment } from './types';\nimport {\n  EXTENSION_CONFIG,\n  TIMING,\n  DOM_ELEMENTS,\n  CSS_CLASSES,\n  CSS_SELECTORS\n} from './constants';\n\nexport const defaultConfig: RootConfig = {\n  env: (process.env.NODE_ENV as Environment) || 'production',\n  app: {\n    extensionId: EXTENSION_CONFIG.ID,\n    translationPrefix: EXTENSION_CONFIG.TRANSLATION_PREFIX,\n  },\n  slider: {\n    maxSlides: EXTENSION_CONFIG.MAX_SLIDES,\n    defaultTransitionTime: TIMING.DEFAULT_TRANSITION_TIME,\n    checkTime: TIMING.CHECK_INTERVAL,\n    dataCheckInterval: TIMING.DATA_CHECK_INTERVAL,\n    dom: {\n      containerId: DOM_ELEMENTS.SWIPER_AD_CONTAINER_ID,\n      swiperClass: CSS_CLASSES.AD_SWIPER,\n    },\n    swiper: {\n      spaceBetween: 30,\n      effect: 'coverflow',\n      centeredSlides: true,\n      slidesPerView: 2,\n      coverflowEffect: {\n        rotate: 0,\n        depth: 100,\n        modifier: 1,\n        slideShadows: true,\n        stretch: 0,\n      },\n      pagination: {\n        el: CSS_SELECTORS.SWIPER_PAGINATION_EL,\n        type: 'bullets',\n      },\n      navigation: {\n        nextEl: CSS_SELECTORS.SWIPER_BUTTON_NEXT_EL,\n        prevEl: CSS_SELECTORS.SWIPER_BUTTON_PREV_EL,\n      },\n    },\n  },\n  ui: {\n    headerIconId: DOM_ELEMENTS.HEADER_ICON_ID,\n    headerIconUrl: EXTENSION_CONFIG.HEADER_ICON_URL,\n  },\n  data: {\n    apiResources: {\n      // Links queue functionality moved to client1-links-queue extension\n    },\n  },\n};\n\n", "import Swiper from 'swiper';\nimport { EffectCoverflow, Navigation, Pagination, Autoplay } from 'swiper/modules';\nimport app from 'flarum/forum/app';\nimport * as DOMUtils from '../utils/dom-utils';\nimport { isMobileDevice } from '../utils/mobile-detection';\nimport { defaultConfig } from '../../common/config';\nimport { MOBILE_LAYOUT, SLIDESHOW_CONSTANTS } from '../../common/config/constants';\nimport type { FlarumVnode } from '../../common/config/types';\n\n/**\n * Slideshow manager for header advertisements\n */\nexport class SlideshowManager {\n    private swiper: Swiper | undefined;\n    private container: HTMLElement | undefined;\n    private readonly maxSlides = defaultConfig.slider.maxSlides;\n    private readonly checkTime = defaultConfig.slider.checkTime;\n\n    /**\n     * Safely read a forum attribute if available\n     */\n    private getForumAttribute(key: string): unknown {\n        try {\n            const forum = app && app.forum;\n            const attrFn = forum && forum.attribute;\n            if (typeof attrFn === 'function') {\n                return attrFn.call(forum, key);\n            }\n            return;\n        } catch {\n            return;\n        }\n    }\n\n    /**\n     * Initialize and attach slideshow to the DOM\n     */\n    attachAdvertiseHeader(_vdom: FlarumVnode): void {\n        try {\n            this.destroy(); // Clean up any existing instance\n\n            const container = this.createContainer();\n            const swiper = this.createSwiperElement(container);\n            const wrapper = this.createSwiperWrapper(swiper);\n\n            this.populateSlides(wrapper);\n            this.createPagination(swiper);\n            this.createNavigation(swiper);\n\n            this.container = container;\n            this.appendToDOM(container);\n\n            // Initialize Swiper after DOM attachment\n            setTimeout(() => {\n                this.initializeSwiper(this.getTransitionTime());\n            }, this.checkTime);\n        } catch {\n            // Silently handle slideshow creation errors\n        }\n    }\n\n    /**\n     * Remove existing navigation elements\n     */\n    private removeExistingNavigation(): void {\n        const existingContainer = DOMUtils.querySelector(`#${defaultConfig.slider.dom.containerId}`);\n        if (existingContainer) {\n            DOMUtils.removeElement(existingContainer);\n        }\n\n        const navElements = DOMUtils.querySelectorAll(\".item-nav\");\n        for (const element of navElements) {\n            DOMUtils.removeElement(element);\n        }\n    }\n\n    /**\n     * Create main container element\n     * @returns Container element\n     */\n    private createContainer(): HTMLElement {\n        this.removeExistingNavigation();\n\n        const container = DOMUtils.createElement('div', {\n            id: defaultConfig.slider.dom.containerId,\n            className: 'adContainer'\n        });\n\n        this.applyMobileStyles(container);\n        return container;\n    }\n\n    /**\n     * Apply mobile-specific styles if needed\n     * @param container - Container element\n     */\n    private applyMobileStyles(container: HTMLElement): void {\n        if (isMobileDevice()) {\n            const screenWidth = globalThis.innerWidth;\n            const styleWidth = screenWidth * MOBILE_LAYOUT.SCREEN_WIDTH_MULTIPLIER - MOBILE_LAYOUT.SCREEN_WIDTH_OFFSET;\n            DOMUtils.setStyles(container, {\n                'width': `${styleWidth}px`,\n                'margin-left': `${-(styleWidth * MOBILE_LAYOUT.CONTAINER_MARGIN_MULTIPLIER)}px`\n            });\n        }\n    }\n\n    /**\n     * Create Swiper element\n     * @param {HTMLElement} container - Parent container\n     * @returns {HTMLElement} Swiper element\n     */\n    private createSwiperElement(container: HTMLElement): HTMLElement {\n        const swiper = DOMUtils.createElement('div', {\n            className: `swiper ${defaultConfig.slider.dom.swiperClass}`\n        });\n        DOMUtils.appendChild(container, swiper);\n        return swiper;\n    }\n\n    /**\n     * Create Swiper wrapper\n     * @param {HTMLElement} swiper - Swiper element\n     * @returns {HTMLElement} Wrapper element\n     */\n    private createSwiperWrapper(swiper: HTMLElement): HTMLElement {\n        const wrapper = DOMUtils.createElement('div', {\n            className: 'swiper-wrapper'\n        });\n        DOMUtils.appendChild(swiper, wrapper);\n        return wrapper;\n    }\n\n    /**\n     * Get transition time from forum settings\n     * @returns Transition time in milliseconds\n     */\n    private getTransitionTime(): number {\n        const transitionTime = this.getForumAttribute('Client1HeaderAdvTransitionTime');\n        if (transitionTime) {\n            return Number.parseInt(String(transitionTime), 10);\n        }\n        return defaultConfig.slider.defaultTransitionTime;\n    }\n\n    /**\n     * Populate slides with data from forum settings\n     * @param {HTMLElement} wrapper - Swiper wrapper element\n     */\n    private populateSlides(wrapper: HTMLElement): void {\n        for (let slideIndex = SLIDESHOW_CONSTANTS.INITIAL_SLIDE_INDEX; slideIndex <= this.maxSlides; slideIndex += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT) {\n            const imageSrc = this.getForumAttribute(`Client1HeaderAdvImage${slideIndex}`);\n            const imageLink = this.getForumAttribute(`Client1HeaderAdvLink${slideIndex}`);\n\n            if (imageSrc) {\n                const slide = this.createSlide(String(imageSrc), String(imageLink || ''));\n                DOMUtils.appendChild(wrapper, slide);\n            }\n        }\n    }\n\n    /**\n     * Create individual slide\n     * @param {string} imageSrc - Image source URL\n     * @param {string} imageLink - Link URL\n     * @returns {HTMLElement} Slide element\n     */\n    private createSlide(imageSrc: string, imageLink: string): HTMLElement {\n        const slide = DOMUtils.createElement('div', {\n            className: 'swiper-slide'\n        });\n\n        let clickHandler = '';\n        if (imageLink) {\n            clickHandler = `window.location.href=\"${imageLink}\"`;\n        }\n        slide.innerHTML = `<img onclick='${clickHandler}' src='${imageSrc}' />`;\n\n        return slide;\n    }\n\n    /**\n     * Create pagination element\n     * @param {HTMLElement} swiper - Swiper element\n     */\n    private createPagination(swiper: HTMLElement): void {\n        const pagination = DOMUtils.createElement('div', {\n            className: 'swiper-pagination'\n        });\n        DOMUtils.appendChild(swiper, pagination);\n    }\n\n    /**\n     * Create navigation elements\n     * @param {HTMLElement} swiper - Swiper element\n     */\n    private createNavigation(swiper: HTMLElement): void {\n        const prevButton = DOMUtils.createElement('div', {\n            className: 'swiper-button-prev'\n        });\n        const nextButton = DOMUtils.createElement('div', {\n            className: 'swiper-button-next'\n        });\n\n        DOMUtils.appendChild(swiper, prevButton);\n        DOMUtils.appendChild(swiper, nextButton);\n    }\n\n    /**\n     * Append slideshow to DOM\n     * @param {HTMLElement} container - Container element\n     */\n    private appendToDOM(container: HTMLElement): void {\n        const contentContainer = DOMUtils.querySelector(\"#content .container\");\n        if (contentContainer) {\n            DOMUtils.prependChild(contentContainer, container);\n        }\n    }\n\n    /**\n     * Initialize Swiper instance\n     * @param {number} transitionTime - Transition time in milliseconds\n     */\n    private initializeSwiper(transitionTime: number): void {\n        try {\n            this.swiper = new Swiper(`.${defaultConfig.slider.dom.swiperClass}`, {\n                autoplay: {\n                    delay: transitionTime,\n                },\n                spaceBetween: defaultConfig.slider.swiper.spaceBetween,\n                effect: defaultConfig.slider.swiper.effect,\n                centeredSlides: defaultConfig.slider.swiper.centeredSlides,\n                slidesPerView: defaultConfig.slider.swiper.slidesPerView,\n                coverflowEffect: {\n                    rotate: defaultConfig.slider.swiper.coverflowEffect.rotate,\n                    depth: defaultConfig.slider.swiper.coverflowEffect.depth,\n                    modifier: defaultConfig.slider.swiper.coverflowEffect.modifier,\n                    slideShadows: defaultConfig.slider.swiper.coverflowEffect.slideShadows,\n                    stretch: defaultConfig.slider.swiper.coverflowEffect.stretch,\n                },\n                pagination: {\n                    el: defaultConfig.slider.swiper.pagination.el,\n                    type: defaultConfig.slider.swiper.pagination.type,\n                },\n                navigation: {\n                    nextEl: defaultConfig.slider.swiper.navigation.nextEl,\n                    prevEl: defaultConfig.slider.swiper.navigation.prevEl,\n                },\n                modules: [EffectCoverflow, Navigation, Pagination, Autoplay]\n            });\n        } catch {\n            // Silently handle Swiper initialization errors\n        }\n    }\n\n    /**\n     * Destroy slideshow instance\n     */\n    destroy(): void {\n        if (this.swiper) {\n            this.swiper.destroy(true, true);\n            delete this.swiper;\n        }\n\n        if (this.container) {\n            DOMUtils.removeElement(this.container);\n            delete this.container;\n        }\n    }\n}\n", "import Swiper from 'swiper';\nimport { Autoplay } from 'swiper/modules';\nimport app from 'flarum/forum/app';\nimport * as DOMUtils from '../utils/dom-utils';\nimport { isMobileDevice, getSwiperConfig } from '../utils/mobile-detection';\nimport { ARRAY_CONSTANTS } from '../../common/config/constants';\nimport type { TagData } from '../../common/config/types';\n\n/**\n * UI Manager for handling various UI components\n */\nexport class UIManager {\n\n    /**\n     * Change category layout to swiper-based layout\n     */\n    changeCategoryLayout(): void {\n        try {\n            if (DOMUtils.getElementById(\"swiperTagContainer\")) {\n                return; // Already exists\n            }\n\n            const tagTiles = DOMUtils.querySelectorAll(\".TagTile\");\n            if (tagTiles.length === ARRAY_CONSTANTS.EMPTY_LENGTH) {\n                return;\n            }\n\n            const container = this.createTagSwiperContainer();\n            if (!container) {\n                return;\n            }\n\n            const swiper = this.createTagSwiper(container);\n            if (!swiper) {\n                return;\n            }\n\n            const wrapper = this.createTagSwiperWrapper(swiper);\n            if (!wrapper) {\n                return;\n            }\n\n            this.populateTagSlides(wrapper, tagTiles);\n            this.appendTagContainer(container);\n            this.addTagSwiperContent(container);\n            this.removeOriginalTagTiles();\n            this.setupMobileStyles();\n            this.initializeTagSwiper();\n        } catch {\n            // Silently handle category layout errors\n        }\n    }\n\n    /**\n     * Create tag swiper container\n     */\n    private createTagSwiperContainer(): HTMLElement {\n        const container = DOMUtils.createElement('div', {\n            className: 'swiperTagContainer',\n            id: 'swiperTagContainer'\n        });\n\n        const textContainer = DOMUtils.createElement('div', {\n            className: 'TagTextOuterContainer'\n        });\n\n        DOMUtils.appendChild(container, textContainer);\n        return container;\n    }\n\n    /**\n     * Create tag swiper element\n     */\n    private createTagSwiper(container: HTMLElement): HTMLElement {\n        const textContainer = container.querySelector('.TagTextOuterContainer');\n        const swiper = DOMUtils.createElement('div', {\n            className: 'swiper tagSwiper'\n        });\n\n        if (textContainer) {\n            DOMUtils.appendChild(textContainer, swiper);\n        }\n\n        return swiper;\n    }\n\n    /**\n     * Create tag swiper wrapper\n     */\n    private createTagSwiperWrapper(swiper: HTMLElement): HTMLElement {\n        const wrapper = DOMUtils.createElement('div', {\n            className: 'swiper-wrapper',\n            id: 'swiperTagWrapper'\n        });\n        DOMUtils.appendChild(swiper, wrapper);\n        return wrapper;\n    }\n\n    /**\n     * Populate tag slides\n     */\n    private populateTagSlides(wrapper: HTMLElement, tagTiles: NodeListOf<Element>): void {\n        const isMobile = isMobileDevice();\n\n        for (const tag of tagTiles) {\n            const tagElement = tag as HTMLElement;\n            const tagData = this.extractTagData(tagElement);\n\n            if (tagData) {\n                const slide = this.createTagSlide(tagData, isMobile);\n                DOMUtils.appendChild(wrapper, slide);\n            }\n        }\n    }\n\n    /**\n     * Extract tag data from DOM element\n     */\n    private extractTagData(tag: HTMLElement): TagData | void {\n        const linkElement = tag.querySelector('a') as HTMLAnchorElement;\n        const nameElement = tag.querySelector('.TagTile-name') as HTMLElement;\n        const descElement = tag.querySelector('.TagTile-description') as HTMLElement;\n\n        if (!linkElement || !nameElement) {\n            return;\n        }\n\n        // Get background from flarum-tag-background plugin or fallback to computed style\n        const backgroundImage = this.getTagBackgroundImage(linkElement.href, tag);\n        const computedStyle = globalThis.getComputedStyle(tag);\n        const background = backgroundImage || computedStyle.background;\n\n        let description = '';\n        let descColor = '';\n        if (descElement) {\n            description = descElement.textContent || '';\n            descColor = globalThis.getComputedStyle(descElement).color;\n        }\n\n        return {\n            url: linkElement.href,\n            background: background,\n            name: nameElement.textContent || '',\n            nameColor: globalThis.getComputedStyle(nameElement).color,\n            description,\n            descColor\n        };\n    }\n\n    /**\n     * Get tag background image from flarum-tag-background plugin\n     */\n    private getTagBackgroundImage(tagUrl: string, tagElement: HTMLElement): string | void {\n        try {\n            // Extract tag slug from URL\n            const url = new URL(tagUrl, globalThis.location.origin);\n            const parts = url.pathname.split('/').filter(Boolean);\n            const tIndex = parts.indexOf('t');\n            const tagsIndex = parts.indexOf('tags');\n\n            let slug = '';\n\n            if (tIndex !== ARRAY_CONSTANTS.NOT_FOUND_INDEX && parts[tIndex + ARRAY_CONSTANTS.NEXT_ITEM_OFFSET]) {\n                slug = parts[tIndex + ARRAY_CONSTANTS.NEXT_ITEM_OFFSET];\n            } else if (tagsIndex !== ARRAY_CONSTANTS.NOT_FOUND_INDEX && parts[tagsIndex + ARRAY_CONSTANTS.NEXT_ITEM_OFFSET]) {\n                slug = parts[tagsIndex + ARRAY_CONSTANTS.NEXT_ITEM_OFFSET];\n            } else if (parts.length > ARRAY_CONSTANTS.EMPTY_LENGTH) {\n                slug = parts[parts.length + ARRAY_CONSTANTS.LAST_ITEM_OFFSET];\n            }\n\n            if (!slug) {\n                return;\n            }\n\n            // Get tag from Flarum store\n            const tags = app.store.all('tags') as unknown[];\n            const tagModel = tags.find((tagItem: unknown) => {\n                const tagRecord = tagItem as Record<string, unknown>;\n                let tagSlug = '';\n\n                if (typeof tagRecord.slug === 'function') {\n                    tagSlug = tagRecord.slug();\n                } else if (tagRecord.attribute && typeof tagRecord.attribute === 'function') {\n                    tagSlug = tagRecord.attribute('slug');\n                }\n\n                return tagSlug === slug;\n            });\n\n            if (!tagModel) {\n                return;\n            }\n\n            // Get background URL from tag model\n            const tagRecord = tagModel as Record<string, unknown>;\n            let bgUrl = '';\n\n            if (tagRecord.attribute && typeof tagRecord.attribute === 'function') {\n                bgUrl = tagRecord.attribute('wusong8899BackgroundURL');\n            }\n\n            if (bgUrl) {\n                return `url(${bgUrl})`;\n            }\n\n            return;\n        } catch {\n            // Fallback to checking inline styles set by flarum-tag-background\n            const inlineBackground = tagElement.style.background;\n            if (inlineBackground && inlineBackground.includes('url(')) {\n                return inlineBackground;\n            }\n            return;\n        }\n    }\n\n    /**\n     * Create individual tag slide\n     */\n    private createTagSlide(tagData: TagData, isMobile: boolean): HTMLElement {\n        const slide = DOMUtils.createElement('div', {\n            className: 'swiper-slide swiper-slide-tag'\n        });\n\n        let innerClass = 'swiper-slide-tag-inner';\n        if (isMobile) {\n            innerClass = 'swiper-slide-tag-inner-mobile';\n        }\n\n        const backgroundStyle = `background:${tagData.background};background-size: cover;background-position: center;background-repeat: no-repeat;`;\n\n        // Check if there's a background image (from flarum-tag-background plugin)\n        const hasBackgroundImage = this.hasBackgroundImage(tagData.background);\n\n        // If there's a background image, hide the text; otherwise show it\n        let textContent = '';\n        if (!hasBackgroundImage) {\n            textContent = `\n            <div style='font-weight:bold;font-size:14px;color:${tagData.nameColor}'>\n                ${tagData.name}\n            </div>\n        `;\n        }\n\n        slide.innerHTML = `\n            <a href='${tagData.url}'>\n                <div class='${innerClass}' style='${backgroundStyle}'>\n                    ${textContent}\n                </div>\n            </a>\n        `;\n\n        return slide;\n    }\n\n    /**\n     * Check if background contains an image URL\n     */\n    private hasBackgroundImage(background: string): boolean {\n        if (!background) {\n            return false;\n        }\n\n        // Check if background contains url() function\n        return background.includes('url(') && !background.includes('url()');\n    }\n\n    /**\n     * Append tag container to DOM\n     */\n    private appendTagContainer(container: HTMLElement): void {\n        const contentElement = DOMUtils.querySelector(\"#content .container .TagsPage-content\");\n        if (contentElement) {\n            DOMUtils.prependChild(contentElement, container);\n        }\n    }\n\n    /**\n     * Add additional content to tag container\n     */\n    private addTagSwiperContent(container: HTMLElement): void {\n        const textContainer = container.querySelector('.TagTextOuterContainer');\n        if (textContainer) {\n            const titleElement = DOMUtils.createElement('div', {\n                className: 'TagTextContainer'\n            }, \"<div class='TagTextIcon'></div>中文玩家社区资讯\");\n\n            DOMUtils.prependChild(textContainer, titleElement);\n\n            const socialButtons = this.createSocialButtonsHTML();\n            textContainer.insertAdjacentHTML('beforeend', socialButtons);\n        }\n    }\n\n    /**\n     * Create social buttons HTML\n     */\n    private createSocialButtonsHTML(): string {\n        const extensionId = 'wusong8899-client1-header-adv';\n\n        // Define social media platforms with their settings keys and default icons\n        const socialPlatforms = [\n            {\n                urlKey: `${extensionId}.SocialKickUrl`,\n                iconKey: `${extensionId}.SocialKickIcon`,\n                defaultIcon: ''\n            },\n            {\n                urlKey: `${extensionId}.SocialFacebookUrl`,\n                iconKey: `${extensionId}.SocialFacebookIcon`,\n                defaultIcon: ''\n            },\n            {\n                urlKey: `${extensionId}.SocialTwitterUrl`,\n                iconKey: `${extensionId}.SocialTwitterIcon`,\n                defaultIcon: ''\n            },\n            {\n                urlKey: `${extensionId}.SocialYouTubeUrl`,\n                iconKey: `${extensionId}.SocialYouTubeIcon`,\n                defaultIcon: ''\n            },\n            {\n                urlKey: `${extensionId}.SocialInstagramUrl`,\n                iconKey: `${extensionId}.SocialInstagramIcon`,\n                defaultIcon: ''\n            }\n        ];\n\n        // Generate social buttons HTML\n        const socialButtons = socialPlatforms\n            .map((platform, index) => {\n                const url = app.forum.attribute(platform.urlKey) || '';\n                const iconUrl = app.forum.attribute(platform.iconKey) || platform.defaultIcon;\n\n                // Only render button if both URL and icon are provided\n                if (!url.trim() || !iconUrl.trim()) {\n                    return '';\n                }\n\n                let marginStyle = '';\n                if (index > ARRAY_CONSTANTS.FIRST_INDEX) {\n                    marginStyle = 'margin-left: 20px;';\n                }\n                return `<img onClick=\"window.open('${url}', '_blank')\" style=\"width: 32px;${marginStyle}\" src=\"${iconUrl}\">`;\n            })\n            .filter(button => button !== '') // Remove empty buttons\n            .join('');\n\n        // Only render the container if there are social buttons\n        if (!socialButtons) {\n            return '';\n        }\n\n        return `\n            <div style=\"text-align:center;padding-top: 10px;\">\n                <button class=\"Button Button--primary\" type=\"button\" style=\"font-weight: normal !important; color:#ffa000; background: #1a1d2e !important;border-radius: 2rem !important;\">\n                    <div style=\"margin-top: 5px;\" class=\"Button-label\">\n                        ${socialButtons}\n                    </div>\n                </button>\n            </div>\n        `;\n    }\n\n    /**\n     * Remove original tag tiles\n     */\n    private removeOriginalTagTiles(): void {\n        const tagTiles = DOMUtils.querySelector(\".TagTiles\");\n        if (tagTiles) {\n            DOMUtils.removeElement(tagTiles);\n        }\n    }\n\n    /**\n     * Setup mobile-specific styles\n     */\n    private setupMobileStyles(): void {\n        if (isMobileDevice()) {\n            const app = DOMUtils.getElementById(\"app\");\n            const appContent = DOMUtils.querySelector(\".App-content\") as HTMLElement;\n\n            if (app) {\n                DOMUtils.setStyles(app, { 'overflow-x': 'hidden' });\n            }\n\n            if (appContent) {\n                DOMUtils.setStyles(appContent, {\n                    'min-height': 'auto',\n                    'background': ''\n                });\n            }\n        }\n    }\n\n    /**\n     * Initialize tag swiper\n     */\n    private initializeTagSwiper(): void {\n        try {\n            const config = getSwiperConfig();\n            const AUTOPLAY_DELAY = 3000;\n            const swiperInstance = new Swiper(\".tagSwiper\", {\n                loop: true,\n                spaceBetween: config.spaceBetween,\n                slidesPerView: config.slidesPerView,\n                autoplay: {\n                    delay: AUTOPLAY_DELAY,\n                    disableOnInteraction: false,\n                },\n                modules: [Autoplay]\n            });\n            // Store reference to prevent garbage collection\n            if (swiperInstance) {\n                // Swiper initialized successfully\n            }\n        } catch {\n            // Silently handle tag swiper initialization errors\n        }\n    }\n}\n", "import app from 'flarum/forum/app';\nimport extractText from 'flarum/common/utils/extractText';\nimport { defaultConfig, type RootConfig, type SlideData } from '../../common/config';\nimport { SLIDESHOW_CONSTANTS, JSON_CONSTANTS } from '../../common/config/constants';\n\n/**\n * Configuration management utility\n */\nexport class ConfigManager {\n    private static instance: ConfigManager;\n    private config = new Map<string, unknown>();\n    private typedConfig: RootConfig = defaultConfig;\n\n    private constructor() {\n        this.loadDefaultConfig();\n    }\n\n    /**\n     * Get singleton instance\n     */\n    static getInstance(): ConfigManager {\n        if (!ConfigManager.instance) {\n            ConfigManager.instance = new ConfigManager();\n        }\n        return ConfigManager.instance;\n    }\n\n    /**\n     * Load default configuration\n     */\n    private loadDefaultConfig(): void {\n        this.config.set('maxSlides', defaultConfig.slider.maxSlides);\n        this.config.set('defaultTransitionTime', defaultConfig.slider.defaultTransitionTime);\n        this.config.set('checkTime', defaultConfig.slider.checkTime);\n        this.config.set('dataCheckInterval', defaultConfig.slider.dataCheckInterval);\n    }\n\n    /**\n     * Get configuration value\n     * @param key - Configuration key\n     * @param defaultValue - Default value if key not found\n     * @returns Configuration value\n     */\n    get(key: string, defaultValue?: unknown): unknown {\n        return this.config.get(key) ?? defaultValue;\n    }\n\n    /**\n     * Set configuration value\n     * @param key - Configuration key\n     * @param value - Configuration value\n     */\n    set(key: string, value: unknown): void {\n        this.config.set(key, value);\n    }\n\n\n    /**\n     * Safely read a forum attribute if available\n     */\n    private getForumAttribute(key: string): unknown {\n        try {\n            const forum = app && app.forum;\n            const attrFn = forum && forum.attribute;\n            if (typeof attrFn === 'function') {\n                return attrFn.call(forum, key);\n            }\n            return;\n        } catch {\n            return;\n        }\n    }\n\n    /**\n     * Get transition time from forum settings\n     * @returns Transition time in milliseconds\n     */\n    getTransitionTime(): number {\n        const transitionTime = this.getForumAttribute('Client1HeaderAdvTransitionTime');\n        if (transitionTime) {\n            return Number.parseInt(String(transitionTime), 10);\n        }\n        return this.get('defaultTransitionTime') as number;\n    }\n\n    /**\n     * Get slide image URL\n     * @param {number} slideNumber - Slide number (1-based)\n     * @returns {string | null} Image URL or null if not set\n     */\n    getSlideImage(slideNumber: number): string {\n        const result = this.getForumAttribute(`Client1HeaderAdvImage${slideNumber}`) as string;\n        return result || '';\n    }\n\n    /**\n     * Get slide link URL\n     * @param {number} slideNumber - Slide number (1-based)\n     * @returns {string} Link URL or empty string if not set\n     */\n    getSlideLink(slideNumber: number): string {\n        const result = this.getForumAttribute(`Client1HeaderAdvLink${slideNumber}`) as string;\n        return result || '';\n    }\n\n    /**\n     * Get all configured slides\n     * @returns Array of slide configurations\n     */\n    getAllSlides(): SlideData[] {\n        const slides: SlideData[] = [];\n        const maxSlides = this.get('maxSlides') as number;\n\n        for (let slideIndex = SLIDESHOW_CONSTANTS.INITIAL_SLIDE_INDEX; slideIndex <= maxSlides; slideIndex += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT) {\n            const image = this.getSlideImage(slideIndex);\n            const link = this.getSlideLink(slideIndex);\n\n            if (image) {\n                slides.push({\n                    slideNumber: slideIndex,\n                    image,\n                    link: link || '#'\n                });\n            }\n        }\n\n        return slides;\n    }\n\n    /**\n     * Get header icon URL from forum settings\n     * @returns Header icon URL or empty string\n     */\n    getHeaderIconUrl(): string {\n        return String(this.getForumAttribute('Client1HeaderAdvHeaderIconUrl') || '');\n    }\n\n    /**\n     * Get social media URL for a platform\n     * @param platform - Social media platform name\n     * @returns Social media URL or empty string\n     */\n    getSocialMediaUrl(platform: string): string {\n        return String(this.getForumAttribute(`wusong8899-client1-header-adv.Social${platform}Url`) || '');\n    }\n\n    /**\n     * Get social media icon for a platform\n     * @param platform - Social media platform name\n     * @returns Social media icon or empty string\n     */\n    getSocialMediaIcon(platform: string): string {\n        return String(this.getForumAttribute(`wusong8899-client1-header-adv.Social${platform}Icon`) || '');\n    }\n\n    /**\n     * Get translation with parameters\n     * @param key - Translation key\n     * @param parameters - Translation parameters\n     * @returns Translated string\n     */\n    getTranslation(key: string, parameters: Record<string, unknown> = {}): string {\n        const fullKey = this.getTranslationKey(key);\n        const translator = app && app.translator;\n        if (translator && translator.trans) {\n            const result = translator.trans(fullKey, parameters);\n            return extractText(result);\n        }\n        return key;\n    }\n\n    /**\n     * Get full translation key\n     * @param key - Short key\n     * @returns Full translation key\n     */\n    private getTranslationKey(key: string): string {\n        return `${defaultConfig.app.translationPrefix}.${key}`;\n    }\n\n    /**\n     * Check if user is logged in\n     * @returns True if user is logged in\n     */\n    isUserLoggedIn(): boolean {\n        return Boolean(app && app.session && app.session.user);\n    }\n\n    /**\n     * Get current route name\n     * @returns Current route name or null\n     */\n    getCurrentRoute(): string {\n        const current = app && app.current;\n        const routeName = current && current.get && current.get('routeName');\n        return routeName || '';\n    }\n\n    /**\n     * Check if current page is tags page\n     * @returns True if on tags page\n     */\n    isTagsPage(): boolean {\n        const route = this.getCurrentRoute();\n        return route === 'tags' || route === 'tag';\n    }\n\n    /**\n     * Check if current page is discussion page\n     * @returns True if on discussion page\n     */\n    isDiscussionPage(): boolean {\n        const route = this.getCurrentRoute();\n        return route === 'discussion';\n    }\n\n    /**\n     * Check if current page is user page\n     * @returns True if on user page\n     */\n    isUserPage(): boolean {\n        const route = this.getCurrentRoute();\n        return route === 'user';\n    }\n\n    /**\n     * Check if a slide is valid (has image)\n     * @param slideNumber - Slide number to check\n     * @returns True if slide has valid image\n     */\n    isSlideValid(slideNumber: number): boolean {\n        const image = this.getSlideImage(slideNumber);\n        return typeof image === 'string' && image.trim().length > SLIDESHOW_CONSTANTS.VALIDATION_ERRORS_EMPTY;\n    }\n\n    /**\n     * Get count of valid slides\n     * @returns Number of valid slides\n     */\n    getValidSlideCount(): number {\n        const maxSlides = this.get('maxSlides') as number;\n        let count = SLIDESHOW_CONSTANTS.VALIDATION_ERRORS_EMPTY;\n\n        for (let slideIndex = SLIDESHOW_CONSTANTS.INITIAL_SLIDE_INDEX; slideIndex <= maxSlides; slideIndex += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT) {\n            if (this.isSlideValid(slideIndex)) {\n                count += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT;\n            }\n        }\n\n        return count;\n    }\n\n    /**\n     * Check if slideshow should be displayed\n     * @returns True if slideshow should be shown\n     */\n    shouldDisplaySlideshow(): boolean {\n        return this.isTagsPage() && this.getValidSlideCount() > SLIDESHOW_CONSTANTS.VALIDATION_ERRORS_EMPTY;\n    }\n\n    /**\n     * Export configuration as JSON\n     * @returns JSON string of configuration\n     */\n    exportConfig(): string {\n        const configObject: Record<string, unknown> = {};\n        for (const [key, value] of this.config) {\n            configObject[key] = value;\n        }\n        return JSON.stringify(configObject, (key, value) => value, JSON_CONSTANTS.INDENT_SIZE);\n    }\n\n    /**\n     * Import configuration from JSON\n     * @param jsonConfig - JSON configuration string\n     * @returns True if import successful\n     */\n    importConfig(jsonConfig: string): boolean {\n        try {\n            const configObject = JSON.parse(jsonConfig);\n            for (const [key, value] of Object.entries(configObject)) {\n                this.config.set(key, value);\n            }\n            return true;\n        } catch {\n            return false;\n        }\n    }\n\n    /**\n     * Reset configuration to defaults\n     */\n    resetToDefaults(): void {\n        this.config.clear();\n        this.loadDefaultConfig();\n    }\n}\n", "import app from 'flarum/forum/app';\nimport { ConfigManager } from './config-manager';\nimport { SLIDESHOW_CONSTANTS, ERROR_HANDLING, JSON_CONSTANTS } from '../../common/config/constants';\nimport type {\n    ErrorLogEntry,\n    ValidationResult,\n    DependencyCheckResult,\n    ConfigurationCheckResult,\n    NotificationType\n} from '../../common/config/types';\n\n/**\n * Error handling and logging utility\n */\nexport class ErrorHandler {\n    private static instance: ErrorHandler;\n    private configManager: ConfigManager;\n    private errorLog: ErrorLogEntry[] = [];\n\n    private constructor() {\n        this.configManager = ConfigManager.getInstance();\n        this.setupGlobalErrorHandler();\n    }\n\n    /**\n     * Get singleton instance\n     */\n    static getInstance(): ErrorHandler {\n        if (!ErrorHandler.instance) {\n            ErrorHandler.instance = new ErrorHandler();\n        }\n        return ErrorHandler.instance;\n    }\n\n    /**\n     * Setup global error handler\n     */\n    private setupGlobalErrorHandler(): void {\n        globalThis.addEventListener('error', (event) => {\n            this.logError(event.error, 'Global Error Handler');\n        });\n\n        globalThis.addEventListener('unhandledrejection', (event) => {\n            this.logError(new Error(event.reason), 'Unhandled Promise Rejection');\n        });\n    }\n\n    /**\n     * Log error with context\n     * @param {Error} error - Error object\n     * @param {string} context - Error context\n     */\n    logError(error: Error, context = 'Unknown'): void {\n        const errorEntry = {\n            timestamp: new Date(),\n            error,\n            context\n        };\n\n        this.errorLog.push(errorEntry);\n\n        // Keep only last 50 errors to prevent memory issues\n        if (this.errorLog.length > ERROR_HANDLING.MAX_ERROR_LOG_ENTRIES) {\n            this.errorLog.shift();\n        }\n\n        // Log error to internal log only\n    }\n\n    /**\n     * Handle async operation with error catching\n     * @param operation - Async operation to execute\n     * @param context - Operation context\n     * @param fallbackValue - Value to return on error\n     * @returns Operation result or fallback value\n     */\n    handleAsync<TResult>(\n        operation: () => Promise<TResult>,\n        context: string,\n        fallbackValue?: TResult\n    ): Promise<TResult | undefined> {\n        return operation().then(\n            result => result,\n            error => {\n                this.logError(error as Error, context);\n                return fallbackValue;\n            }\n        );\n    }\n\n    /**\n     * Handle synchronous operation with error catching\n     * @param operation - Operation to execute\n     * @param context - Operation context\n     * @param fallbackValue - Value to return on error\n     * @returns Operation result or fallback value\n     */\n    handleSync<TResult>(\n        operation: () => TResult,\n        context: string,\n        fallbackValue?: TResult\n    ): TResult | undefined {\n        try {\n            return operation();\n        } catch (error) {\n            this.logError(error as Error, context);\n            return fallbackValue;\n        }\n    }\n\n    /**\n     * Validate URL format\n     * @param url - URL to validate\n     * @returns True if valid URL\n     */\n    isValidUrl(url: string): boolean {\n        if (!url || typeof url !== 'string') {\n            return false;\n        }\n\n        try {\n            const urlObj = new URL(url);\n            return Boolean(urlObj);\n        } catch {\n            return false;\n        }\n    }\n\n    /**\n     * Validate slide configuration\n     * @param slideNumber - Slide number\n     * @param imageUrl - Image URL\n     * @param linkUrl - Link URL\n     * @returns Validation result\n     */\n    validateSlideConfig(slideNumber: number, imageUrl: string, linkUrl: string): ValidationResult {\n        const errors: string[] = [];\n\n        if (!Number.isInteger(slideNumber) ||\n            slideNumber < ERROR_HANDLING.SLIDE_NUMBER_MIN ||\n            slideNumber > ERROR_HANDLING.SLIDE_NUMBER_MAX) {\n            errors.push(`Slide number must be between ${ERROR_HANDLING.SLIDE_NUMBER_MIN} and ${ERROR_HANDLING.SLIDE_NUMBER_MAX}`);\n        }\n\n        if (!imageUrl || !this.isValidUrl(imageUrl)) {\n            errors.push('Image URL is required and must be valid');\n        }\n\n        if (linkUrl && !this.isValidUrl(linkUrl)) {\n            errors.push('Link URL must be valid if provided');\n        }\n\n        return {\n            isValid: errors.length === SLIDESHOW_CONSTANTS.VALIDATION_ERRORS_EMPTY,\n            errors\n        };\n    }\n\n    /**\n     * Validate DOM element exists\n     * @param selector - CSS selector\n     * @param context - Context for error reporting\n     * @returns Element if found, void otherwise\n     */\n    validateElement(selector: string, context = 'Element validation'): Element | void {\n        try {\n            const element = document.querySelector(selector);\n            if (!element) {\n                this.logError(new Error(`Element not found: ${selector}`), context);\n                return;\n            }\n            return element;\n        } catch (error) {\n            this.logError(error as Error, context);\n            return;\n        }\n    }\n\n    /**\n     * Safely execute DOM operation\n     * @param operation - DOM operation\n     * @param context - Operation context\n     * @returns Operation result or undefined\n     */\n    safeDOMOperation<TResult>(operation: () => TResult, context: string): TResult | undefined {\n        return this.handleSync(operation, `DOM Operation: ${context}`);\n    }\n\n    /**\n     * Create error notification\n     * @param message - Error message\n     * @param notificationType - Notification type\n     */\n    showErrorNotification(message: string, notificationType: NotificationType = 'error'): void {\n        try {\n            // Use Flarum's notification system if available\n            if (app.alerts && app.alerts.show) {\n                app.alerts.show({\n                    type: notificationType,\n                    content: message\n                } as never);\n            } else {\n                // Silently handle notification fallback\n            }\n        } catch {\n            // Silently handle notification errors\n        }\n    }\n\n    /**\n     * Get error log\n     * @returns Array of error entries\n     */\n    getErrorLog(): ErrorLogEntry[] {\n        return [...this.errorLog];\n    }\n\n    /**\n     * Clear error log\n     */\n    clearErrorLog(): void {\n        this.errorLog = [];\n    }\n\n    /**\n     * Export error log as JSON\n     * @returns {string} JSON string of error log\n     */\n    exportErrorLog(): string {\n        const exportData = this.errorLog.map(entry => ({\n            timestamp: entry.timestamp.toISOString(),\n            message: entry.error.message,\n            stack: entry.error.stack,\n            context: entry.context\n        }));\n\n        // Format the JSON with proper indentation\n        let result = JSON.stringify(exportData);\n\n        // Add indentation manually to avoid null usage\n        try {\n            const parsed = JSON.parse(result);\n            result = this.formatJsonWithIndent(parsed, JSON_CONSTANTS.INDENT_SIZE);\n        } catch {\n            // Fallback to basic stringify if parsing fails\n        }\n\n        return result;\n    }\n\n    /**\n     * Format JSON with custom indentation\n     */\n    private formatJsonWithIndent(obj: unknown, indent: number): string {\n        const spaces = ' '.repeat(indent);\n        return JSON.stringify(obj, (key, value) => value, spaces);\n    }\n\n    /**\n     * Check if extension dependencies are available\n     * @returns Dependency check results\n     */\n    checkDependencies(): DependencyCheckResult {\n        const missing: string[] = [];\n\n        // Check for required globals\n        if (typeof app === 'undefined') {\n            missing.push('Flarum app object');\n        }\n\n        // Check for Swiper\n        try {\n            // This will be checked when Swiper is actually imported\n        } catch {\n            missing.push('Swiper library');\n        }\n\n        return {\n            isValid: missing.length === SLIDESHOW_CONSTANTS.VALIDATION_ERRORS_EMPTY,\n            missing\n        };\n    }\n\n    /**\n     * Validate extension configuration\n     * @returns Configuration validation results\n     */\n    validateConfiguration(): ConfigurationCheckResult {\n        const issues: string[] = [];\n        const config = this.configManager.getExtensionConfig() as Record<string, unknown>;\n\n        if (!config.extensionId) {\n            issues.push('Extension ID is not configured');\n        }\n\n        const maxSlides = config.maxSlides as number;\n        if (maxSlides < ERROR_HANDLING.CONFIG_MAX_SLIDES_MIN || maxSlides > ERROR_HANDLING.CONFIG_MAX_SLIDES_MAX) {\n            issues.push(`Max slides should be between ${ERROR_HANDLING.CONFIG_MAX_SLIDES_MIN} and ${ERROR_HANDLING.CONFIG_MAX_SLIDES_MAX}`);\n        }\n\n        const transitionTime = config.transitionTime as number;\n        if (transitionTime < ERROR_HANDLING.TRANSITION_TIME_MIN || transitionTime > ERROR_HANDLING.TRANSITION_TIME_MAX) {\n            issues.push(`Transition time should be between ${ERROR_HANDLING.TRANSITION_TIME_MIN} and ${ERROR_HANDLING.TRANSITION_TIME_MAX} milliseconds`);\n        }\n\n        // Note: No slides configured is expected for fresh installations\n        // This is not treated as a critical error\n\n        const NO_ISSUES = 0;\n        return {\n            isValid: issues.length === NO_ISSUES,\n            issues\n        };\n    }\n\n    /**\n     * Initialize error handling for the extension\n     * @returns True if initialization successful\n     */\n    initialize(): boolean {\n        try {\n            const depCheck = this.checkDependencies();\n            if (!depCheck.isValid) {\n                this.logError(\n                    new Error(`Missing dependencies: ${depCheck.missing.join(', ')}`),\n                    'Dependency Check'\n                );\n                return false;\n            }\n\n            const configCheck = this.validateConfiguration();\n            if (!configCheck.isValid) {\n                this.logError(\n                    new Error(`Configuration issues: ${configCheck.issues.join(', ')}`),\n                    'Configuration Check'\n                );\n                // Don't return false for config issues, just log them\n            }\n\n            return true;\n        } catch (error) {\n            this.logError(error as Error, 'Error Handler Initialization');\n            return false;\n        }\n    }\n}\n", "import { extend } from 'flarum/common/extend';\r\nimport app from 'flarum/forum/app';\r\nimport HeaderPrimary from 'flarum/forum/components/HeaderPrimary';\r\n\r\nimport { SlideshowManager } from './components/slideshow-manager';\r\nimport { UIManager } from './components/ui-manager';\r\nimport { ErrorHandler } from './utils/error-handler';\r\nimport { ConfigManager } from './utils/config-manager';\r\nimport { defaultConfig } from '../common/config';\r\n\r\n/**\r\n * Main extension initializer\r\n */\r\napp.initializers.add(defaultConfig.app.extensionId, () => {\r\n    const errorHandler = ErrorHandler.getInstance();\r\n    const configManager = ConfigManager.getInstance();\r\n\r\n    // Initialize error handling\r\n    if (!errorHandler.initialize()) {\r\n        return;\r\n    }\r\n\r\n    const slideshowManager = new SlideshowManager();\r\n    const uiManager = new UIManager();\r\n\r\n    extend(HeaderPrimary.prototype, 'view', function headerPrimaryViewExtension(vnode: unknown) {\r\n        errorHandler.handleSync(() => {\r\n            if (configManager.isTagsPage()) {\r\n                initializeExtension(vnode, slideshowManager, uiManager);\r\n            }\r\n        }, 'HeaderPrimary view extension');\r\n    });\r\n});\r\n\r\n/**\r\n * Initialize extension components\r\n */\r\nconst initializeExtension = (\r\n    vnode: unknown,\r\n    slideshowManager: SlideshowManager,\r\n    uiManager: UIManager\r\n): void => {\r\n    try {\r\n        // Setup slideshow\r\n        slideshowManager.attachAdvertiseHeader(vnode);\r\n\r\n        // Setup UI components\r\n        setupUIComponents(uiManager);\r\n\r\n        // Add header icon for non-logged users\r\n        if (!app.session.user) {\r\n            addHeaderIcon();\r\n        }\r\n\r\n    } catch {\r\n        // Silently handle initialization errors\r\n    }\r\n}\r\n\r\n/**\r\n * Setup UI components\r\n */\r\nconst setupUIComponents = (uiManager: UIManager): void => {\r\n    try {\r\n        if (!document.getElementById(\"swiperTagContainer\")) {\r\n            uiManager.changeCategoryLayout();\r\n            // Additional UI setup would go here\r\n        }\r\n    } catch {\r\n        // Silently handle UI setup errors\r\n    }\r\n};\r\n\r\n/**\r\n * Add header icon for branding\r\n */\r\nconst addHeaderIcon = (): void => {\r\n    let headerIconContainer = document.getElementById(defaultConfig.ui.headerIconId);\r\n\r\n    if (headerIconContainer === null) {\r\n        // Get header icon URL from settings, fallback to default config\r\n        const headerIconUrl = app.forum.attribute('Client1HeaderAdvHeaderIconUrl') || defaultConfig.ui.headerIconUrl;\r\n\r\n        headerIconContainer = document.createElement(\"div\");\r\n        headerIconContainer.id = defaultConfig.ui.headerIconId;\r\n        headerIconContainer.style.display = 'inline-block';\r\n        headerIconContainer.style.marginTop = '8px';\r\n        headerIconContainer.innerHTML = `<img src=\"${headerIconUrl}\" style=\"height: 24px;\" />`;\r\n\r\n        const backControl = document.querySelector(\"#app-navigation .App-backControl\");\r\n        if (backControl && backControl.firstChild) {\r\n            backControl.firstChild.before(headerIconContainer);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"], "names": ["isObject", "obj", "extend", "target", "src", "noExtend", "key", "ssrDocument", "getDocument", "doc", "ssrWindow", "callback", "id", "getWindow", "win", "classesToTokens", "classes", "c", "deleteProps", "object", "nextTick", "delay", "now", "getComputedStyle", "el", "window", "style", "getTranslate", "axis", "matrix", "curTransform", "transformMatrix", "curStyle", "o", "isNode", "node", "to", "i", "nextSource", "keysArray", "nextIndex", "len", "<PERSON><PERSON><PERSON>", "desc", "setCSSProperty", "varName", "varValue", "animateCSSModeScroll", "_ref", "swiper", "targetPosition", "side", "startPosition", "startTime", "time", "duration", "dir", "isOutOfBound", "current", "animate", "progress", "easeProgress", "currentPosition", "getSlideTransformEl", "slideEl", "elementChildren", "element", "selector", "children", "elementIsChildOfSlot", "slot", "elementsQueue", "elementToCheck", "elementIsChildOf", "parent", "<PERSON><PERSON><PERSON><PERSON>", "showWarning", "text", "createElement", "tag", "elementPrevAll", "prevEls", "prev", "elementNextAll", "nextEls", "next", "elementStyle", "prop", "elementIndex", "child", "elementParents", "parents", "elementOuterSize", "size", "<PERSON><PERSON><PERSON><PERSON>", "makeElementsArray", "getRotateFix", "v", "setInnerHTML", "html", "s", "support", "calcSupport", "document", "getSupport", "deviceCached", "calcDevice", "_temp", "userAgent", "platform", "ua", "device", "screenWidth", "screenHeight", "android", "ipad", "ipod", "iphone", "windows", "macos", "iPadScreens", "getDevice", "overrides", "browser", "calcB<PERSON>er", "needPerspectiveFix", "<PERSON><PERSON><PERSON><PERSON>", "major", "minor", "num", "isWebView", "isSafariB<PERSON><PERSON>", "need3dFix", "<PERSON><PERSON><PERSON><PERSON>", "Resize", "on", "emit", "observer", "animationFrame", "resize<PERSON><PERSON>ler", "createObserver", "entries", "width", "height", "newWidth", "newHeight", "_ref2", "contentBoxSize", "contentRect", "removeObserver", "orientationChangeHandler", "Observer", "extendParams", "observers", "attach", "options", "ObserverFunc", "mutations", "observerUpdate", "init", "containerParents", "destroy", "eventsEmitter", "events", "handler", "priority", "self", "method", "event", "once<PERSON><PERSON><PERSON>", "_len", "args", "_key", "index", "<PERSON><PERSON><PERSON><PERSON>", "data", "context", "_len2", "_key2", "updateSize", "updateSlides", "getDirectionPropertyValue", "label", "params", "wrapperEl", "slidesEl", "swiperSize", "rtl", "wrongRTL", "isVirtual", "previousSlidesLength", "slides", "<PERSON><PERSON><PERSON><PERSON>", "snapGrid", "slidesGrid", "slidesSizesGrid", "offsetBefore", "offsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "spaceBetween", "slidePosition", "prevSlideSize", "gridEnabled", "slideSize", "shouldResetSlideSize", "slide", "slideStyles", "currentTransform", "currentWebKitTransform", "paddingLeft", "paddingRight", "marginLeft", "marginRight", "boxSizing", "clientWidth", "offsetWidth", "newSlidesGrid", "slidesGridItem", "groups", "groupSize", "_", "slideIndex", "allSlidesSize", "slideSizeValue", "maxSnap", "snap", "offsetSize", "allSlidesOffset", "snapIndex", "addToSnapGrid", "addToSlidesGrid", "backFaceHiddenClass", "hasClassBackfaceClassAdded", "updateAutoHeight", "speed", "activeSlides", "getSlideByIndex", "updateSlidesOffset", "minusOffset", "toggleSlideClasses$1", "condition", "className", "updateSlidesProgress", "translate", "offsetCenter", "slideOffset", "slideProgress", "originalSlideProgress", "slideBefore", "slideAfter", "isFullyVisible", "isVisible", "updateProgress", "multiplier", "translatesDiff", "isBeginning", "isEnd", "progressLoop", "wasBeginning", "wasEnd", "isBeginningRounded", "isEndRounded", "firstSlideIndex", "lastSlideIndex", "firstSlideTranslate", "lastSlideTranslate", "translateMax", "translateAbs", "toggleSlideClasses", "updateSlidesClasses", "activeIndex", "getFilteredSlide", "activeSlide", "prevSlide", "nextSlide", "processLazyPreloader", "imageEl", "slideSelector", "lazyEl", "unlazy", "preload", "amount", "<PERSON><PERSON><PERSON><PERSON>iew", "activeColumn", "preloadColumns", "slideIndexLastInView", "realIndex", "getActiveIndexByTranslate", "updateActiveIndex", "newActiveIndex", "previousIndex", "previousRealIndex", "previousSnapIndex", "getVirtualRealIndex", "aIndex", "skip", "firstSlideInColumn", "activeSlideIndex", "updateClickedSlide", "path", "pathEl", "slideFound", "update", "getSwiperTranslate", "currentTranslate", "setTranslate", "byController", "x", "y", "z", "newProgress", "minTranslate", "maxTranslate", "translateTo", "runCallbacks", "translateBounds", "internal", "newTranslate", "isH", "e", "setTransition", "transitionEmit", "direction", "step", "transitionStart", "transitionEnd", "transition", "slideTo", "initial", "enabled", "normalizedTranslate", "normalizedGrid", "normalizedGridNext", "t", "slideToLoop", "newIndex", "targetSlideIndex", "cols", "centeredSlides", "needLoopFix", "slideNext", "animating", "perGroup", "increment", "slidePrev", "rtlTranslate", "normalize", "val", "normalizedSnapGrid", "isFreeMode", "prevSnap", "prevSnapIndex", "prevIndex", "lastIndex", "slideReset", "slideToClosest", "threshold", "currentSnap", "nextSnap", "slideToClickedSlide", "slideToIndex", "isGrid", "loopCreate", "slideRealIndex", "initSlides", "clearBlankSlides", "slidesPerGroup", "shouldFillGroup", "shouldFillGrid", "addBlankSlides", "amountOfSlides", "slidesToAdd", "loopFix", "byMousewheel", "allowSlidePrev", "allowSlideNext", "initialSlide", "loopedSlides", "prependSlidesIndexes", "appendSlidesIndexes", "isInitialOverflow", "isNext", "isPrev", "slidesPrepended", "slidesAppended", "activeColIndexWithShift", "colIndexToPrepend", "currentSlideTranslate", "diff", "shift", "loopParams", "loop<PERSON><PERSON><PERSON>", "newSlidesOrder", "loop", "setGrabCursor", "moving", "unsetGrabCursor", "grabCursor", "closestElement", "base", "__closestFrom", "found", "preventEdgeSwipe", "startX", "edgeSwipeDetection", "edgeSwipeThreshold", "onTouchStart", "touches", "targetEl", "swipingClassHasValue", "eventPath", "noSwipingSelector", "isTargetShadow", "startY", "preventDefault", "shouldPreventDefault", "onTouchMove", "targetTouch", "pageX", "pageY", "diffX", "diffY", "touchAngle", "touchesDiff", "prevTouchesDirection", "isLoop", "allowLoopFix", "evt", "disableParentSwiper", "resistanceRatio", "onTouchEnd", "touchEndTime", "timeDiff", "pathTree", "currentPos", "swipeToLast", "stopIndex", "rewindFirstIndex", "rewindLastIndex", "ratio", "onResize", "isVirtualLoop", "onClick", "onScroll", "onLoad", "onDocumentTouchStart", "capture", "dom<PERSON>ethod", "swiperMethod", "attachEvents", "detachEvents", "events$1", "isGridEnabled", "setBreakpoint", "initialized", "breakpoints", "breakpointsBase", "breakpoint<PERSON><PERSON><PERSON>", "breakpoint", "breakpointP<PERSON>ms", "wasMultiRow", "isMultiRow", "wasGrabCursor", "isGrabCursor", "wasEnabled", "wasModuleEnabled", "isModuleEnabled", "directionChanged", "needsReLoop", "<PERSON><PERSON><PERSON>", "isEnabled", "<PERSON><PERSON><PERSON>", "getBreakpoint", "containerEl", "currentHeight", "points", "point", "minRatio", "b", "value", "prepareClasses", "prefix", "resultClasses", "item", "classNames", "addClasses", "suffixes", "removeClasses", "checkOverflow", "wasLocked", "slidesOffsetBefore", "lastSlideRightEdge", "checkOverflow$1", "defaults", "moduleExtendParams", "allModulesParams", "moduleParamName", "moduleParams", "prototypes", "extendedDefaults", "Swiper", "swipers", "newParams", "mod", "swiperParams", "eventName", "property", "min", "cls", "updates", "view", "exact", "spv", "breakLoop", "translateValue", "translated", "newDirection", "needUpdate", "currentDirection", "getWrapperSelector", "lazyElements", "deleteInstance", "cleanStyles", "newDefaults", "modules", "module", "m", "prototypeGroup", "protoMethod", "createElementIfNotDefined", "originalParams", "checkProps", "Navigation", "getEl", "res", "toggleEl", "disabled", "subEl", "nextEl", "prevEl", "onPrevClick", "onNextClick", "initButton", "destroyButton", "disable", "_s", "targetIsButton", "isHidden", "enable", "classesToSelector", "Pagination", "pfx", "number", "bulletSize", "dynamicBulletIndex", "isPaginationDisabled", "setSideBullets", "bulletEl", "position", "bulletActiveClass", "getMoveDirection", "length", "onBulletClick", "moveDirection", "total", "bullets", "firstIndex", "midIndex", "classesToRemove", "suffix", "bullet", "bulletIndex", "first<PERSON><PERSON>played<PERSON><PERSON>et", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dynamicBulletsLength", "bulletsOffset", "offsetProp", "subElIndex", "fractionEl", "totalEl", "progressbarDirection", "scale", "scaleX", "scaleY", "progressEl", "render", "paginationHTML", "numberOfBullets", "Autoplay", "timeout", "raf", "autoplayDelayTotal", "autoplayDelayCurrent", "autoplayTimeLeft", "autoplayStartTime", "wasPaused", "isTouched", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touchStartTimeout", "slideChanged", "pausedByInteraction", "pausedByPointerEnter", "onTransitionEnd", "resume", "calcTimeLeft", "timeLeft", "getSlideDelay", "activeSlideEl", "run", "delayForce", "currentSlideDelay", "proceed", "start", "stop", "pause", "reset", "onVisibilityChange", "onPointerEnter", "onPointerLeave", "attachMouseEvents", "detachMouseEvents", "attachDocumentEvents", "detachDocumentEvents", "effectInit", "effect", "overwriteParams", "perspective", "recreateShadows", "getEffectParams", "overwriteParamsResult", "shadowEl", "requireUpdateOnVirtual", "effect<PERSON>arget", "effectParams", "transformEl", "createShadow", "shadowClass", "shadow<PERSON><PERSON><PERSON>", "EffectCoverflow", "swiper<PERSON><PERSON><PERSON>", "swiperHeight", "isHorizontal", "transform", "center", "rotate", "r", "centerOffset", "offsetMultiplier", "rotateY", "rotateX", "translateZ", "stretch", "translateY", "translateX", "slideTransform", "shadowBeforeEl", "shadowAfterEl", "MOBILE_DETECTION", "SWIPER_CONFIG", "ERROR_HANDLING", "MOBILE_LAYOUT", "SLIDESHOW_CONSTANTS", "ARRAY_CONSTANTS", "JSON_CONSTANTS", "TIMING", "DOM_ELEMENTS", "CSS_CLASSES", "CSS_SELECTORS", "EXTENSION_CONFIG", "tagName", "attributes", "innerHTML", "getElementById", "querySelector", "querySelectorAll", "setStyles", "styles", "append<PERSON><PERSON><PERSON>", "prepend<PERSON>hild", "removeElement", "mobileDetectionState", "isMobileDevice", "check", "mobileRegex", "mobileRegex2", "userAgentSubstr", "getSwiperConfig", "defaultConfig", "SlideshowManager", "forum", "app", "attrFn", "_vdom", "container", "wrapper", "existingContainer", "DOMUtils.querySelector", "DOMUtils.removeElement", "navElements", "DOMUtils.querySelectorAll", "DOMUtils.createElement", "styleWidth", "DOMUtils.setStyles", "DOMUtils.appendChild", "transitionTime", "imageSrc", "imageLink", "clickHandler", "pagination", "prevButton", "nextButton", "contentContainer", "DOMUtils.prependChild", "UIManager", "DOMUtils.getElementById", "tagTiles", "textContainer", "isMobile", "tagElement", "tagData", "linkElement", "nameElement", "desc<PERSON><PERSON>", "backgroundImage", "computedStyle", "background", "description", "descColor", "tagUrl", "parts", "tIndex", "tagsIndex", "slug", "tagModel", "tagItem", "tagRecord", "tagSlug", "bgUrl", "inlineBackground", "innerClass", "backgroundStyle", "hasBackgroundImage", "textContent", "contentElement", "titleElement", "socialButtons", "extensionId", "url", "iconUrl", "marginStyle", "button", "appContent", "config", "AUTOPLAY_DELAY", "swiperInstance", "ConfigManager", "defaultValue", "slideNumber", "maxSlides", "image", "link", "parameters", "<PERSON><PERSON><PERSON>", "translator", "result", "extractText", "route", "count", "configObject", "jsonConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "errorEntry", "operation", "fallback<PERSON><PERSON><PERSON>", "imageUrl", "linkUrl", "errors", "message", "notificationType", "exportData", "entry", "parsed", "indent", "spaces", "missing", "issues", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "slideshowManager", "uiManager", "HeaderPrimary", "vnode", "initializeExtension", "setupUIComponents", "addHeaderIcon", "headerIconContainer", "headerIconUrl", "backControl"], "mappings": "mCAYA,SAASA,GAASC,EAAK,CACrB,OAAOA,IAAQ,MAAQ,OAAOA,GAAQ,UAAY,gBAAiBA,GAAOA,EAAI,cAAgB,MAChG,CACA,SAASC,GAAOC,EAAQC,EAAK,CACvBD,IAAW,SACbA,EAAS,CAAA,GAEPC,IAAQ,SACVA,EAAM,CAAA,GAER,MAAMC,EAAW,CAAC,YAAa,cAAe,WAAW,EACzD,OAAO,KAAKD,CAAG,EAAE,OAAOE,GAAOD,EAAS,QAAQC,CAAG,EAAI,CAAC,EAAE,QAAQA,GAAO,CACnE,OAAOH,EAAOG,CAAG,EAAM,IAAaH,EAAOG,CAAG,EAAIF,EAAIE,CAAG,EAAWN,GAASI,EAAIE,CAAG,CAAC,GAAKN,GAASG,EAAOG,CAAG,CAAC,GAAK,OAAO,KAAKF,EAAIE,CAAG,CAAC,EAAE,OAAS,GACpJJ,GAAOC,EAAOG,CAAG,EAAGF,EAAIE,CAAG,CAAC,CAEhC,CAAC,CACH,CACA,MAAMC,GAAc,CAClB,KAAM,CAAA,EACN,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,EACvB,cAAe,CACb,MAAO,CAAC,EACR,SAAU,EACd,EACE,eAAgB,CACd,OAAO,IACT,EACA,kBAAmB,CACjB,MAAO,CAAA,CACT,EACA,gBAAiB,CACf,OAAO,IACT,EACA,aAAc,CACZ,MAAO,CACL,WAAY,CAAC,CACnB,CACE,EACA,eAAgB,CACd,MAAO,CACL,SAAU,CAAA,EACV,WAAY,CAAA,EACZ,MAAO,CAAA,EACP,cAAe,CAAC,EAChB,sBAAuB,CACrB,MAAO,CAAA,CACT,CACN,CACE,EACA,iBAAkB,CAChB,MAAO,CAAA,CACT,EACA,YAAa,CACX,OAAO,IACT,EACA,SAAU,CACR,KAAM,GACN,KAAM,GACN,SAAU,GACV,KAAM,GACN,OAAQ,GACR,SAAU,GACV,SAAU,GACV,OAAQ,EACZ,CACA,EACA,SAASC,GAAc,CACrB,MAAMC,EAAM,OAAO,SAAa,IAAc,SAAW,CAAA,EACzDP,OAAAA,GAAOO,EAAKF,EAAW,EAChBE,CACT,CACA,MAAMC,GAAY,CAChB,SAAUH,GACV,UAAW,CACT,UAAW,EACf,EACE,SAAU,CACR,KAAM,GACN,KAAM,GACN,SAAU,GACV,KAAM,GACN,OAAQ,GACR,SAAU,GACV,SAAU,GACV,OAAQ,EACZ,EACE,QAAS,CACP,cAAe,CAAC,EAChB,WAAY,CAAC,EACb,IAAK,CAAC,EACN,MAAO,CAAC,CACZ,EACE,YAAa,UAAuB,CAClC,OAAO,IACT,EACA,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,EACvB,kBAAmB,CACjB,MAAO,CACL,kBAAmB,CACjB,MAAO,EACT,CACN,CACE,EACA,OAAQ,CAAC,EACT,MAAO,CAAC,EACR,OAAQ,CAAA,EACR,YAAa,CAAC,EACd,cAAe,CAAC,EAChB,YAAa,CACX,MAAO,CAAA,CACT,EACA,sBAAsBI,EAAU,CAC9B,OAAI,OAAO,WAAe,KACxBA,EAAQ,EACD,MAEF,WAAWA,EAAU,CAAC,CAC/B,EACA,qBAAqBC,EAAI,CACnB,OAAO,WAAe,KAG1B,aAAaA,CAAE,CACjB,CACF,EACA,SAASC,GAAY,CACnB,MAAMC,EAAM,OAAO,OAAW,IAAc,OAAS,CAAA,EACrDZ,OAAAA,GAAOY,EAAKJ,EAAS,EACdI,CACT,CC7IA,SAASC,GAAgBC,EAAS,CAChC,OAAIA,IAAY,SACdA,EAAU,IAELA,EAAQ,OAAO,MAAM,GAAG,EAAE,OAAOC,GAAK,CAAC,CAACA,EAAE,KAAI,CAAE,CACzD,CAEA,SAASC,GAAYjB,EAAK,CACxB,MAAMkB,EAASlB,EACf,OAAO,KAAKkB,CAAM,EAAE,QAAQb,GAAO,CACjC,GAAI,CACFa,EAAOb,CAAG,EAAI,IAChB,MAAY,CAEZ,CACA,GAAI,CACF,OAAOa,EAAOb,CAAG,CACnB,MAAY,CAEZ,CACF,CAAC,CACH,CACA,SAASc,GAAST,EAAUU,EAAO,CACjC,OAAIA,IAAU,SACZA,EAAQ,GAEH,WAAWV,EAAUU,CAAK,CACnC,CACA,SAASC,IAAM,CACb,OAAO,KAAK,IAAG,CACjB,CACA,SAASC,GAAiBC,EAAI,CAC5B,MAAMC,EAASZ,EAAS,EACxB,IAAIa,EACJ,OAAID,EAAO,mBACTC,EAAQD,EAAO,iBAAiBD,EAAI,IAAI,GAEtC,CAACE,GAASF,EAAG,eACfE,EAAQF,EAAG,cAERE,IACHA,EAAQF,EAAG,OAENE,CACT,CACA,SAASC,GAAaH,EAAII,EAAM,CAC1BA,IAAS,SACXA,EAAO,KAET,MAAMH,EAASZ,EAAS,EACxB,IAAIgB,EACAC,EACAC,EACJ,MAAMC,EAAWT,GAAiBC,CAAE,EACpC,OAAIC,EAAO,iBACTK,EAAeE,EAAS,WAAaA,EAAS,gBAC1CF,EAAa,MAAM,GAAG,EAAE,OAAS,IACnCA,EAAeA,EAAa,MAAM,IAAI,EAAE,IAAI,GAAK,EAAE,QAAQ,IAAK,GAAG,CAAC,EAAE,KAAK,IAAI,GAIjFC,EAAkB,IAAIN,EAAO,gBAAgBK,IAAiB,OAAS,GAAKA,CAAY,IAExFC,EAAkBC,EAAS,cAAgBA,EAAS,YAAcA,EAAS,aAAeA,EAAS,aAAeA,EAAS,WAAaA,EAAS,iBAAiB,WAAW,EAAE,QAAQ,aAAc,oBAAoB,EACzNH,EAASE,EAAgB,WAAW,MAAM,GAAG,GAE3CH,IAAS,MAEPH,EAAO,gBAAiBK,EAAeC,EAAgB,IAElDF,EAAO,SAAW,GAAIC,EAAe,WAAWD,EAAO,EAAE,CAAC,EAE9DC,EAAe,WAAWD,EAAO,CAAC,CAAC,GAEtCD,IAAS,MAEPH,EAAO,gBAAiBK,EAAeC,EAAgB,IAElDF,EAAO,SAAW,GAAIC,EAAe,WAAWD,EAAO,EAAE,CAAC,EAE9DC,EAAe,WAAWD,EAAO,CAAC,CAAC,GAEnCC,GAAgB,CACzB,CACA,SAAS9B,GAASiC,EAAG,CACnB,OAAO,OAAOA,GAAM,UAAYA,IAAM,MAAQA,EAAE,aAAe,OAAO,UAAU,SAAS,KAAKA,CAAC,EAAE,MAAM,EAAG,EAAE,IAAM,QACpH,CACA,SAASC,GAAOC,EAAM,CAEpB,OAAI,OAAO,OAAW,KAAe,OAAO,OAAO,YAAgB,IAC1DA,aAAgB,YAElBA,IAASA,EAAK,WAAa,GAAKA,EAAK,WAAa,GAC3D,CACA,SAASjC,GAAS,CAChB,MAAMkC,EAAK,OAAO,UAAU,QAAU,EAAI,OAAY,UAAU,CAAC,CAAC,EAC5D/B,EAAW,CAAC,YAAa,cAAe,WAAW,EACzD,QAASgC,EAAI,EAAGA,EAAI,UAAU,OAAQA,GAAK,EAAG,CAC5C,MAAMC,EAAaD,EAAI,GAAK,UAAU,QAAUA,EAAI,OAAY,UAAUA,CAAC,EAC3E,GAAgCC,GAAe,MAAQ,CAACJ,GAAOI,CAAU,EAAG,CAC1E,MAAMC,EAAY,OAAO,KAAK,OAAOD,CAAU,CAAC,EAAE,OAAOhC,GAAOD,EAAS,QAAQC,CAAG,EAAI,CAAC,EACzF,QAASkC,EAAY,EAAGC,EAAMF,EAAU,OAAQC,EAAYC,EAAKD,GAAa,EAAG,CAC/E,MAAME,EAAUH,EAAUC,CAAS,EAC7BG,EAAO,OAAO,yBAAyBL,EAAYI,CAAO,EAC5DC,IAAS,QAAaA,EAAK,aACzB3C,GAASoC,EAAGM,CAAO,CAAC,GAAK1C,GAASsC,EAAWI,CAAO,CAAC,EACnDJ,EAAWI,CAAO,EAAE,WACtBN,EAAGM,CAAO,EAAIJ,EAAWI,CAAO,EAEhCxC,EAAOkC,EAAGM,CAAO,EAAGJ,EAAWI,CAAO,CAAC,EAEhC,CAAC1C,GAASoC,EAAGM,CAAO,CAAC,GAAK1C,GAASsC,EAAWI,CAAO,CAAC,GAC/DN,EAAGM,CAAO,EAAI,CAAA,EACVJ,EAAWI,CAAO,EAAE,WACtBN,EAAGM,CAAO,EAAIJ,EAAWI,CAAO,EAEhCxC,EAAOkC,EAAGM,CAAO,EAAGJ,EAAWI,CAAO,CAAC,GAGzCN,EAAGM,CAAO,EAAIJ,EAAWI,CAAO,EAGtC,CACF,CACF,CACA,OAAON,CACT,CACA,SAASQ,GAAepB,EAAIqB,EAASC,EAAU,CAC7CtB,EAAG,MAAM,YAAYqB,EAASC,CAAQ,CACxC,CACA,SAASC,GAAqBC,EAAM,CAClC,GAAI,CACF,OAAAC,EACA,eAAAC,EACA,KAAAC,CACJ,EAAMH,EACJ,MAAMvB,EAASZ,EAAS,EAClBuC,EAAgB,CAACH,EAAO,UAC9B,IAAII,EAAY,KACZC,EACJ,MAAMC,EAAWN,EAAO,OAAO,MAC/BA,EAAO,UAAU,MAAM,eAAiB,OACxCxB,EAAO,qBAAqBwB,EAAO,cAAc,EACjD,MAAMO,EAAMN,EAAiBE,EAAgB,OAAS,OAChDK,EAAe,CAACC,EAASvD,IACtBqD,IAAQ,QAAUE,GAAWvD,GAAUqD,IAAQ,QAAUE,GAAWvD,EAEvEwD,EAAU,IAAM,CACpBL,EAAO,IAAI,KAAI,EAAG,QAAO,EACrBD,IAAc,OAChBA,EAAYC,GAEd,MAAMM,EAAW,KAAK,IAAI,KAAK,KAAKN,EAAOD,GAAaE,EAAU,CAAC,EAAG,CAAC,EACjEM,EAAe,GAAM,KAAK,IAAID,EAAW,KAAK,EAAE,EAAI,EAC1D,IAAIE,EAAkBV,EAAgBS,GAAgBX,EAAiBE,GAOvE,GANIK,EAAaK,EAAiBZ,CAAc,IAC9CY,EAAkBZ,GAEpBD,EAAO,UAAU,SAAS,CACxB,CAACE,CAAI,EAAGW,CACd,CAAK,EACGL,EAAaK,EAAiBZ,CAAc,EAAG,CACjDD,EAAO,UAAU,MAAM,SAAW,SAClCA,EAAO,UAAU,MAAM,eAAiB,GACxC,WAAW,IAAM,CACfA,EAAO,UAAU,MAAM,SAAW,GAClCA,EAAO,UAAU,SAAS,CACxB,CAACE,CAAI,EAAGW,CAClB,CAAS,CACH,CAAC,EACDrC,EAAO,qBAAqBwB,EAAO,cAAc,EACjD,MACF,CACAA,EAAO,eAAiBxB,EAAO,sBAAsBkC,CAAO,CAC9D,EACAA,EAAO,CACT,CACA,SAASI,GAAoBC,EAAS,CACpC,OAAOA,EAAQ,cAAc,yBAAyB,GAAKA,EAAQ,YAAcA,EAAQ,WAAW,cAAc,yBAAyB,GAAKA,CAClJ,CACA,SAASC,EAAgBC,EAASC,EAAU,CACtCA,IAAa,SACfA,EAAW,IAEb,MAAM1C,EAASZ,EAAS,EAClBuD,EAAW,CAAC,GAAGF,EAAQ,QAAQ,EAIrC,OAHIzC,EAAO,iBAAmByC,aAAmB,iBAC/CE,EAAS,KAAK,GAAGF,EAAQ,iBAAgB,CAAE,EAExCC,EAGEC,EAAS,OAAO5C,GAAMA,EAAG,QAAQ2C,CAAQ,CAAC,EAFxCC,CAGX,CACA,SAASC,GAAqB7C,EAAI8C,EAAM,CAEtC,MAAMC,EAAgB,CAACD,CAAI,EAC3B,KAAOC,EAAc,OAAS,GAAG,CAC/B,MAAMC,EAAiBD,EAAc,MAAK,EAC1C,GAAI/C,IAAOgD,EACT,MAAO,GAETD,EAAc,KAAK,GAAGC,EAAe,SAAU,GAAIA,EAAe,WAAaA,EAAe,WAAW,SAAW,CAAA,EAAK,GAAIA,EAAe,iBAAmBA,EAAe,iBAAgB,EAAK,CAAA,CAAG,CACxM,CACF,CACA,SAASC,GAAiBjD,EAAIkD,EAAQ,CACpC,MAAMjD,EAASZ,EAAS,EACxB,IAAI8D,EAAUD,EAAO,SAASlD,CAAE,EAChC,MAAI,CAACmD,GAAWlD,EAAO,iBAAmBiD,aAAkB,kBAE1DC,EADiB,CAAC,GAAGD,EAAO,iBAAgB,CAAE,EAC3B,SAASlD,CAAE,EACzBmD,IACHA,EAAUN,GAAqB7C,EAAIkD,CAAM,IAGtCC,CACT,CACA,SAASC,GAAYC,EAAM,CACzB,GAAI,CACF,QAAQ,KAAKA,CAAI,EACjB,MACF,MAAc,CAEd,CACF,CACA,SAASC,GAAcC,EAAK/D,EAAS,CAC/BA,IAAY,SACdA,EAAU,CAAA,GAEZ,MAAMQ,EAAK,SAAS,cAAcuD,CAAG,EACrC,OAAAvD,EAAG,UAAU,IAAI,GAAI,MAAM,QAAQR,CAAO,EAAIA,EAAUD,GAAgBC,CAAO,CAAE,EAC1EQ,CACT,CAeA,SAASwD,GAAexD,EAAI2C,EAAU,CACpC,MAAMc,EAAU,CAAA,EAChB,KAAOzD,EAAG,wBAAwB,CAChC,MAAM0D,EAAO1D,EAAG,uBACZ2C,EACEe,EAAK,QAAQf,CAAQ,GAAGc,EAAQ,KAAKC,CAAI,EACxCD,EAAQ,KAAKC,CAAI,EACxB1D,EAAK0D,CACP,CACA,OAAOD,CACT,CACA,SAASE,GAAe3D,EAAI2C,EAAU,CACpC,MAAMiB,EAAU,CAAA,EAChB,KAAO5D,EAAG,oBAAoB,CAC5B,MAAM6D,EAAO7D,EAAG,mBACZ2C,EACEkB,EAAK,QAAQlB,CAAQ,GAAGiB,EAAQ,KAAKC,CAAI,EACxCD,EAAQ,KAAKC,CAAI,EACxB7D,EAAK6D,CACP,CACA,OAAOD,CACT,CACA,SAASE,EAAa9D,EAAI+D,EAAM,CAE9B,OADe1E,EAAS,EACV,iBAAiBW,EAAI,IAAI,EAAE,iBAAiB+D,CAAI,CAChE,CACA,SAASC,GAAahE,EAAI,CACxB,IAAIiE,EAAQjE,EACRa,EACJ,GAAIoD,EAAO,CAGT,IAFApD,EAAI,GAEIoD,EAAQA,EAAM,mBAAqB,MACrCA,EAAM,WAAa,IAAGpD,GAAK,GAEjC,OAAOA,CACT,CAEF,CACA,SAASqD,GAAelE,EAAI2C,EAAU,CACpC,MAAMwB,EAAU,CAAA,EAChB,IAAIjB,EAASlD,EAAG,cAChB,KAAOkD,GACDP,EACEO,EAAO,QAAQP,CAAQ,GAAGwB,EAAQ,KAAKjB,CAAM,EAEjDiB,EAAQ,KAAKjB,CAAM,EAErBA,EAASA,EAAO,cAElB,OAAOiB,CACT,CAWA,SAASC,GAAiBpE,EAAIqE,EAAMC,EAAgB,CAClD,MAAMrE,EAASZ,EAAS,EAEtB,OAAOW,EAAGqE,IAAS,QAAU,cAAgB,cAAc,EAAI,WAAWpE,EAAO,iBAAiBD,EAAI,IAAI,EAAE,iBAAiBqE,IAAS,QAAU,eAAiB,YAAY,CAAC,EAAI,WAAWpE,EAAO,iBAAiBD,EAAI,IAAI,EAAE,iBAAiBqE,IAAS,QAAU,cAAgB,eAAe,CAAC,CAGvS,CACA,SAASE,EAAkBvE,EAAI,CAC7B,OAAQ,MAAM,QAAQA,CAAE,EAAIA,EAAK,CAACA,CAAE,GAAG,OAAO,GAAK,CAAC,CAAC,CAAC,CACxD,CACA,SAASwE,GAAa/C,EAAQ,CAC5B,OAAOgD,GACD,KAAK,IAAIA,CAAC,EAAI,GAAKhD,EAAO,SAAWA,EAAO,QAAQ,WAAa,KAAK,IAAIgD,CAAC,EAAI,KAAO,EACjFA,EAAI,KAENA,CAEX,CACA,SAASC,GAAa1E,EAAI2E,EAAM,CAC1BA,IAAS,SACXA,EAAO,IAEL,OAAO,aAAiB,IAC1B3E,EAAG,UAAY,aAAa,aAAa,OAAQ,CAC/C,WAAY4E,GAAKA,CACvB,CAAK,EAAE,WAAWD,CAAI,EAElB3E,EAAG,UAAY2E,CAEnB,CCjVA,IAAIE,GACJ,SAASC,IAAc,CACrB,MAAM7E,EAASZ,EAAS,EAClB0F,EAAW/F,EAAW,EAC5B,MAAO,CACL,aAAc+F,EAAS,iBAAmBA,EAAS,gBAAgB,OAAS,mBAAoBA,EAAS,gBAAgB,MACzH,MAAO,CAAC,EAAE,iBAAkB9E,GAAUA,EAAO,eAAiB8E,aAAoB9E,EAAO,cAC7F,CACA,CACA,SAAS+E,IAAa,CACpB,OAAKH,KACHA,GAAUC,GAAW,GAEhBD,EACT,CAEA,IAAII,GACJ,SAASC,GAAWC,EAAO,CACzB,GAAI,CACF,UAAAC,CACJ,EAAMD,IAAU,OAAS,CAAA,EAAKA,EAC5B,MAAMN,EAAUG,GAAU,EACpB/E,EAASZ,EAAS,EAClBgG,EAAWpF,EAAO,UAAU,SAC5BqF,EAAKF,GAAanF,EAAO,UAAU,UACnCsF,EAAS,CACb,IAAK,GACL,QAAS,EACb,EACQC,EAAcvF,EAAO,OAAO,MAC5BwF,EAAexF,EAAO,OAAO,OAC7ByF,EAAUJ,EAAG,MAAM,6BAA6B,EACtD,IAAIK,EAAOL,EAAG,MAAM,sBAAsB,EAC1C,MAAMM,EAAON,EAAG,MAAM,yBAAyB,EACzCO,EAAS,CAACF,GAAQL,EAAG,MAAM,4BAA4B,EACvDQ,EAAUT,IAAa,QAC7B,IAAIU,EAAQV,IAAa,WAGzB,MAAMW,EAAc,CAAC,YAAa,YAAa,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAU,EACrK,MAAI,CAACL,GAAQI,GAASlB,EAAQ,OAASmB,EAAY,QAAQ,GAAGR,CAAW,IAAIC,CAAY,EAAE,GAAK,IAC9FE,EAAOL,EAAG,MAAM,qBAAqB,EAChCK,IAAMA,EAAO,CAAC,EAAG,EAAG,QAAQ,GACjCI,EAAQ,IAINL,GAAW,CAACI,IACdP,EAAO,GAAK,UACZA,EAAO,QAAU,KAEfI,GAAQE,GAAUD,KACpBL,EAAO,GAAK,MACZA,EAAO,IAAM,IAIRA,CACT,CACA,SAASU,GAAUC,EAAW,CAC5B,OAAIA,IAAc,SAChBA,EAAY,CAAA,GAETjB,KACHA,GAAeC,GAAWgB,CAAS,GAE9BjB,EACT,CAEA,IAAIkB,GACJ,SAASC,IAAc,CACrB,MAAMnG,EAASZ,EAAS,EAClBkG,EAASU,GAAS,EACxB,IAAII,EAAqB,GACzB,SAASC,GAAW,CAClB,MAAMhB,EAAKrF,EAAO,UAAU,UAAU,YAAW,EACjD,OAAOqF,EAAG,QAAQ,QAAQ,GAAK,GAAKA,EAAG,QAAQ,QAAQ,EAAI,GAAKA,EAAG,QAAQ,SAAS,EAAI,CAC1F,CACA,GAAIgB,EAAQ,EAAI,CACd,MAAMhB,EAAK,OAAOrF,EAAO,UAAU,SAAS,EAC5C,GAAIqF,EAAG,SAAS,UAAU,EAAG,CAC3B,KAAM,CAACiB,EAAOC,CAAK,EAAIlB,EAAG,MAAM,UAAU,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,IAAImB,GAAO,OAAOA,CAAG,CAAC,EAC9FJ,EAAqBE,EAAQ,IAAMA,IAAU,IAAMC,EAAQ,CAC7D,CACF,CACA,MAAME,EAAY,+CAA+C,KAAKzG,EAAO,UAAU,SAAS,EAC1F0G,EAAkBL,EAAQ,EAC1BM,EAAYD,GAAmBD,GAAanB,EAAO,IACzD,MAAO,CACL,SAAUc,GAAsBM,EAChC,mBAAAN,EACA,UAAAO,EACA,UAAAF,CACJ,CACA,CACA,SAASG,IAAa,CACpB,OAAKV,KACHA,GAAUC,GAAW,GAEhBD,EACT,CAEA,SAASW,GAAOtF,EAAM,CACpB,GAAI,CACF,OAAAC,EACA,GAAAsF,EACA,KAAAC,CACJ,EAAMxF,EACJ,MAAMvB,EAASZ,EAAS,EACxB,IAAI4H,EAAW,KACXC,EAAiB,KACrB,MAAMC,EAAgB,IAAM,CACtB,CAAC1F,GAAUA,EAAO,WAAa,CAACA,EAAO,cAC3CuF,EAAK,cAAc,EACnBA,EAAK,QAAQ,EACf,EACMI,EAAiB,IAAM,CACvB,CAAC3F,GAAUA,EAAO,WAAa,CAACA,EAAO,cAC3CwF,EAAW,IAAI,eAAeI,GAAW,CACvCH,EAAiBjH,EAAO,sBAAsB,IAAM,CAClD,KAAM,CACJ,MAAAqH,EACA,OAAAC,CACV,EAAY9F,EACJ,IAAI+F,EAAWF,EACXG,EAAYF,EAChBF,EAAQ,QAAQK,GAAS,CACvB,GAAI,CACF,eAAAC,EACA,YAAAC,EACA,OAAAjJ,CACZ,EAAc+I,EACA/I,GAAUA,IAAW8C,EAAO,KAChC+F,EAAWI,EAAcA,EAAY,OAASD,EAAe,CAAC,GAAKA,GAAgB,WACnFF,EAAYG,EAAcA,EAAY,QAAUD,EAAe,CAAC,GAAKA,GAAgB,UACvF,CAAC,GACGH,IAAaF,GAASG,IAAcF,IACtCJ,EAAa,CAEjB,CAAC,CACH,CAAC,EACDF,EAAS,QAAQxF,EAAO,EAAE,EAC5B,EACMoG,EAAiB,IAAM,CACvBX,GACFjH,EAAO,qBAAqBiH,CAAc,EAExCD,GAAYA,EAAS,WAAaxF,EAAO,KAC3CwF,EAAS,UAAUxF,EAAO,EAAE,EAC5BwF,EAAW,KAEf,EACMa,EAA2B,IAAM,CACjC,CAACrG,GAAUA,EAAO,WAAa,CAACA,EAAO,aAC3CuF,EAAK,mBAAmB,CAC1B,EACAD,EAAG,OAAQ,IAAM,CACf,GAAItF,EAAO,OAAO,gBAAkB,OAAOxB,EAAO,eAAmB,IAAa,CAChFmH,EAAc,EACd,MACF,CACAnH,EAAO,iBAAiB,SAAUkH,CAAa,EAC/ClH,EAAO,iBAAiB,oBAAqB6H,CAAwB,CACvE,CAAC,EACDf,EAAG,UAAW,IAAM,CAClBc,EAAc,EACd5H,EAAO,oBAAoB,SAAUkH,CAAa,EAClDlH,EAAO,oBAAoB,oBAAqB6H,CAAwB,CAC1E,CAAC,CACH,CAEA,SAASC,GAASvG,EAAM,CACtB,GAAI,CACF,OAAAC,EACA,aAAAuG,EACA,GAAAjB,EACA,KAAAC,CACJ,EAAMxF,EACJ,MAAMyG,EAAY,CAAA,EACZhI,EAASZ,EAAS,EAClB6I,EAAS,SAAUvJ,EAAQwJ,EAAS,CACpCA,IAAY,SACdA,EAAU,CAAA,GAEZ,MAAMC,EAAenI,EAAO,kBAAoBA,EAAO,uBACjDgH,EAAW,IAAImB,EAAaC,GAAa,CAI7C,GAAI5G,EAAO,oBAAqB,OAChC,GAAI4G,EAAU,SAAW,EAAG,CAC1BrB,EAAK,iBAAkBqB,EAAU,CAAC,CAAC,EACnC,MACF,CACA,MAAMC,EAAiB,UAA0B,CAC/CtB,EAAK,iBAAkBqB,EAAU,CAAC,CAAC,CACrC,EACIpI,EAAO,sBACTA,EAAO,sBAAsBqI,CAAc,EAE3CrI,EAAO,WAAWqI,EAAgB,CAAC,CAEvC,CAAC,EACDrB,EAAS,QAAQtI,EAAQ,CACvB,WAAY,OAAOwJ,EAAQ,WAAe,IAAc,GAAOA,EAAQ,WACvE,UAAW1G,EAAO,YAAc,OAAO0G,EAAQ,UAAc,IAAc,GAAOA,GAAS,UAC3F,cAAe,OAAOA,EAAQ,cAAkB,IAAc,GAAOA,EAAQ,aACnF,CAAK,EACDF,EAAU,KAAKhB,CAAQ,CACzB,EACMsB,EAAO,IAAM,CACjB,GAAK9G,EAAO,OAAO,SACnB,IAAIA,EAAO,OAAO,eAAgB,CAChC,MAAM+G,EAAmBtE,GAAezC,EAAO,MAAM,EACrD,QAASZ,EAAI,EAAGA,EAAI2H,EAAiB,OAAQ3H,GAAK,EAChDqH,EAAOM,EAAiB3H,CAAC,CAAC,CAE9B,CAEAqH,EAAOzG,EAAO,OAAQ,CACpB,UAAWA,EAAO,OAAO,oBAC/B,CAAK,EAGDyG,EAAOzG,EAAO,UAAW,CACvB,WAAY,EAClB,CAAK,EACH,EACMgH,EAAU,IAAM,CACpBR,EAAU,QAAQhB,GAAY,CAC5BA,EAAS,WAAU,CACrB,CAAC,EACDgB,EAAU,OAAO,EAAGA,EAAU,MAAM,CACtC,EACAD,EAAa,CACX,SAAU,GACV,eAAgB,GAChB,qBAAsB,EAC1B,CAAG,EACDjB,EAAG,OAAQwB,CAAI,EACfxB,EAAG,UAAW0B,CAAO,CACvB,CAIA,IAAIC,GAAgB,CAClB,GAAGC,EAAQC,EAASC,EAAU,CAC5B,MAAMC,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,OAAOF,GAAY,WAAY,OAAOE,EAC1C,MAAMC,EAASF,EAAW,UAAY,OACtC,OAAAF,EAAO,MAAM,GAAG,EAAE,QAAQK,GAAS,CAC5BF,EAAK,gBAAgBE,CAAK,IAAGF,EAAK,gBAAgBE,CAAK,EAAI,CAAA,GAChEF,EAAK,gBAAgBE,CAAK,EAAED,CAAM,EAAEH,CAAO,CAC7C,CAAC,EACME,CACT,EACA,KAAKH,EAAQC,EAASC,EAAU,CAC9B,MAAMC,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,OAAOF,GAAY,WAAY,OAAOE,EAC1C,SAASG,GAAc,CACrBH,EAAK,IAAIH,EAAQM,CAAW,EACxBA,EAAY,gBACd,OAAOA,EAAY,eAErB,QAASC,EAAO,UAAU,OAAQC,EAAO,IAAI,MAAMD,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,CAAI,EAAI,UAAUA,CAAI,EAE7BR,EAAQ,MAAME,EAAMK,CAAI,CAC1B,CACA,OAAAF,EAAY,eAAiBL,EACtBE,EAAK,GAAGH,EAAQM,EAAaJ,CAAQ,CAC9C,EACA,MAAMD,EAASC,EAAU,CACvB,MAAMC,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,OAAOF,GAAY,WAAY,OAAOE,EAC1C,MAAMC,EAASF,EAAW,UAAY,OACtC,OAAIC,EAAK,mBAAmB,QAAQF,CAAO,EAAI,GAC7CE,EAAK,mBAAmBC,CAAM,EAAEH,CAAO,EAElCE,CACT,EACA,OAAOF,EAAS,CACd,MAAME,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,CAACA,EAAK,mBAAoB,OAAOA,EACrC,MAAMO,EAAQP,EAAK,mBAAmB,QAAQF,CAAO,EACrD,OAAIS,GAAS,GACXP,EAAK,mBAAmB,OAAOO,EAAO,CAAC,EAElCP,CACT,EACA,IAAIH,EAAQC,EAAS,CACnB,MAAME,EAAO,KAEb,MADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,CAACA,EAAK,iBACVH,EAAO,MAAM,GAAG,EAAE,QAAQK,GAAS,CAC7B,OAAOJ,EAAY,IACrBE,EAAK,gBAAgBE,CAAK,EAAI,CAAA,EACrBF,EAAK,gBAAgBE,CAAK,GACnCF,EAAK,gBAAgBE,CAAK,EAAE,QAAQ,CAACM,EAAcD,IAAU,EACvDC,IAAiBV,GAAWU,EAAa,gBAAkBA,EAAa,iBAAmBV,IAC7FE,EAAK,gBAAgBE,CAAK,EAAE,OAAOK,EAAO,CAAC,CAE/C,CAAC,CAEL,CAAC,EACMP,CACT,EACA,MAAO,CACL,MAAMA,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,CAACA,EAAK,gBAAiB,OAAOA,EAClC,IAAIH,EACAY,EACAC,EACJ,QAASC,EAAQ,UAAU,OAAQN,EAAO,IAAI,MAAMM,CAAK,EAAGC,EAAQ,EAAGA,EAAQD,EAAOC,IACpFP,EAAKO,CAAK,EAAI,UAAUA,CAAK,EAE/B,OAAI,OAAOP,EAAK,CAAC,GAAM,UAAY,MAAM,QAAQA,EAAK,CAAC,CAAC,GACtDR,EAASQ,EAAK,CAAC,EACfI,EAAOJ,EAAK,MAAM,EAAGA,EAAK,MAAM,EAChCK,EAAUV,IAEVH,EAASQ,EAAK,CAAC,EAAE,OACjBI,EAAOJ,EAAK,CAAC,EAAE,KACfK,EAAUL,EAAK,CAAC,EAAE,SAAWL,GAE/BS,EAAK,QAAQC,CAAO,GACA,MAAM,QAAQb,CAAM,EAAIA,EAASA,EAAO,MAAM,GAAG,GACzD,QAAQK,GAAS,CACvBF,EAAK,oBAAsBA,EAAK,mBAAmB,QACrDA,EAAK,mBAAmB,QAAQQ,GAAgB,CAC9CA,EAAa,MAAME,EAAS,CAACR,EAAO,GAAGO,CAAI,CAAC,CAC9C,CAAC,EAECT,EAAK,iBAAmBA,EAAK,gBAAgBE,CAAK,GACpDF,EAAK,gBAAgBE,CAAK,EAAE,QAAQM,GAAgB,CAClDA,EAAa,MAAME,EAASD,CAAI,CAClC,CAAC,CAEL,CAAC,EACMT,CACT,CACF,EAEA,SAASa,IAAa,CACpB,MAAMlI,EAAS,KACf,IAAI6F,EACAC,EACJ,MAAMvH,EAAKyB,EAAO,GACd,OAAOA,EAAO,OAAO,MAAU,KAAeA,EAAO,OAAO,QAAU,KACxE6F,EAAQ7F,EAAO,OAAO,MAEtB6F,EAAQtH,EAAG,YAET,OAAOyB,EAAO,OAAO,OAAW,KAAeA,EAAO,OAAO,SAAW,KAC1E8F,EAAS9F,EAAO,OAAO,OAEvB8F,EAASvH,EAAG,aAEV,EAAAsH,IAAU,GAAK7F,EAAO,aAAY,GAAM8F,IAAW,GAAK9F,EAAO,gBAKnE6F,EAAQA,EAAQ,SAASxD,EAAa9D,EAAI,cAAc,GAAK,EAAG,EAAE,EAAI,SAAS8D,EAAa9D,EAAI,eAAe,GAAK,EAAG,EAAE,EACzHuH,EAASA,EAAS,SAASzD,EAAa9D,EAAI,aAAa,GAAK,EAAG,EAAE,EAAI,SAAS8D,EAAa9D,EAAI,gBAAgB,GAAK,EAAG,EAAE,EACvH,OAAO,MAAMsH,CAAK,IAAGA,EAAQ,GAC7B,OAAO,MAAMC,CAAM,IAAGA,EAAS,GACnC,OAAO,OAAO9F,EAAQ,CACpB,MAAA6F,EACA,OAAAC,EACA,KAAM9F,EAAO,aAAY,EAAK6F,EAAQC,CAC1C,CAAG,EACH,CAEA,SAASqC,IAAe,CACtB,MAAMnI,EAAS,KACf,SAASoI,EAA0BlJ,EAAMmJ,EAAO,CAC9C,OAAO,WAAWnJ,EAAK,iBAAiBc,EAAO,kBAAkBqI,CAAK,CAAC,GAAK,CAAC,CAC/E,CACA,MAAMC,EAAStI,EAAO,OAChB,CACJ,UAAAuI,EACA,SAAAC,EACA,KAAMC,EACN,aAAcC,EACd,SAAAC,CACJ,EAAM3I,EACE4I,EAAY5I,EAAO,SAAWsI,EAAO,QAAQ,QAC7CO,EAAuBD,EAAY5I,EAAO,QAAQ,OAAO,OAASA,EAAO,OAAO,OAChF8I,EAAS9H,EAAgBwH,EAAU,IAAIxI,EAAO,OAAO,UAAU,gBAAgB,EAC/E+I,EAAeH,EAAY5I,EAAO,QAAQ,OAAO,OAAS8I,EAAO,OACvE,IAAIE,EAAW,CAAA,EACf,MAAMC,EAAa,CAAA,EACbC,EAAkB,CAAA,EACxB,IAAIC,EAAeb,EAAO,mBACtB,OAAOa,GAAiB,aAC1BA,EAAeb,EAAO,mBAAmB,KAAKtI,CAAM,GAEtD,IAAIoJ,EAAcd,EAAO,kBACrB,OAAOc,GAAgB,aACzBA,EAAcd,EAAO,kBAAkB,KAAKtI,CAAM,GAEpD,MAAMqJ,EAAyBrJ,EAAO,SAAS,OACzCsJ,EAA2BtJ,EAAO,WAAW,OACnD,IAAIuJ,EAAejB,EAAO,aACtBkB,EAAgB,CAACL,EACjBM,EAAgB,EAChB7B,EAAQ,EACZ,GAAI,OAAOa,EAAe,IACxB,OAEE,OAAOc,GAAiB,UAAYA,EAAa,QAAQ,GAAG,GAAK,EACnEA,EAAe,WAAWA,EAAa,QAAQ,IAAK,EAAE,CAAC,EAAI,IAAMd,EACxD,OAAOc,GAAiB,WACjCA,EAAe,WAAWA,CAAY,GAExCvJ,EAAO,YAAc,CAACuJ,EAGtBT,EAAO,QAAQ/H,GAAW,CACpB2H,EACF3H,EAAQ,MAAM,WAAa,GAE3BA,EAAQ,MAAM,YAAc,GAE9BA,EAAQ,MAAM,aAAe,GAC7BA,EAAQ,MAAM,UAAY,EAC5B,CAAC,EAGGuH,EAAO,gBAAkBA,EAAO,UAClC3I,GAAe4I,EAAW,kCAAmC,EAAE,EAC/D5I,GAAe4I,EAAW,iCAAkC,EAAE,GAEhE,MAAMmB,EAAcpB,EAAO,MAAQA,EAAO,KAAK,KAAO,GAAKtI,EAAO,KAC9D0J,EACF1J,EAAO,KAAK,WAAW8I,CAAM,EACpB9I,EAAO,MAChBA,EAAO,KAAK,YAAW,EAIzB,IAAI2J,EACJ,MAAMC,EAAuBtB,EAAO,gBAAkB,QAAUA,EAAO,aAAe,OAAO,KAAKA,EAAO,WAAW,EAAE,OAAOjL,GACpH,OAAOiL,EAAO,YAAYjL,CAAG,EAAE,cAAkB,GACzD,EAAE,OAAS,EACZ,QAAS+B,EAAI,EAAGA,EAAI2J,EAAc3J,GAAK,EAAG,CACxCuK,EAAY,EACZ,IAAIE,EAKJ,GAJIf,EAAO1J,CAAC,IAAGyK,EAAQf,EAAO1J,CAAC,GAC3BsK,GACF1J,EAAO,KAAK,YAAYZ,EAAGyK,EAAOf,CAAM,EAEtC,EAAAA,EAAO1J,CAAC,GAAKiD,EAAawH,EAAO,SAAS,IAAM,QAEpD,IAAIvB,EAAO,gBAAkB,OAAQ,CAC/BsB,IACFd,EAAO1J,CAAC,EAAE,MAAMY,EAAO,kBAAkB,OAAO,CAAC,EAAI,IAEvD,MAAM8J,EAAc,iBAAiBD,CAAK,EACpCE,EAAmBF,EAAM,MAAM,UAC/BG,EAAyBH,EAAM,MAAM,gBAO3C,GANIE,IACFF,EAAM,MAAM,UAAY,QAEtBG,IACFH,EAAM,MAAM,gBAAkB,QAE5BvB,EAAO,aACTqB,EAAY3J,EAAO,aAAY,EAAK2C,GAAiBkH,EAAO,OAAa,EAAIlH,GAAiBkH,EAAO,QAAc,MAC9G,CAEL,MAAMhE,EAAQuC,EAA0B0B,EAAa,OAAO,EACtDG,EAAc7B,EAA0B0B,EAAa,cAAc,EACnEI,EAAe9B,EAA0B0B,EAAa,eAAe,EACrEK,EAAa/B,EAA0B0B,EAAa,aAAa,EACjEM,EAAchC,EAA0B0B,EAAa,cAAc,EACnEO,EAAYP,EAAY,iBAAiB,YAAY,EAC3D,GAAIO,GAAaA,IAAc,aAC7BV,EAAY9D,EAAQsE,EAAaC,MAC5B,CACL,KAAM,CACJ,YAAAE,EACA,YAAAC,EACZ,EAAcV,EACJF,EAAY9D,EAAQoE,EAAcC,EAAeC,EAAaC,GAAeG,GAAcD,EAC7F,CACF,CACIP,IACFF,EAAM,MAAM,UAAYE,GAEtBC,IACFH,EAAM,MAAM,gBAAkBG,GAE5B1B,EAAO,eAAcqB,EAAY,KAAK,MAAMA,CAAS,EAC3D,MACEA,GAAalB,GAAcH,EAAO,cAAgB,GAAKiB,GAAgBjB,EAAO,cAC1EA,EAAO,eAAcqB,EAAY,KAAK,MAAMA,CAAS,GACrDb,EAAO1J,CAAC,IACV0J,EAAO1J,CAAC,EAAE,MAAMY,EAAO,kBAAkB,OAAO,CAAC,EAAI,GAAG2J,CAAS,MAGjEb,EAAO1J,CAAC,IACV0J,EAAO1J,CAAC,EAAE,gBAAkBuK,GAE9BT,EAAgB,KAAKS,CAAS,EAC1BrB,EAAO,gBACTkB,EAAgBA,EAAgBG,EAAY,EAAIF,EAAgB,EAAIF,EAChEE,IAAkB,GAAKrK,IAAM,IAAGoK,EAAgBA,EAAgBf,EAAa,EAAIc,GACjFnK,IAAM,IAAGoK,EAAgBA,EAAgBf,EAAa,EAAIc,GAC1D,KAAK,IAAIC,CAAa,EAAI,EAAI,MAAMA,EAAgB,GACpDlB,EAAO,eAAckB,EAAgB,KAAK,MAAMA,CAAa,GAC7D5B,EAAQU,EAAO,iBAAmB,GAAGU,EAAS,KAAKQ,CAAa,EACpEP,EAAW,KAAKO,CAAa,IAEzBlB,EAAO,eAAckB,EAAgB,KAAK,MAAMA,CAAa,IAC5D5B,EAAQ,KAAK,IAAI5H,EAAO,OAAO,mBAAoB4H,CAAK,GAAK5H,EAAO,OAAO,iBAAmB,GAAGgJ,EAAS,KAAKQ,CAAa,EACjIP,EAAW,KAAKO,CAAa,EAC7BA,EAAgBA,EAAgBG,EAAYJ,GAE9CvJ,EAAO,aAAe2J,EAAYJ,EAClCE,EAAgBE,EAChB/B,GAAS,EACX,CAaA,GAZA5H,EAAO,YAAc,KAAK,IAAIA,EAAO,YAAayI,CAAU,EAAIW,EAC5DV,GAAOC,IAAaL,EAAO,SAAW,SAAWA,EAAO,SAAW,eACrEC,EAAU,MAAM,MAAQ,GAAGvI,EAAO,YAAcuJ,CAAY,MAE1DjB,EAAO,iBACTC,EAAU,MAAMvI,EAAO,kBAAkB,OAAO,CAAC,EAAI,GAAGA,EAAO,YAAcuJ,CAAY,MAEvFG,GACF1J,EAAO,KAAK,kBAAkB2J,EAAWX,CAAQ,EAI/C,CAACV,EAAO,eAAgB,CAC1B,MAAMkC,EAAgB,CAAA,EACtB,QAASpL,EAAI,EAAGA,EAAI4J,EAAS,OAAQ5J,GAAK,EAAG,CAC3C,IAAIqL,EAAiBzB,EAAS5J,CAAC,EAC3BkJ,EAAO,eAAcmC,EAAiB,KAAK,MAAMA,CAAc,GAC/DzB,EAAS5J,CAAC,GAAKY,EAAO,YAAcyI,GACtC+B,EAAc,KAAKC,CAAc,CAErC,CACAzB,EAAWwB,EACP,KAAK,MAAMxK,EAAO,YAAcyI,CAAU,EAAI,KAAK,MAAMO,EAASA,EAAS,OAAS,CAAC,CAAC,EAAI,GAC5FA,EAAS,KAAKhJ,EAAO,YAAcyI,CAAU,CAEjD,CACA,GAAIG,GAAaN,EAAO,KAAM,CAC5B,MAAM1F,EAAOsG,EAAgB,CAAC,EAAIK,EAClC,GAAIjB,EAAO,eAAiB,EAAG,CAC7B,MAAMoC,EAAS,KAAK,MAAM1K,EAAO,QAAQ,aAAeA,EAAO,QAAQ,aAAesI,EAAO,cAAc,EACrGqC,EAAY/H,EAAO0F,EAAO,eAChC,QAASlJ,EAAI,EAAGA,EAAIsL,EAAQtL,GAAK,EAC/B4J,EAAS,KAAKA,EAASA,EAAS,OAAS,CAAC,EAAI2B,CAAS,CAE3D,CACA,QAASvL,EAAI,EAAGA,EAAIY,EAAO,QAAQ,aAAeA,EAAO,QAAQ,YAAaZ,GAAK,EAC7EkJ,EAAO,iBAAmB,GAC5BU,EAAS,KAAKA,EAASA,EAAS,OAAS,CAAC,EAAIpG,CAAI,EAEpDqG,EAAW,KAAKA,EAAWA,EAAW,OAAS,CAAC,EAAIrG,CAAI,EACxD5C,EAAO,aAAe4C,CAE1B,CAEA,GADIoG,EAAS,SAAW,IAAGA,EAAW,CAAC,CAAC,GACpCO,IAAiB,EAAG,CACtB,MAAMlM,EAAM2C,EAAO,aAAY,GAAM0I,EAAM,aAAe1I,EAAO,kBAAkB,aAAa,EAChG8I,EAAO,OAAO,CAAC8B,EAAGC,IACZ,CAACvC,EAAO,SAAWA,EAAO,KAAa,GACvCuC,IAAe/B,EAAO,OAAS,CAIpC,EAAE,QAAQ/H,GAAW,CACpBA,EAAQ,MAAM1D,CAAG,EAAI,GAAGkM,CAAY,IACtC,CAAC,CACH,CACA,GAAIjB,EAAO,gBAAkBA,EAAO,qBAAsB,CACxD,IAAIwC,EAAgB,EACpB5B,EAAgB,QAAQ6B,GAAkB,CACxCD,GAAiBC,GAAkBxB,GAAgB,EACrD,CAAC,EACDuB,GAAiBvB,EACjB,MAAMyB,EAAUF,EAAgBrC,EAAaqC,EAAgBrC,EAAa,EAC1EO,EAAWA,EAAS,IAAIiC,GAClBA,GAAQ,EAAU,CAAC9B,EACnB8B,EAAOD,EAAgBA,EAAU5B,EAC9B6B,CACR,CACH,CACA,GAAI3C,EAAO,yBAA0B,CACnC,IAAIwC,EAAgB,EACpB5B,EAAgB,QAAQ6B,GAAkB,CACxCD,GAAiBC,GAAkBxB,GAAgB,EACrD,CAAC,EACDuB,GAAiBvB,EACjB,MAAM2B,GAAc5C,EAAO,oBAAsB,IAAMA,EAAO,mBAAqB,GACnF,GAAIwC,EAAgBI,EAAazC,EAAY,CAC3C,MAAM0C,GAAmB1C,EAAaqC,EAAgBI,GAAc,EACpElC,EAAS,QAAQ,CAACiC,EAAMG,IAAc,CACpCpC,EAASoC,CAAS,EAAIH,EAAOE,CAC/B,CAAC,EACDlC,EAAW,QAAQ,CAACgC,EAAMG,IAAc,CACtCnC,EAAWmC,CAAS,EAAIH,EAAOE,CACjC,CAAC,CACH,CACF,CAOA,GANA,OAAO,OAAOnL,EAAQ,CACpB,OAAA8I,EACA,SAAAE,EACA,WAAAC,EACA,gBAAAC,CACJ,CAAG,EACGZ,EAAO,gBAAkBA,EAAO,SAAW,CAACA,EAAO,qBAAsB,CAC3E3I,GAAe4I,EAAW,kCAAmC,GAAG,CAACS,EAAS,CAAC,CAAC,IAAI,EAChFrJ,GAAe4I,EAAW,iCAAkC,GAAGvI,EAAO,KAAO,EAAIkJ,EAAgBA,EAAgB,OAAS,CAAC,EAAI,CAAC,IAAI,EACpI,MAAMmC,EAAgB,CAACrL,EAAO,SAAS,CAAC,EAClCsL,EAAkB,CAACtL,EAAO,WAAW,CAAC,EAC5CA,EAAO,SAAWA,EAAO,SAAS,IAAIgD,GAAKA,EAAIqI,CAAa,EAC5DrL,EAAO,WAAaA,EAAO,WAAW,IAAIgD,GAAKA,EAAIsI,CAAe,CACpE,CAeA,GAdIvC,IAAiBF,GACnB7I,EAAO,KAAK,oBAAoB,EAE9BgJ,EAAS,SAAWK,IAClBrJ,EAAO,OAAO,eAAeA,EAAO,cAAa,EACrDA,EAAO,KAAK,sBAAsB,GAEhCiJ,EAAW,SAAWK,GACxBtJ,EAAO,KAAK,wBAAwB,EAElCsI,EAAO,qBACTtI,EAAO,mBAAkB,EAE3BA,EAAO,KAAK,eAAe,EACvB,CAAC4I,GAAa,CAACN,EAAO,UAAYA,EAAO,SAAW,SAAWA,EAAO,SAAW,QAAS,CAC5F,MAAMiD,EAAsB,GAAGjD,EAAO,sBAAsB,kBACtDkD,EAA6BxL,EAAO,GAAG,UAAU,SAASuL,CAAmB,EAC/ExC,GAAgBT,EAAO,wBACpBkD,GAA4BxL,EAAO,GAAG,UAAU,IAAIuL,CAAmB,EACnEC,GACTxL,EAAO,GAAG,UAAU,OAAOuL,CAAmB,CAElD,CACF,CAEA,SAASE,GAAiBC,EAAO,CAC/B,MAAM1L,EAAS,KACT2L,EAAe,CAAA,EACf/C,EAAY5I,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAC1D,IAAIgG,EAAY,EACZ5G,EACA,OAAOsM,GAAU,SACnB1L,EAAO,cAAc0L,CAAK,EACjBA,IAAU,IACnB1L,EAAO,cAAcA,EAAO,OAAO,KAAK,EAE1C,MAAM4L,EAAkBhE,GAClBgB,EACK5I,EAAO,OAAOA,EAAO,oBAAoB4H,CAAK,CAAC,EAEjD5H,EAAO,OAAO4H,CAAK,EAG5B,GAAI5H,EAAO,OAAO,gBAAkB,QAAUA,EAAO,OAAO,cAAgB,EAC1E,GAAIA,EAAO,OAAO,gBACfA,EAAO,eAAiB,IAAI,QAAQ6J,GAAS,CAC5C8B,EAAa,KAAK9B,CAAK,CACzB,CAAC,MAED,KAAKzK,EAAI,EAAGA,EAAI,KAAK,KAAKY,EAAO,OAAO,aAAa,EAAGZ,GAAK,EAAG,CAC9D,MAAMwI,EAAQ5H,EAAO,YAAcZ,EACnC,GAAIwI,EAAQ5H,EAAO,OAAO,QAAU,CAAC4I,EAAW,MAChD+C,EAAa,KAAKC,EAAgBhE,CAAK,CAAC,CAC1C,MAGF+D,EAAa,KAAKC,EAAgB5L,EAAO,WAAW,CAAC,EAIvD,IAAKZ,EAAI,EAAGA,EAAIuM,EAAa,OAAQvM,GAAK,EACxC,GAAI,OAAOuM,EAAavM,CAAC,EAAM,IAAa,CAC1C,MAAM0G,EAAS6F,EAAavM,CAAC,EAAE,aAC/B4G,EAAYF,EAASE,EAAYF,EAASE,CAC5C,EAIEA,GAAaA,IAAc,KAAGhG,EAAO,UAAU,MAAM,OAAS,GAAGgG,CAAS,KAChF,CAEA,SAAS6F,IAAqB,CAC5B,MAAM7L,EAAS,KACT8I,EAAS9I,EAAO,OAEhB8L,EAAc9L,EAAO,UAAYA,EAAO,aAAY,EAAKA,EAAO,UAAU,WAAaA,EAAO,UAAU,UAAY,EAC1H,QAAS,EAAI,EAAG,EAAI8I,EAAO,OAAQ,GAAK,EACtCA,EAAO,CAAC,EAAE,mBAAqB9I,EAAO,aAAY,EAAK8I,EAAO,CAAC,EAAE,WAAaA,EAAO,CAAC,EAAE,WAAagD,EAAc9L,EAAO,sBAAqB,CAEnJ,CAEA,MAAM+L,GAAuB,CAAChL,EAASiL,EAAWC,IAAc,CAC1DD,GAAa,CAACjL,EAAQ,UAAU,SAASkL,CAAS,EACpDlL,EAAQ,UAAU,IAAIkL,CAAS,EACtB,CAACD,GAAajL,EAAQ,UAAU,SAASkL,CAAS,GAC3DlL,EAAQ,UAAU,OAAOkL,CAAS,CAEtC,EACA,SAASC,GAAqBC,EAAW,CACnCA,IAAc,SAChBA,EAAY,MAAQ,KAAK,WAAa,GAExC,MAAMnM,EAAS,KACTsI,EAAStI,EAAO,OAChB,CACJ,OAAA8I,EACA,aAAcJ,EACd,SAAAM,CACJ,EAAMhJ,EACJ,GAAI8I,EAAO,SAAW,EAAG,OACrB,OAAOA,EAAO,CAAC,EAAE,kBAAsB,KAAa9I,EAAO,mBAAkB,EACjF,IAAIoM,EAAe,CAACD,EAChBzD,IAAK0D,EAAeD,GACxBnM,EAAO,qBAAuB,CAAA,EAC9BA,EAAO,cAAgB,CAAA,EACvB,IAAIuJ,EAAejB,EAAO,aACtB,OAAOiB,GAAiB,UAAYA,EAAa,QAAQ,GAAG,GAAK,EACnEA,EAAe,WAAWA,EAAa,QAAQ,IAAK,EAAE,CAAC,EAAI,IAAMvJ,EAAO,KAC/D,OAAOuJ,GAAiB,WACjCA,EAAe,WAAWA,CAAY,GAExC,QAASnK,EAAI,EAAGA,EAAI0J,EAAO,OAAQ1J,GAAK,EAAG,CACzC,MAAMyK,EAAQf,EAAO1J,CAAC,EACtB,IAAIiN,EAAcxC,EAAM,kBACpBvB,EAAO,SAAWA,EAAO,iBAC3B+D,GAAevD,EAAO,CAAC,EAAE,mBAE3B,MAAMwD,GAAiBF,GAAgB9D,EAAO,eAAiBtI,EAAO,aAAY,EAAK,GAAKqM,IAAgBxC,EAAM,gBAAkBN,GAC9HgD,GAAyBH,EAAepD,EAAS,CAAC,GAAKV,EAAO,eAAiBtI,EAAO,aAAY,EAAK,GAAKqM,IAAgBxC,EAAM,gBAAkBN,GACpJiD,EAAc,EAAEJ,EAAeC,GAC/BI,EAAaD,EAAcxM,EAAO,gBAAgBZ,CAAC,EACnDsN,EAAiBF,GAAe,GAAKA,GAAexM,EAAO,KAAOA,EAAO,gBAAgBZ,CAAC,EAC1FuN,EAAYH,GAAe,GAAKA,EAAcxM,EAAO,KAAO,GAAKyM,EAAa,GAAKA,GAAczM,EAAO,MAAQwM,GAAe,GAAKC,GAAczM,EAAO,KAC3J2M,IACF3M,EAAO,cAAc,KAAK6J,CAAK,EAC/B7J,EAAO,qBAAqB,KAAKZ,CAAC,GAEpC2M,GAAqBlC,EAAO8C,EAAWrE,EAAO,iBAAiB,EAC/DyD,GAAqBlC,EAAO6C,EAAgBpE,EAAO,sBAAsB,EACzEuB,EAAM,SAAWnB,EAAM,CAAC4D,EAAgBA,EACxCzC,EAAM,iBAAmBnB,EAAM,CAAC6D,EAAwBA,CAC1D,CACF,CAEA,SAASK,GAAeT,EAAW,CACjC,MAAMnM,EAAS,KACf,GAAI,OAAOmM,EAAc,IAAa,CACpC,MAAMU,EAAa7M,EAAO,aAAe,GAAK,EAE9CmM,EAAYnM,GAAUA,EAAO,WAAaA,EAAO,UAAY6M,GAAc,CAC7E,CACA,MAAMvE,EAAStI,EAAO,OAChB8M,EAAiB9M,EAAO,aAAY,EAAKA,EAAO,aAAY,EAClE,GAAI,CACF,SAAAW,EACA,YAAAoM,EACA,MAAAC,EACA,aAAAC,CACJ,EAAMjN,EACJ,MAAMkN,EAAeH,EACfI,EAASH,EACf,GAAIF,IAAmB,EACrBnM,EAAW,EACXoM,EAAc,GACdC,EAAQ,OACH,CACLrM,GAAYwL,EAAYnM,EAAO,aAAY,GAAM8M,EACjD,MAAMM,EAAqB,KAAK,IAAIjB,EAAYnM,EAAO,aAAY,CAAE,EAAI,EACnEqN,EAAe,KAAK,IAAIlB,EAAYnM,EAAO,aAAY,CAAE,EAAI,EACnE+M,EAAcK,GAAsBzM,GAAY,EAChDqM,EAAQK,GAAgB1M,GAAY,EAChCyM,IAAoBzM,EAAW,GAC/B0M,IAAc1M,EAAW,EAC/B,CACA,GAAI2H,EAAO,KAAM,CACf,MAAMgF,EAAkBtN,EAAO,oBAAoB,CAAC,EAC9CuN,EAAiBvN,EAAO,oBAAoBA,EAAO,OAAO,OAAS,CAAC,EACpEwN,EAAsBxN,EAAO,WAAWsN,CAAe,EACvDG,EAAqBzN,EAAO,WAAWuN,CAAc,EACrDG,EAAe1N,EAAO,WAAWA,EAAO,WAAW,OAAS,CAAC,EAC7D2N,EAAe,KAAK,IAAIxB,CAAS,EACnCwB,GAAgBH,EAClBP,GAAgBU,EAAeH,GAAuBE,EAEtDT,GAAgBU,EAAeD,EAAeD,GAAsBC,EAElET,EAAe,IAAGA,GAAgB,EACxC,CACA,OAAO,OAAOjN,EAAQ,CACpB,SAAAW,EACA,aAAAsM,EACA,YAAAF,EACA,MAAAC,CACJ,CAAG,GACG1E,EAAO,qBAAuBA,EAAO,gBAAkBA,EAAO,aAAYtI,EAAO,qBAAqBmM,CAAS,EAC/GY,GAAe,CAACG,GAClBlN,EAAO,KAAK,uBAAuB,EAEjCgN,GAAS,CAACG,GACZnN,EAAO,KAAK,iBAAiB,GAE3BkN,GAAgB,CAACH,GAAeI,GAAU,CAACH,IAC7ChN,EAAO,KAAK,UAAU,EAExBA,EAAO,KAAK,WAAYW,CAAQ,CAClC,CAEA,MAAMiN,GAAqB,CAAC7M,EAASiL,EAAWC,IAAc,CACxDD,GAAa,CAACjL,EAAQ,UAAU,SAASkL,CAAS,EACpDlL,EAAQ,UAAU,IAAIkL,CAAS,EACtB,CAACD,GAAajL,EAAQ,UAAU,SAASkL,CAAS,GAC3DlL,EAAQ,UAAU,OAAOkL,CAAS,CAEtC,EACA,SAAS4B,IAAsB,CAC7B,MAAM7N,EAAS,KACT,CACJ,OAAA8I,EACA,OAAAR,EACA,SAAAE,EACA,YAAAsF,CACJ,EAAM9N,EACE4I,EAAY5I,EAAO,SAAWsI,EAAO,QAAQ,QAC7CoB,EAAc1J,EAAO,MAAQsI,EAAO,MAAQA,EAAO,KAAK,KAAO,EAC/DyF,EAAmB7M,GAChBF,EAAgBwH,EAAU,IAAIF,EAAO,UAAU,GAAGpH,CAAQ,iBAAiBA,CAAQ,EAAE,EAAE,CAAC,EAEjG,IAAI8M,EACAC,EACAC,EACJ,GAAItF,EACF,GAAIN,EAAO,KAAM,CACf,IAAIuC,EAAaiD,EAAc9N,EAAO,QAAQ,aAC1C6K,EAAa,IAAGA,EAAa7K,EAAO,QAAQ,OAAO,OAAS6K,GAC5DA,GAAc7K,EAAO,QAAQ,OAAO,SAAQ6K,GAAc7K,EAAO,QAAQ,OAAO,QACpFgO,EAAcD,EAAiB,6BAA6BlD,CAAU,IAAI,CAC5E,MACEmD,EAAcD,EAAiB,6BAA6BD,CAAW,IAAI,OAGzEpE,GACFsE,EAAclF,EAAO,KAAK/H,GAAWA,EAAQ,SAAW+M,CAAW,EACnEI,EAAYpF,EAAO,KAAK/H,GAAWA,EAAQ,SAAW+M,EAAc,CAAC,EACrEG,EAAYnF,EAAO,KAAK/H,GAAWA,EAAQ,SAAW+M,EAAc,CAAC,GAErEE,EAAclF,EAAOgF,CAAW,EAGhCE,IACGtE,IAEHwE,EAAYhM,GAAe8L,EAAa,IAAI1F,EAAO,UAAU,gBAAgB,EAAE,CAAC,EAC5EA,EAAO,MAAQ,CAAC4F,IAClBA,EAAYpF,EAAO,CAAC,GAItBmF,EAAYlM,GAAeiM,EAAa,IAAI1F,EAAO,UAAU,gBAAgB,EAAE,CAAC,EAC5EA,EAAO,MAAQ,CAAC2F,IAAc,IAChCA,EAAYnF,EAAOA,EAAO,OAAS,CAAC,KAI1CA,EAAO,QAAQ/H,GAAW,CACxB6M,GAAmB7M,EAASA,IAAYiN,EAAa1F,EAAO,gBAAgB,EAC5EsF,GAAmB7M,EAASA,IAAYmN,EAAW5F,EAAO,cAAc,EACxEsF,GAAmB7M,EAASA,IAAYkN,EAAW3F,EAAO,cAAc,CAC1E,CAAC,EACDtI,EAAO,kBAAiB,CAC1B,CAEA,MAAMmO,GAAuB,CAACnO,EAAQoO,IAAY,CAChD,GAAI,CAACpO,GAAUA,EAAO,WAAa,CAACA,EAAO,OAAQ,OACnD,MAAMqO,EAAgB,IAAMrO,EAAO,UAAY,eAAiB,IAAIA,EAAO,OAAO,UAAU,GACtFe,EAAUqN,EAAQ,QAAQC,EAAa,CAAE,EAC/C,GAAItN,EAAS,CACX,IAAIuN,EAASvN,EAAQ,cAAc,IAAIf,EAAO,OAAO,kBAAkB,EAAE,EACrE,CAACsO,GAAUtO,EAAO,YAChBe,EAAQ,WACVuN,EAASvN,EAAQ,WAAW,cAAc,IAAIf,EAAO,OAAO,kBAAkB,EAAE,EAGhF,sBAAsB,IAAM,CACtBe,EAAQ,aACVuN,EAASvN,EAAQ,WAAW,cAAc,IAAIf,EAAO,OAAO,kBAAkB,EAAE,EAC5EsO,GAAQA,EAAO,OAAM,EAE7B,CAAC,GAGDA,GAAQA,EAAO,OAAM,CAC3B,CACF,EACMC,GAAS,CAACvO,EAAQ4H,IAAU,CAChC,GAAI,CAAC5H,EAAO,OAAO4H,CAAK,EAAG,OAC3B,MAAMwG,EAAUpO,EAAO,OAAO4H,CAAK,EAAE,cAAc,kBAAkB,EACjEwG,GAASA,EAAQ,gBAAgB,SAAS,CAChD,EACMI,GAAUxO,GAAU,CACxB,GAAI,CAACA,GAAUA,EAAO,WAAa,CAACA,EAAO,OAAQ,OACnD,IAAIyO,EAASzO,EAAO,OAAO,oBAC3B,MAAMR,EAAMQ,EAAO,OAAO,OAC1B,GAAI,CAACR,GAAO,CAACiP,GAAUA,EAAS,EAAG,OACnCA,EAAS,KAAK,IAAIA,EAAQjP,CAAG,EAC7B,MAAMkP,EAAgB1O,EAAO,OAAO,gBAAkB,OAASA,EAAO,qBAAoB,EAAK,KAAK,KAAKA,EAAO,OAAO,aAAa,EAC9H8N,EAAc9N,EAAO,YAC3B,GAAIA,EAAO,OAAO,MAAQA,EAAO,OAAO,KAAK,KAAO,EAAG,CACrD,MAAM2O,EAAeb,EACfc,EAAiB,CAACD,EAAeF,CAAM,EAC7CG,EAAe,KAAK,GAAG,MAAM,KAAK,CAChC,OAAQH,CACd,CAAK,EAAE,IAAI,CAAC7D,EAAGxL,IACFuP,EAAeD,EAAgBtP,CACvC,CAAC,EACFY,EAAO,OAAO,QAAQ,CAACe,EAAS3B,IAAM,CAChCwP,EAAe,SAAS7N,EAAQ,MAAM,GAAGwN,GAAOvO,EAAQZ,CAAC,CAC/D,CAAC,EACD,MACF,CACA,MAAMyP,EAAuBf,EAAcY,EAAgB,EAC3D,GAAI1O,EAAO,OAAO,QAAUA,EAAO,OAAO,KACxC,QAASZ,EAAI0O,EAAcW,EAAQrP,GAAKyP,EAAuBJ,EAAQrP,GAAK,EAAG,CAC7E,MAAM0P,GAAa1P,EAAII,EAAMA,GAAOA,GAChCsP,EAAYhB,GAAegB,EAAYD,IAAsBN,GAAOvO,EAAQ8O,CAAS,CAC3F,KAEA,SAAS1P,EAAI,KAAK,IAAI0O,EAAcW,EAAQ,CAAC,EAAGrP,GAAK,KAAK,IAAIyP,EAAuBJ,EAAQjP,EAAM,CAAC,EAAGJ,GAAK,EACtGA,IAAM0O,IAAgB1O,EAAIyP,GAAwBzP,EAAI0O,IACxDS,GAAOvO,EAAQZ,CAAC,CAIxB,EAEA,SAAS2P,GAA0B/O,EAAQ,CACzC,KAAM,CACJ,WAAAiJ,EACA,OAAAX,CACJ,EAAMtI,EACEmM,EAAYnM,EAAO,aAAeA,EAAO,UAAY,CAACA,EAAO,UACnE,IAAI8N,EACJ,QAAS1O,EAAI,EAAGA,EAAI6J,EAAW,OAAQ7J,GAAK,EACtC,OAAO6J,EAAW7J,EAAI,CAAC,EAAM,IAC3B+M,GAAalD,EAAW7J,CAAC,GAAK+M,EAAYlD,EAAW7J,EAAI,CAAC,GAAK6J,EAAW7J,EAAI,CAAC,EAAI6J,EAAW7J,CAAC,GAAK,EACtG0O,EAAc1O,EACL+M,GAAalD,EAAW7J,CAAC,GAAK+M,EAAYlD,EAAW7J,EAAI,CAAC,IACnE0O,EAAc1O,EAAI,GAEX+M,GAAalD,EAAW7J,CAAC,IAClC0O,EAAc1O,GAIlB,OAAIkJ,EAAO,sBACLwF,EAAc,GAAK,OAAOA,EAAgB,OAAaA,EAAc,GAEpEA,CACT,CACA,SAASkB,GAAkBC,EAAgB,CACzC,MAAMjP,EAAS,KACTmM,EAAYnM,EAAO,aAAeA,EAAO,UAAY,CAACA,EAAO,UAC7D,CACJ,SAAAgJ,EACA,OAAAV,EACA,YAAa4G,EACb,UAAWC,EACX,UAAWC,CACf,EAAMpP,EACJ,IAAI8N,EAAcmB,EACd7D,EACJ,MAAMiE,EAAsBC,GAAU,CACpC,IAAIR,EAAYQ,EAAStP,EAAO,QAAQ,aACxC,OAAI8O,EAAY,IACdA,EAAY9O,EAAO,QAAQ,OAAO,OAAS8O,GAEzCA,GAAa9O,EAAO,QAAQ,OAAO,SACrC8O,GAAa9O,EAAO,QAAQ,OAAO,QAE9B8O,CACT,EAIA,GAHI,OAAOhB,EAAgB,MACzBA,EAAciB,GAA0B/O,CAAM,GAE5CgJ,EAAS,QAAQmD,CAAS,GAAK,EACjCf,EAAYpC,EAAS,QAAQmD,CAAS,MACjC,CACL,MAAMoD,EAAO,KAAK,IAAIjH,EAAO,mBAAoBwF,CAAW,EAC5D1C,EAAYmE,EAAO,KAAK,OAAOzB,EAAcyB,GAAQjH,EAAO,cAAc,CAC5E,CAEA,GADI8C,GAAapC,EAAS,SAAQoC,EAAYpC,EAAS,OAAS,GAC5D8E,IAAgBoB,GAAiB,CAAClP,EAAO,OAAO,KAAM,CACpDoL,IAAcgE,IAChBpP,EAAO,UAAYoL,EACnBpL,EAAO,KAAK,iBAAiB,GAE/B,MACF,CACA,GAAI8N,IAAgBoB,GAAiBlP,EAAO,OAAO,MAAQA,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAS,CAC1GA,EAAO,UAAYqP,EAAoBvB,CAAW,EAClD,MACF,CACA,MAAMpE,EAAc1J,EAAO,MAAQsI,EAAO,MAAQA,EAAO,KAAK,KAAO,EAGrE,IAAIwG,EACJ,GAAI9O,EAAO,SAAWsI,EAAO,QAAQ,SAAWA,EAAO,KACrDwG,EAAYO,EAAoBvB,CAAW,UAClCpE,EAAa,CACtB,MAAM8F,EAAqBxP,EAAO,OAAO,KAAKe,GAAWA,EAAQ,SAAW+M,CAAW,EACvF,IAAI2B,EAAmB,SAASD,EAAmB,aAAa,yBAAyB,EAAG,EAAE,EAC1F,OAAO,MAAMC,CAAgB,IAC/BA,EAAmB,KAAK,IAAIzP,EAAO,OAAO,QAAQwP,CAAkB,EAAG,CAAC,GAE1EV,EAAY,KAAK,MAAMW,EAAmBnH,EAAO,KAAK,IAAI,CAC5D,SAAWtI,EAAO,OAAO8N,CAAW,EAAG,CACrC,MAAMjD,EAAa7K,EAAO,OAAO8N,CAAW,EAAE,aAAa,yBAAyB,EAChFjD,EACFiE,EAAY,SAASjE,EAAY,EAAE,EAEnCiE,EAAYhB,CAEhB,MACEgB,EAAYhB,EAEd,OAAO,OAAO9N,EAAQ,CACpB,kBAAAoP,EACA,UAAAhE,EACA,kBAAA+D,EACA,UAAAL,EACA,cAAAI,EACA,YAAApB,CACJ,CAAG,EACG9N,EAAO,aACTwO,GAAQxO,CAAM,EAEhBA,EAAO,KAAK,mBAAmB,EAC/BA,EAAO,KAAK,iBAAiB,GACzBA,EAAO,aAAeA,EAAO,OAAO,sBAClCmP,IAAsBL,GACxB9O,EAAO,KAAK,iBAAiB,EAE/BA,EAAO,KAAK,aAAa,EAE7B,CAEA,SAAS0P,GAAmBnR,EAAIoR,EAAM,CACpC,MAAM3P,EAAS,KACTsI,EAAStI,EAAO,OACtB,IAAI6J,EAAQtL,EAAG,QAAQ,IAAI+J,EAAO,UAAU,gBAAgB,EACxD,CAACuB,GAAS7J,EAAO,WAAa2P,GAAQA,EAAK,OAAS,GAAKA,EAAK,SAASpR,CAAE,GAC3E,CAAC,GAAGoR,EAAK,MAAMA,EAAK,QAAQpR,CAAE,EAAI,EAAGoR,EAAK,MAAM,CAAC,EAAE,QAAQC,GAAU,CAC/D,CAAC/F,GAAS+F,EAAO,SAAWA,EAAO,QAAQ,IAAItH,EAAO,UAAU,gBAAgB,IAClFuB,EAAQ+F,EAEZ,CAAC,EAEH,IAAIC,EAAa,GACbhF,EACJ,GAAIhB,GACF,QAASzK,EAAI,EAAGA,EAAIY,EAAO,OAAO,OAAQZ,GAAK,EAC7C,GAAIY,EAAO,OAAOZ,CAAC,IAAMyK,EAAO,CAC9BgG,EAAa,GACbhF,EAAazL,EACb,KACF,EAGJ,GAAIyK,GAASgG,EACX7P,EAAO,aAAe6J,EAClB7J,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAC1CA,EAAO,aAAe,SAAS6J,EAAM,aAAa,yBAAyB,EAAG,EAAE,EAEhF7J,EAAO,aAAe6K,MAEnB,CACL7K,EAAO,aAAe,OACtBA,EAAO,aAAe,OACtB,MACF,CACIsI,EAAO,qBAAuBtI,EAAO,eAAiB,QAAaA,EAAO,eAAiBA,EAAO,aACpGA,EAAO,oBAAmB,CAE9B,CAEA,IAAI8P,GAAS,CACX,WAAA5H,GACA,aAAAC,GACA,iBAAAsD,GACA,mBAAAI,GACA,qBAAAK,GACA,eAAAU,GACA,oBAAAiB,GACA,kBAAAmB,GACA,mBAAAU,EACF,EAEA,SAASK,GAAmBpR,EAAM,CAC5BA,IAAS,SACXA,EAAO,KAAK,aAAY,EAAK,IAAM,KAErC,MAAMqB,EAAS,KACT,CACJ,OAAAsI,EACA,aAAcI,EACd,UAAAyD,EACA,UAAA5D,CACJ,EAAMvI,EACJ,GAAIsI,EAAO,iBACT,OAAOI,EAAM,CAACyD,EAAYA,EAE5B,GAAI7D,EAAO,QACT,OAAO6D,EAET,IAAI6D,EAAmBtR,GAAa6J,EAAW5J,CAAI,EACnD,OAAAqR,GAAoBhQ,EAAO,sBAAqB,EAC5C0I,IAAKsH,EAAmB,CAACA,GACtBA,GAAoB,CAC7B,CAEA,SAASC,GAAa9D,EAAW+D,EAAc,CAC7C,MAAMlQ,EAAS,KACT,CACJ,aAAc0I,EACd,OAAAJ,EACA,UAAAC,EACA,SAAA5H,CACJ,EAAMX,EACJ,IAAImQ,EAAI,EACJC,EAAI,EACR,MAAMC,EAAI,EACNrQ,EAAO,eACTmQ,EAAIzH,EAAM,CAACyD,EAAYA,EAEvBiE,EAAIjE,EAEF7D,EAAO,eACT6H,EAAI,KAAK,MAAMA,CAAC,EAChBC,EAAI,KAAK,MAAMA,CAAC,GAElBpQ,EAAO,kBAAoBA,EAAO,UAClCA,EAAO,UAAYA,EAAO,aAAY,EAAKmQ,EAAIC,EAC3C9H,EAAO,QACTC,EAAUvI,EAAO,aAAY,EAAK,aAAe,WAAW,EAAIA,EAAO,aAAY,EAAK,CAACmQ,EAAI,CAACC,EACpF9H,EAAO,mBACbtI,EAAO,eACTmQ,GAAKnQ,EAAO,sBAAqB,EAEjCoQ,GAAKpQ,EAAO,sBAAqB,EAEnCuI,EAAU,MAAM,UAAY,eAAe4H,CAAC,OAAOC,CAAC,OAAOC,CAAC,OAI9D,IAAIC,EACJ,MAAMxD,EAAiB9M,EAAO,aAAY,EAAKA,EAAO,aAAY,EAC9D8M,IAAmB,EACrBwD,EAAc,EAEdA,GAAenE,EAAYnM,EAAO,aAAY,GAAM8M,EAElDwD,IAAgB3P,GAClBX,EAAO,eAAemM,CAAS,EAEjCnM,EAAO,KAAK,eAAgBA,EAAO,UAAWkQ,CAAY,CAC5D,CAEA,SAASK,IAAe,CACtB,MAAO,CAAC,KAAK,SAAS,CAAC,CACzB,CAEA,SAASC,IAAe,CACtB,MAAO,CAAC,KAAK,SAAS,KAAK,SAAS,OAAS,CAAC,CAChD,CAEA,SAASC,GAAYtE,EAAWT,EAAOgF,EAAcC,EAAiBC,EAAU,CAC1EzE,IAAc,SAChBA,EAAY,GAEVT,IAAU,SACZA,EAAQ,KAAK,OAAO,OAElBgF,IAAiB,SACnBA,EAAe,IAEbC,IAAoB,SACtBA,EAAkB,IAEpB,MAAM3Q,EAAS,KACT,CACJ,OAAAsI,EACA,UAAAC,CACJ,EAAMvI,EACJ,GAAIA,EAAO,WAAasI,EAAO,+BAC7B,MAAO,GAET,MAAMiI,EAAevQ,EAAO,aAAY,EAClCwQ,EAAexQ,EAAO,aAAY,EACxC,IAAI6Q,EAKJ,GAJIF,GAAmBxE,EAAYoE,EAAcM,EAAeN,EAAsBI,GAAmBxE,EAAYqE,EAAcK,EAAeL,EAAkBK,EAAe1E,EAGnLnM,EAAO,eAAe6Q,CAAY,EAC9BvI,EAAO,QAAS,CAClB,MAAMwI,EAAM9Q,EAAO,aAAY,EAC/B,GAAI0L,IAAU,EACZnD,EAAUuI,EAAM,aAAe,WAAW,EAAI,CAACD,MAC1C,CACL,GAAI,CAAC7Q,EAAO,QAAQ,aAClB,OAAAF,GAAqB,CACnB,OAAAE,EACA,eAAgB,CAAC6Q,EACjB,KAAMC,EAAM,OAAS,KAC/B,CAAS,EACM,GAETvI,EAAU,SAAS,CACjB,CAACuI,EAAM,OAAS,KAAK,EAAG,CAACD,EACzB,SAAU,QAClB,CAAO,CACH,CACA,MAAO,EACT,CACA,OAAInF,IAAU,GACZ1L,EAAO,cAAc,CAAC,EACtBA,EAAO,aAAa6Q,CAAY,EAC5BH,IACF1Q,EAAO,KAAK,wBAAyB0L,EAAOkF,CAAQ,EACpD5Q,EAAO,KAAK,eAAe,KAG7BA,EAAO,cAAc0L,CAAK,EAC1B1L,EAAO,aAAa6Q,CAAY,EAC5BH,IACF1Q,EAAO,KAAK,wBAAyB0L,EAAOkF,CAAQ,EACpD5Q,EAAO,KAAK,iBAAiB,GAE1BA,EAAO,YACVA,EAAO,UAAY,GACdA,EAAO,oCACVA,EAAO,kCAAoC,SAAuB+Q,EAAG,CAC/D,CAAC/Q,GAAUA,EAAO,WAClB+Q,EAAE,SAAW,OACjB/Q,EAAO,UAAU,oBAAoB,gBAAiBA,EAAO,iCAAiC,EAC9FA,EAAO,kCAAoC,KAC3C,OAAOA,EAAO,kCACdA,EAAO,UAAY,GACf0Q,GACF1Q,EAAO,KAAK,eAAe,EAE/B,GAEFA,EAAO,UAAU,iBAAiB,gBAAiBA,EAAO,iCAAiC,IAGxF,EACT,CAEA,IAAImM,GAAY,CACd,aAAc4D,GACd,aAAAE,GACA,aAAAM,GACA,aAAAC,GACA,YAAAC,EACF,EAEA,SAASO,GAAc1Q,EAAU4P,EAAc,CAC7C,MAAMlQ,EAAS,KACVA,EAAO,OAAO,UACjBA,EAAO,UAAU,MAAM,mBAAqB,GAAGM,CAAQ,KACvDN,EAAO,UAAU,MAAM,gBAAkBM,IAAa,EAAI,MAAQ,IAEpEN,EAAO,KAAK,gBAAiBM,EAAU4P,CAAY,CACrD,CAEA,SAASe,GAAelR,EAAM,CAC5B,GAAI,CACF,OAAAC,EACA,aAAA0Q,EACA,UAAAQ,EACA,KAAAC,CACJ,EAAMpR,EACJ,KAAM,CACJ,YAAA+N,EACA,cAAAoB,CACJ,EAAMlP,EACJ,IAAIO,EAAM2Q,EACL3Q,IACCuN,EAAcoB,EAAe3O,EAAM,OAAgBuN,EAAcoB,EAAe3O,EAAM,OAAYA,EAAM,SAE9GP,EAAO,KAAK,aAAamR,CAAI,EAAE,EAC3BT,GAAgBnQ,IAAQ,QAC1BP,EAAO,KAAK,uBAAuBmR,CAAI,EAAE,EAChCT,GAAgB5C,IAAgBoB,IACzClP,EAAO,KAAK,wBAAwBmR,CAAI,EAAE,EACtC5Q,IAAQ,OACVP,EAAO,KAAK,sBAAsBmR,CAAI,EAAE,EAExCnR,EAAO,KAAK,sBAAsBmR,CAAI,EAAE,EAG9C,CAEA,SAASC,GAAgBV,EAAcQ,EAAW,CAC5CR,IAAiB,SACnBA,EAAe,IAEjB,MAAM1Q,EAAS,KACT,CACJ,OAAAsI,CACJ,EAAMtI,EACAsI,EAAO,UACPA,EAAO,YACTtI,EAAO,iBAAgB,EAEzBiR,GAAe,CACb,OAAAjR,EACA,aAAA0Q,EACA,UAAAQ,EACA,KAAM,OACV,CAAG,EACH,CAEA,SAASG,GAAcX,EAAcQ,EAAW,CAC1CR,IAAiB,SACnBA,EAAe,IAEjB,MAAM1Q,EAAS,KACT,CACJ,OAAAsI,CACJ,EAAMtI,EACJA,EAAO,UAAY,GACf,CAAAsI,EAAO,UACXtI,EAAO,cAAc,CAAC,EACtBiR,GAAe,CACb,OAAAjR,EACA,aAAA0Q,EACA,UAAAQ,EACA,KAAM,KACV,CAAG,EACH,CAEA,IAAII,GAAa,CACf,cAAAN,GACA,gBAAAI,GACA,cAAAC,EACF,EAEA,SAASE,GAAQ3J,EAAO8D,EAAOgF,EAAcE,EAAUY,EAAS,CAC1D5J,IAAU,SACZA,EAAQ,GAEN8I,IAAiB,SACnBA,EAAe,IAEb,OAAO9I,GAAU,WACnBA,EAAQ,SAASA,EAAO,EAAE,GAE5B,MAAM5H,EAAS,KACf,IAAI6K,EAAajD,EACbiD,EAAa,IAAGA,EAAa,GACjC,KAAM,CACJ,OAAAvC,EACA,SAAAU,EACA,WAAAC,EACA,cAAAiG,EACA,YAAApB,EACA,aAAcpF,EACd,UAAAH,EACA,QAAAkJ,CACJ,EAAMzR,EACJ,GAAI,CAACyR,GAAW,CAACb,GAAY,CAACY,GAAWxR,EAAO,WAAaA,EAAO,WAAasI,EAAO,+BACtF,MAAO,GAEL,OAAOoD,EAAU,MACnBA,EAAQ1L,EAAO,OAAO,OAExB,MAAMuP,EAAO,KAAK,IAAIvP,EAAO,OAAO,mBAAoB6K,CAAU,EAClE,IAAIO,EAAYmE,EAAO,KAAK,OAAO1E,EAAa0E,GAAQvP,EAAO,OAAO,cAAc,EAChFoL,GAAapC,EAAS,SAAQoC,EAAYpC,EAAS,OAAS,GAChE,MAAMmD,EAAY,CAACnD,EAASoC,CAAS,EAErC,GAAI9C,EAAO,oBACT,QAASlJ,EAAI,EAAGA,EAAI6J,EAAW,OAAQ7J,GAAK,EAAG,CAC7C,MAAMsS,EAAsB,CAAC,KAAK,MAAMvF,EAAY,GAAG,EACjDwF,EAAiB,KAAK,MAAM1I,EAAW7J,CAAC,EAAI,GAAG,EAC/CwS,EAAqB,KAAK,MAAM3I,EAAW7J,EAAI,CAAC,EAAI,GAAG,EACzD,OAAO6J,EAAW7J,EAAI,CAAC,EAAM,IAC3BsS,GAAuBC,GAAkBD,EAAsBE,GAAsBA,EAAqBD,GAAkB,EAC9H9G,EAAazL,EACJsS,GAAuBC,GAAkBD,EAAsBE,IACxE/G,EAAazL,EAAI,GAEVsS,GAAuBC,IAChC9G,EAAazL,EAEjB,CAGF,GAAIY,EAAO,aAAe6K,IAAeiD,IACnC,CAAC9N,EAAO,iBAAmB0I,EAAMyD,EAAYnM,EAAO,WAAamM,EAAYnM,EAAO,aAAY,EAAKmM,EAAYnM,EAAO,WAAamM,EAAYnM,EAAO,aAAY,IAGpK,CAACA,EAAO,gBAAkBmM,EAAYnM,EAAO,WAAamM,EAAYnM,EAAO,iBAC1E8N,GAAe,KAAOjD,GACzB,MAAO,GAITA,KAAgBqE,GAAiB,IAAMwB,GACzC1Q,EAAO,KAAK,wBAAwB,EAItCA,EAAO,eAAemM,CAAS,EAC/B,IAAI+E,EACArG,EAAaiD,EAAaoD,EAAY,OAAgBrG,EAAaiD,EAAaoD,EAAY,OAAYA,EAAY,QAGxH,MAAMtI,EAAY5I,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAG1D,GAAI,EAFqB4I,GAAa4I,KAEZ9I,GAAO,CAACyD,IAAcnM,EAAO,WAAa,CAAC0I,GAAOyD,IAAcnM,EAAO,WAC/F,OAAAA,EAAO,kBAAkB6K,CAAU,EAE/BvC,EAAO,YACTtI,EAAO,iBAAgB,EAEzBA,EAAO,oBAAmB,EACtBsI,EAAO,SAAW,SACpBtI,EAAO,aAAamM,CAAS,EAE3B+E,IAAc,UAChBlR,EAAO,gBAAgB0Q,EAAcQ,CAAS,EAC9ClR,EAAO,cAAc0Q,EAAcQ,CAAS,GAEvC,GAET,GAAI5I,EAAO,QAAS,CAClB,MAAMwI,EAAM9Q,EAAO,aAAY,EACzB6R,EAAInJ,EAAMyD,EAAY,CAACA,EAC7B,GAAIT,IAAU,EACR9C,IACF5I,EAAO,UAAU,MAAM,eAAiB,OACxCA,EAAO,kBAAoB,IAEzB4I,GAAa,CAAC5I,EAAO,2BAA6BA,EAAO,OAAO,aAAe,GACjFA,EAAO,0BAA4B,GACnC,sBAAsB,IAAM,CAC1BuI,EAAUuI,EAAM,aAAe,WAAW,EAAIe,CAChD,CAAC,GAEDtJ,EAAUuI,EAAM,aAAe,WAAW,EAAIe,EAE5CjJ,GACF,sBAAsB,IAAM,CAC1B5I,EAAO,UAAU,MAAM,eAAiB,GACxCA,EAAO,kBAAoB,EAC7B,CAAC,MAEE,CACL,GAAI,CAACA,EAAO,QAAQ,aAClB,OAAAF,GAAqB,CACnB,OAAAE,EACA,eAAgB6R,EAChB,KAAMf,EAAM,OAAS,KAC/B,CAAS,EACM,GAETvI,EAAU,SAAS,CACjB,CAACuI,EAAM,OAAS,KAAK,EAAGe,EACxB,SAAU,QAClB,CAAO,CACH,CACA,MAAO,EACT,CAEA,MAAMhN,EADUO,GAAU,EACD,SACzB,OAAIwD,GAAa,CAAC4I,GAAW3M,GAAY7E,EAAO,WAC9CA,EAAO,QAAQ,OAAO,GAAO,GAAO6K,CAAU,EAEhD7K,EAAO,cAAc0L,CAAK,EAC1B1L,EAAO,aAAamM,CAAS,EAC7BnM,EAAO,kBAAkB6K,CAAU,EACnC7K,EAAO,oBAAmB,EAC1BA,EAAO,KAAK,wBAAyB0L,EAAOkF,CAAQ,EACpD5Q,EAAO,gBAAgB0Q,EAAcQ,CAAS,EAC1CxF,IAAU,EACZ1L,EAAO,cAAc0Q,EAAcQ,CAAS,EAClClR,EAAO,YACjBA,EAAO,UAAY,GACdA,EAAO,gCACVA,EAAO,8BAAgC,SAAuB+Q,EAAG,CAC3D,CAAC/Q,GAAUA,EAAO,WAClB+Q,EAAE,SAAW,OACjB/Q,EAAO,UAAU,oBAAoB,gBAAiBA,EAAO,6BAA6B,EAC1FA,EAAO,8BAAgC,KACvC,OAAOA,EAAO,8BACdA,EAAO,cAAc0Q,EAAcQ,CAAS,EAC9C,GAEFlR,EAAO,UAAU,iBAAiB,gBAAiBA,EAAO,6BAA6B,GAElF,EACT,CAEA,SAAS8R,GAAYlK,EAAO8D,EAAOgF,EAAcE,EAAU,CACrDhJ,IAAU,SACZA,EAAQ,GAEN8I,IAAiB,SACnBA,EAAe,IAEb,OAAO9I,GAAU,WAEnBA,EADsB,SAASA,EAAO,EAAE,GAG1C,MAAM5H,EAAS,KACf,GAAIA,EAAO,UAAW,OAClB,OAAO0L,EAAU,MACnBA,EAAQ1L,EAAO,OAAO,OAExB,MAAM0J,EAAc1J,EAAO,MAAQA,EAAO,OAAO,MAAQA,EAAO,OAAO,KAAK,KAAO,EACnF,IAAI+R,EAAWnK,EACf,GAAI5H,EAAO,OAAO,KAChB,GAAIA,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAE1C+R,EAAWA,EAAW/R,EAAO,QAAQ,iBAChC,CACL,IAAIgS,EACJ,GAAItI,EAAa,CACf,MAAMmB,EAAakH,EAAW/R,EAAO,OAAO,KAAK,KACjDgS,EAAmBhS,EAAO,OAAO,KAAKe,GAAWA,EAAQ,aAAa,yBAAyB,EAAI,IAAM8J,CAAU,EAAE,MACvH,MACEmH,EAAmBhS,EAAO,oBAAoB+R,CAAQ,EAExD,MAAME,EAAOvI,EAAc,KAAK,KAAK1J,EAAO,OAAO,OAASA,EAAO,OAAO,KAAK,IAAI,EAAIA,EAAO,OAAO,OAC/F,CACJ,eAAAkS,CACR,EAAUlS,EAAO,OACX,IAAI0O,EAAgB1O,EAAO,OAAO,cAC9B0O,IAAkB,OACpBA,EAAgB1O,EAAO,qBAAoB,GAE3C0O,EAAgB,KAAK,KAAK,WAAW1O,EAAO,OAAO,cAAe,EAAE,CAAC,EACjEkS,GAAkBxD,EAAgB,IAAM,IAC1CA,EAAgBA,EAAgB,IAGpC,IAAIyD,EAAcF,EAAOD,EAAmBtD,EAO5C,GANIwD,IACFC,EAAcA,GAAeH,EAAmB,KAAK,KAAKtD,EAAgB,CAAC,GAEzEkC,GAAYsB,GAAkBlS,EAAO,OAAO,gBAAkB,QAAU,CAAC0J,IAC3EyI,EAAc,IAEZA,EAAa,CACf,MAAMjB,EAAYgB,EAAiBF,EAAmBhS,EAAO,YAAc,OAAS,OAASgS,EAAmBhS,EAAO,YAAc,EAAIA,EAAO,OAAO,cAAgB,OAAS,OAChLA,EAAO,QAAQ,CACb,UAAAkR,EACA,QAAS,GACT,iBAAkBA,IAAc,OAASc,EAAmB,EAAIA,EAAmBC,EAAO,EAC1F,eAAgBf,IAAc,OAASlR,EAAO,UAAY,MACpE,CAAS,CACH,CACA,GAAI0J,EAAa,CACf,MAAMmB,EAAakH,EAAW/R,EAAO,OAAO,KAAK,KACjD+R,EAAW/R,EAAO,OAAO,KAAKe,GAAWA,EAAQ,aAAa,yBAAyB,EAAI,IAAM8J,CAAU,EAAE,MAC/G,MACEkH,EAAW/R,EAAO,oBAAoB+R,CAAQ,CAElD,CAEF,6BAAsB,IAAM,CAC1B/R,EAAO,QAAQ+R,EAAUrG,EAAOgF,EAAcE,CAAQ,CACxD,CAAC,EACM5Q,CACT,CAGA,SAASoS,GAAU1G,EAAOgF,EAAcE,EAAU,CAC5CF,IAAiB,SACnBA,EAAe,IAEjB,MAAM1Q,EAAS,KACT,CACJ,QAAAyR,EACA,OAAAnJ,EACA,UAAA+J,CACJ,EAAMrS,EACJ,GAAI,CAACyR,GAAWzR,EAAO,UAAW,OAAOA,EACrC,OAAO0L,EAAU,MACnBA,EAAQ1L,EAAO,OAAO,OAExB,IAAIsS,EAAWhK,EAAO,eAClBA,EAAO,gBAAkB,QAAUA,EAAO,iBAAmB,GAAKA,EAAO,qBAC3EgK,EAAW,KAAK,IAAItS,EAAO,qBAAqB,UAAW,EAAI,EAAG,CAAC,GAErE,MAAMuS,EAAYvS,EAAO,YAAcsI,EAAO,mBAAqB,EAAIgK,EACjE1J,EAAY5I,EAAO,SAAWsI,EAAO,QAAQ,QACnD,GAAIA,EAAO,KAAM,CACf,GAAI+J,GAAa,CAACzJ,GAAaN,EAAO,oBAAqB,MAAO,GAMlE,GALAtI,EAAO,QAAQ,CACb,UAAW,MACjB,CAAK,EAEDA,EAAO,YAAcA,EAAO,UAAU,WAClCA,EAAO,cAAgBA,EAAO,OAAO,OAAS,GAAKsI,EAAO,QAC5D,6BAAsB,IAAM,CAC1BtI,EAAO,QAAQA,EAAO,YAAcuS,EAAW7G,EAAOgF,EAAcE,CAAQ,CAC9E,CAAC,EACM,EAEX,CACA,OAAItI,EAAO,QAAUtI,EAAO,MACnBA,EAAO,QAAQ,EAAG0L,EAAOgF,EAAcE,CAAQ,EAEjD5Q,EAAO,QAAQA,EAAO,YAAcuS,EAAW7G,EAAOgF,EAAcE,CAAQ,CACrF,CAGA,SAAS4B,GAAU9G,EAAOgF,EAAcE,EAAU,CAC5CF,IAAiB,SACnBA,EAAe,IAEjB,MAAM1Q,EAAS,KACT,CACJ,OAAAsI,EACA,SAAAU,EACA,WAAAC,EACA,aAAAwJ,EACA,QAAAhB,EACA,UAAAY,CACJ,EAAMrS,EACJ,GAAI,CAACyR,GAAWzR,EAAO,UAAW,OAAOA,EACrC,OAAO0L,EAAU,MACnBA,EAAQ1L,EAAO,OAAO,OAExB,MAAM4I,EAAY5I,EAAO,SAAWsI,EAAO,QAAQ,QACnD,GAAIA,EAAO,KAAM,CACf,GAAI+J,GAAa,CAACzJ,GAAaN,EAAO,oBAAqB,MAAO,GAClEtI,EAAO,QAAQ,CACb,UAAW,MACjB,CAAK,EAEDA,EAAO,YAAcA,EAAO,UAAU,UACxC,CACA,MAAMmM,EAAYsG,EAAezS,EAAO,UAAY,CAACA,EAAO,UAC5D,SAAS0S,EAAUC,EAAK,CACtB,OAAIA,EAAM,EAAU,CAAC,KAAK,MAAM,KAAK,IAAIA,CAAG,CAAC,EACtC,KAAK,MAAMA,CAAG,CACvB,CACA,MAAMjB,EAAsBgB,EAAUvG,CAAS,EACzCyG,EAAqB5J,EAAS,IAAI2J,GAAOD,EAAUC,CAAG,CAAC,EACvDE,EAAavK,EAAO,UAAYA,EAAO,SAAS,QACtD,IAAIwK,EAAW9J,EAAS4J,EAAmB,QAAQlB,CAAmB,EAAI,CAAC,EAC3E,GAAI,OAAOoB,EAAa,MAAgBxK,EAAO,SAAWuK,GAAa,CACrE,IAAIE,EACJ/J,EAAS,QAAQ,CAACiC,EAAMG,IAAc,CAChCsG,GAAuBzG,IAEzB8H,EAAgB3H,EAEpB,CAAC,EACG,OAAO2H,EAAkB,MAC3BD,EAAWD,EAAa7J,EAAS+J,CAAa,EAAI/J,EAAS+J,EAAgB,EAAIA,EAAgB,EAAIA,CAAa,EAEpH,CACA,IAAIC,EAAY,EAShB,GARI,OAAOF,EAAa,MACtBE,EAAY/J,EAAW,QAAQ6J,CAAQ,EACnCE,EAAY,IAAGA,EAAYhT,EAAO,YAAc,GAChDsI,EAAO,gBAAkB,QAAUA,EAAO,iBAAmB,GAAKA,EAAO,qBAC3E0K,EAAYA,EAAYhT,EAAO,qBAAqB,WAAY,EAAI,EAAI,EACxEgT,EAAY,KAAK,IAAIA,EAAW,CAAC,IAGjC1K,EAAO,QAAUtI,EAAO,YAAa,CACvC,MAAMiT,EAAYjT,EAAO,OAAO,SAAWA,EAAO,OAAO,QAAQ,SAAWA,EAAO,QAAUA,EAAO,QAAQ,OAAO,OAAS,EAAIA,EAAO,OAAO,OAAS,EACvJ,OAAOA,EAAO,QAAQiT,EAAWvH,EAAOgF,EAAcE,CAAQ,CAChE,SAAWtI,EAAO,MAAQtI,EAAO,cAAgB,GAAKsI,EAAO,QAC3D,6BAAsB,IAAM,CAC1BtI,EAAO,QAAQgT,EAAWtH,EAAOgF,EAAcE,CAAQ,CACzD,CAAC,EACM,GAET,OAAO5Q,EAAO,QAAQgT,EAAWtH,EAAOgF,EAAcE,CAAQ,CAChE,CAGA,SAASsC,GAAWxH,EAAOgF,EAAcE,EAAU,CAC7CF,IAAiB,SACnBA,EAAe,IAEjB,MAAM1Q,EAAS,KACf,GAAI,CAAAA,EAAO,UACX,OAAI,OAAO0L,EAAU,MACnBA,EAAQ1L,EAAO,OAAO,OAEjBA,EAAO,QAAQA,EAAO,YAAa0L,EAAOgF,EAAcE,CAAQ,CACzE,CAGA,SAASuC,GAAezH,EAAOgF,EAAcE,EAAUwC,EAAW,CAC5D1C,IAAiB,SACnBA,EAAe,IAEb0C,IAAc,SAChBA,EAAY,IAEd,MAAMpT,EAAS,KACf,GAAIA,EAAO,UAAW,OAClB,OAAO0L,EAAU,MACnBA,EAAQ1L,EAAO,OAAO,OAExB,IAAI4H,EAAQ5H,EAAO,YACnB,MAAMuP,EAAO,KAAK,IAAIvP,EAAO,OAAO,mBAAoB4H,CAAK,EACvDwD,EAAYmE,EAAO,KAAK,OAAO3H,EAAQ2H,GAAQvP,EAAO,OAAO,cAAc,EAC3EmM,EAAYnM,EAAO,aAAeA,EAAO,UAAY,CAACA,EAAO,UACnE,GAAImM,GAAanM,EAAO,SAASoL,CAAS,EAAG,CAG3C,MAAMiI,EAAcrT,EAAO,SAASoL,CAAS,EACvCkI,EAAWtT,EAAO,SAASoL,EAAY,CAAC,EAC1Ce,EAAYkH,GAAeC,EAAWD,GAAeD,IACvDxL,GAAS5H,EAAO,OAAO,eAE3B,KAAO,CAGL,MAAM8S,EAAW9S,EAAO,SAASoL,EAAY,CAAC,EACxCiI,EAAcrT,EAAO,SAASoL,CAAS,EACzCe,EAAY2G,IAAaO,EAAcP,GAAYM,IACrDxL,GAAS5H,EAAO,OAAO,eAE3B,CACA,OAAA4H,EAAQ,KAAK,IAAIA,EAAO,CAAC,EACzBA,EAAQ,KAAK,IAAIA,EAAO5H,EAAO,WAAW,OAAS,CAAC,EAC7CA,EAAO,QAAQ4H,EAAO8D,EAAOgF,EAAcE,CAAQ,CAC5D,CAEA,SAAS2C,IAAsB,CAC7B,MAAMvT,EAAS,KACf,GAAIA,EAAO,UAAW,OACtB,KAAM,CACJ,OAAAsI,EACA,SAAAE,CACJ,EAAMxI,EACE0O,EAAgBpG,EAAO,gBAAkB,OAAStI,EAAO,qBAAoB,EAAKsI,EAAO,cAC/F,IAAIkL,EAAexT,EAAO,sBAAsBA,EAAO,YAAY,EAC/D8O,EACJ,MAAMT,EAAgBrO,EAAO,UAAY,eAAiB,IAAIsI,EAAO,UAAU,GACzEmL,EAASzT,EAAO,MAAQA,EAAO,OAAO,MAAQA,EAAO,OAAO,KAAK,KAAO,EAC9E,GAAIsI,EAAO,KAAM,CACf,GAAItI,EAAO,UAAW,OACtB8O,EAAY,SAAS9O,EAAO,aAAa,aAAa,yBAAyB,EAAG,EAAE,EAChFsI,EAAO,eACTtI,EAAO,YAAY8O,CAAS,EACnB0E,GAAgBC,GAAUzT,EAAO,OAAO,OAAS0O,GAAiB,GAAK1O,EAAO,OAAO,KAAK,KAAO,GAAKA,EAAO,OAAO,OAAS0O,IACtI1O,EAAO,QAAO,EACdwT,EAAexT,EAAO,cAAcgB,EAAgBwH,EAAU,GAAG6F,CAAa,6BAA6BS,CAAS,IAAI,EAAE,CAAC,CAAC,EAC5H3Q,GAAS,IAAM,CACb6B,EAAO,QAAQwT,CAAY,CAC7B,CAAC,GAEDxT,EAAO,QAAQwT,CAAY,CAE/B,MACExT,EAAO,QAAQwT,CAAY,CAE/B,CAEA,IAAI3J,GAAQ,CACV,QAAA0H,GACA,YAAAO,GACA,UAAAM,GACA,UAAAI,GACA,WAAAU,GACA,eAAAC,GACA,oBAAAI,EACF,EAEA,SAASG,GAAWC,EAAgBnC,EAAS,CAC3C,MAAMxR,EAAS,KACT,CACJ,OAAAsI,EACA,SAAAE,CACJ,EAAMxI,EACJ,GAAI,CAACsI,EAAO,MAAQtI,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAS,OACrE,MAAM4T,EAAa,IAAM,CACR5S,EAAgBwH,EAAU,IAAIF,EAAO,UAAU,gBAAgB,EACvE,QAAQ,CAAC/J,EAAIqJ,IAAU,CAC5BrJ,EAAG,aAAa,0BAA2BqJ,CAAK,CAClD,CAAC,CACH,EACMiM,EAAmB,IAAM,CAC7B,MAAM/K,EAAS9H,EAAgBwH,EAAU,IAAIF,EAAO,eAAe,EAAE,EACrEQ,EAAO,QAAQvK,GAAM,CACnBA,EAAG,OAAM,CACX,CAAC,EACGuK,EAAO,OAAS,IAClB9I,EAAO,aAAY,EACnBA,EAAO,aAAY,EAEvB,EACM0J,EAAc1J,EAAO,MAAQsI,EAAO,MAAQA,EAAO,KAAK,KAAO,EACjEA,EAAO,qBAAuBA,EAAO,eAAiB,GAAKoB,IAC7DmK,EAAgB,EAElB,MAAMC,EAAiBxL,EAAO,gBAAkBoB,EAAcpB,EAAO,KAAK,KAAO,GAC3EyL,EAAkB/T,EAAO,OAAO,OAAS8T,IAAmB,EAC5DE,EAAiBtK,GAAe1J,EAAO,OAAO,OAASsI,EAAO,KAAK,OAAS,EAC5E2L,EAAiBC,GAAkB,CACvC,QAAS9U,EAAI,EAAGA,EAAI8U,EAAgB9U,GAAK,EAAG,CAC1C,MAAM2B,EAAUf,EAAO,UAAY6B,GAAc,eAAgB,CAACyG,EAAO,eAAe,CAAC,EAAIzG,GAAc,MAAO,CAACyG,EAAO,WAAYA,EAAO,eAAe,CAAC,EAC7JtI,EAAO,SAAS,OAAOe,CAAO,CAChC,CACF,EACA,GAAIgT,EAAiB,CACnB,GAAIzL,EAAO,mBAAoB,CAC7B,MAAM6L,EAAcL,EAAiB9T,EAAO,OAAO,OAAS8T,EAC5DG,EAAeE,CAAW,EAC1BnU,EAAO,aAAY,EACnBA,EAAO,aAAY,CACrB,MACE2B,GAAY,iLAAiL,EAE/LiS,EAAU,CACZ,SAAWI,EAAgB,CACzB,GAAI1L,EAAO,mBAAoB,CAC7B,MAAM6L,EAAc7L,EAAO,KAAK,KAAOtI,EAAO,OAAO,OAASsI,EAAO,KAAK,KAC1E2L,EAAeE,CAAW,EAC1BnU,EAAO,aAAY,EACnBA,EAAO,aAAY,CACrB,MACE2B,GAAY,4KAA4K,EAE1LiS,EAAU,CACZ,MACEA,EAAU,EAEZ5T,EAAO,QAAQ,CACb,eAAA2T,EACA,UAAWrL,EAAO,eAAiB,OAAY,OAC/C,QAAAkJ,CACJ,CAAG,CACH,CAEA,SAAS4C,GAAQ1Q,EAAO,CACtB,GAAI,CACF,eAAAiQ,EACA,QAAApC,EAAU,GACV,UAAAL,EACA,aAAAjB,EACA,iBAAAR,EACA,QAAA+B,EACA,aAAAtB,EACA,aAAAmE,CACJ,EAAM3Q,IAAU,OAAS,CAAA,EAAKA,EAC5B,MAAM1D,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,KAAM,OACzBA,EAAO,KAAK,eAAe,EAC3B,KAAM,CACJ,OAAA8I,EACA,eAAAwL,EACA,eAAAC,EACA,SAAA/L,EACA,OAAAF,CACJ,EAAMtI,EACE,CACJ,eAAAkS,EACA,aAAAsC,CACJ,EAAMlM,EAGJ,GAFAtI,EAAO,eAAiB,GACxBA,EAAO,eAAiB,GACpBA,EAAO,SAAWsI,EAAO,QAAQ,QAAS,CACxCiJ,IACE,CAACjJ,EAAO,gBAAkBtI,EAAO,YAAc,EACjDA,EAAO,QAAQA,EAAO,QAAQ,OAAO,OAAQ,EAAG,GAAO,EAAI,EAClDsI,EAAO,gBAAkBtI,EAAO,UAAYsI,EAAO,cAC5DtI,EAAO,QAAQA,EAAO,QAAQ,OAAO,OAASA,EAAO,UAAW,EAAG,GAAO,EAAI,EACrEA,EAAO,YAAcA,EAAO,SAAS,OAAS,GACvDA,EAAO,QAAQA,EAAO,QAAQ,aAAc,EAAG,GAAO,EAAI,GAG9DA,EAAO,eAAiBsU,EACxBtU,EAAO,eAAiBuU,EACxBvU,EAAO,KAAK,SAAS,EACrB,MACF,CACA,IAAI0O,EAAgBpG,EAAO,cACvBoG,IAAkB,OACpBA,EAAgB1O,EAAO,qBAAoB,GAE3C0O,EAAgB,KAAK,KAAK,WAAWpG,EAAO,cAAe,EAAE,CAAC,EAC1D4J,GAAkBxD,EAAgB,IAAM,IAC1CA,EAAgBA,EAAgB,IAGpC,MAAMoF,EAAiBxL,EAAO,mBAAqBoG,EAAgBpG,EAAO,eAC1E,IAAImM,EAAevC,EAAiB,KAAK,IAAI4B,EAAgB,KAAK,KAAKpF,EAAgB,CAAC,CAAC,EAAIoF,EACzFW,EAAeX,IAAmB,IACpCW,GAAgBX,EAAiBW,EAAeX,GAElDW,GAAgBnM,EAAO,qBACvBtI,EAAO,aAAeyU,EACtB,MAAM/K,EAAc1J,EAAO,MAAQsI,EAAO,MAAQA,EAAO,KAAK,KAAO,EACjEQ,EAAO,OAAS4F,EAAgB+F,GAAgBzU,EAAO,OAAO,SAAW,SAAW8I,EAAO,OAAS4F,EAAgB+F,EAAe,EACrI9S,GAAY,0OAA0O,EAC7O+H,GAAepB,EAAO,KAAK,OAAS,OAC7C3G,GAAY,yEAAyE,EAEvF,MAAM+S,EAAuB,CAAA,EACvBC,EAAsB,CAAA,EACtB1C,EAAOvI,EAAc,KAAK,KAAKZ,EAAO,OAASR,EAAO,KAAK,IAAI,EAAIQ,EAAO,OAC1E8L,EAAoBpD,GAAWS,EAAOuC,EAAe9F,GAAiB,CAACwD,EAC7E,IAAIpE,EAAc8G,EAAoBJ,EAAexU,EAAO,YACxD,OAAOyP,EAAqB,IAC9BA,EAAmBzP,EAAO,cAAc8I,EAAO,KAAKvK,GAAMA,EAAG,UAAU,SAAS+J,EAAO,gBAAgB,CAAC,CAAC,EAEzGwF,EAAc2B,EAEhB,MAAMoF,EAAS3D,IAAc,QAAU,CAACA,EAClC4D,EAAS5D,IAAc,QAAU,CAACA,EACxC,IAAI6D,EAAkB,EAClBC,EAAiB,EAErB,MAAMC,GADiBvL,EAAcZ,EAAO2G,CAAgB,EAAE,OAASA,IACrByC,GAAkB,OAAOjC,EAAiB,IAAc,CAACvB,EAAgB,EAAI,GAAM,GAErI,GAAIuG,EAA0BR,EAAc,CAC1CM,EAAkB,KAAK,IAAIN,EAAeQ,EAAyBnB,CAAc,EACjF,QAAS1U,EAAI,EAAGA,EAAIqV,EAAeQ,EAAyB7V,GAAK,EAAG,CAClE,MAAMwI,EAAQxI,EAAI,KAAK,MAAMA,EAAI6S,CAAI,EAAIA,EACzC,GAAIvI,EAAa,CACf,MAAMwL,EAAoBjD,EAAOrK,EAAQ,EACzC,QAASxI,EAAI0J,EAAO,OAAS,EAAG1J,GAAK,EAAGA,GAAK,EACvC0J,EAAO1J,CAAC,EAAE,SAAW8V,GAAmBR,EAAqB,KAAKtV,CAAC,CAK3E,MACEsV,EAAqB,KAAKzC,EAAOrK,EAAQ,CAAC,CAE9C,CACF,SAAWqN,EAA0BvG,EAAgBuD,EAAOwC,EAAc,CACxEO,EAAiB,KAAK,IAAIC,GAA2BhD,EAAOwC,EAAe,GAAIX,CAAc,EACzFc,IACFI,EAAiB,KAAK,IAAIA,EAAgBtG,EAAgBuD,EAAOuC,EAAe,CAAC,GAEnF,QAASpV,EAAI,EAAGA,EAAI4V,EAAgB5V,GAAK,EAAG,CAC1C,MAAMwI,EAAQxI,EAAI,KAAK,MAAMA,EAAI6S,CAAI,EAAIA,EACrCvI,EACFZ,EAAO,QAAQ,CAACe,EAAOgB,IAAe,CAChChB,EAAM,SAAWjC,GAAO+M,EAAoB,KAAK9J,CAAU,CACjE,CAAC,EAED8J,EAAoB,KAAK/M,CAAK,CAElC,CACF,CAsCA,GArCA5H,EAAO,oBAAsB,GAC7B,sBAAsB,IAAM,CAC1BA,EAAO,oBAAsB,EAC/B,CAAC,EACGA,EAAO,OAAO,SAAW,SAAW8I,EAAO,OAAS4F,EAAgB+F,EAAe,IACjFE,EAAoB,SAASlF,CAAgB,GAC/CkF,EAAoB,OAAOA,EAAoB,QAAQlF,CAAgB,EAAG,CAAC,EAEzEiF,EAAqB,SAASjF,CAAgB,GAChDiF,EAAqB,OAAOA,EAAqB,QAAQjF,CAAgB,EAAG,CAAC,GAG7EqF,GACFJ,EAAqB,QAAQ9M,GAAS,CACpCkB,EAAOlB,CAAK,EAAE,kBAAoB,GAClCY,EAAS,QAAQM,EAAOlB,CAAK,CAAC,EAC9BkB,EAAOlB,CAAK,EAAE,kBAAoB,EACpC,CAAC,EAECiN,GACFF,EAAoB,QAAQ/M,GAAS,CACnCkB,EAAOlB,CAAK,EAAE,kBAAoB,GAClCY,EAAS,OAAOM,EAAOlB,CAAK,CAAC,EAC7BkB,EAAOlB,CAAK,EAAE,kBAAoB,EACpC,CAAC,EAEH5H,EAAO,aAAY,EACfsI,EAAO,gBAAkB,OAC3BtI,EAAO,aAAY,EACV0J,IAAgBgL,EAAqB,OAAS,GAAKI,GAAUH,EAAoB,OAAS,GAAKE,IACxG7U,EAAO,OAAO,QAAQ,CAAC6J,EAAOgB,IAAe,CAC3C7K,EAAO,KAAK,YAAY6K,EAAYhB,EAAO7J,EAAO,MAAM,CAC1D,CAAC,EAECsI,EAAO,qBACTtI,EAAO,mBAAkB,EAEvBuR,GACF,GAAImD,EAAqB,OAAS,GAAKI,GACrC,GAAI,OAAOnB,EAAmB,IAAa,CACzC,MAAMwB,EAAwBnV,EAAO,WAAW8N,CAAW,EAErDsH,EADoBpV,EAAO,WAAW8N,EAAciH,CAAe,EACxCI,EAC7Bd,EACFrU,EAAO,aAAaA,EAAO,UAAYoV,CAAI,GAE3CpV,EAAO,QAAQ8N,EAAc,KAAK,KAAKiH,CAAe,EAAG,EAAG,GAAO,EAAI,EACnE9E,IACFjQ,EAAO,gBAAgB,eAAiBA,EAAO,gBAAgB,eAAiBoV,EAChFpV,EAAO,gBAAgB,iBAAmBA,EAAO,gBAAgB,iBAAmBoV,GAG1F,SACMnF,EAAc,CAChB,MAAMoF,EAAQ3L,EAAcgL,EAAqB,OAASpM,EAAO,KAAK,KAAOoM,EAAqB,OAClG1U,EAAO,QAAQA,EAAO,YAAcqV,EAAO,EAAG,GAAO,EAAI,EACzDrV,EAAO,gBAAgB,iBAAmBA,EAAO,SACnD,UAEO2U,EAAoB,OAAS,GAAKE,EAC3C,GAAI,OAAOlB,EAAmB,IAAa,CACzC,MAAMwB,EAAwBnV,EAAO,WAAW8N,CAAW,EAErDsH,EADoBpV,EAAO,WAAW8N,EAAckH,CAAc,EACvCG,EAC7Bd,EACFrU,EAAO,aAAaA,EAAO,UAAYoV,CAAI,GAE3CpV,EAAO,QAAQ8N,EAAckH,EAAgB,EAAG,GAAO,EAAI,EACvD/E,IACFjQ,EAAO,gBAAgB,eAAiBA,EAAO,gBAAgB,eAAiBoV,EAChFpV,EAAO,gBAAgB,iBAAmBA,EAAO,gBAAgB,iBAAmBoV,GAG1F,KAAO,CACL,MAAMC,EAAQ3L,EAAciL,EAAoB,OAASrM,EAAO,KAAK,KAAOqM,EAAoB,OAChG3U,EAAO,QAAQA,EAAO,YAAcqV,EAAO,EAAG,GAAO,EAAI,CAC3D,EAKJ,GAFArV,EAAO,eAAiBsU,EACxBtU,EAAO,eAAiBuU,EACpBvU,EAAO,YAAcA,EAAO,WAAW,SAAW,CAACkQ,EAAc,CACnE,MAAMoF,EAAa,CACjB,eAAA3B,EACA,UAAAzC,EACA,aAAAjB,EACA,iBAAAR,EACA,aAAc,EACpB,EACQ,MAAM,QAAQzP,EAAO,WAAW,OAAO,EACzCA,EAAO,WAAW,QAAQ,QAAQhC,GAAK,CACjC,CAACA,EAAE,WAAaA,EAAE,OAAO,MAAMA,EAAE,QAAQ,CAC3C,GAAGsX,EACH,QAAStX,EAAE,OAAO,gBAAkBsK,EAAO,cAAgBiJ,EAAU,EAC/E,CAAS,CACH,CAAC,EACQvR,EAAO,WAAW,mBAAmBA,EAAO,aAAeA,EAAO,WAAW,QAAQ,OAAO,MACrGA,EAAO,WAAW,QAAQ,QAAQ,CAChC,GAAGsV,EACH,QAAStV,EAAO,WAAW,QAAQ,OAAO,gBAAkBsI,EAAO,cAAgBiJ,EAAU,EACrG,CAAO,CAEL,CACAvR,EAAO,KAAK,SAAS,CACvB,CAEA,SAASuV,IAAc,CACrB,MAAMvV,EAAS,KACT,CACJ,OAAAsI,EACA,SAAAE,CACJ,EAAMxI,EACJ,GAAI,CAACsI,EAAO,MAAQ,CAACE,GAAYxI,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAS,OAClFA,EAAO,aAAY,EACnB,MAAMwV,EAAiB,CAAA,EACvBxV,EAAO,OAAO,QAAQe,GAAW,CAC/B,MAAM6G,EAAQ,OAAO7G,EAAQ,iBAAqB,IAAcA,EAAQ,aAAa,yBAAyB,EAAI,EAAIA,EAAQ,iBAC9HyU,EAAe5N,CAAK,EAAI7G,CAC1B,CAAC,EACDf,EAAO,OAAO,QAAQe,GAAW,CAC/BA,EAAQ,gBAAgB,yBAAyB,CACnD,CAAC,EACDyU,EAAe,QAAQzU,GAAW,CAChCyH,EAAS,OAAOzH,CAAO,CACzB,CAAC,EACDf,EAAO,aAAY,EACnBA,EAAO,QAAQA,EAAO,UAAW,CAAC,CACpC,CAEA,IAAIyV,GAAO,CACT,WAAA/B,GACA,QAAAU,GACA,YAAAmB,EACF,EAEA,SAASG,GAAcC,EAAQ,CAC7B,MAAM3V,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,eAAiBA,EAAO,OAAO,eAAiBA,EAAO,UAAYA,EAAO,OAAO,QAAS,OAC7G,MAAMzB,EAAKyB,EAAO,OAAO,oBAAsB,YAAcA,EAAO,GAAKA,EAAO,UAC5EA,EAAO,YACTA,EAAO,oBAAsB,IAE/BzB,EAAG,MAAM,OAAS,OAClBA,EAAG,MAAM,OAASoX,EAAS,WAAa,OACpC3V,EAAO,WACT,sBAAsB,IAAM,CAC1BA,EAAO,oBAAsB,EAC/B,CAAC,CAEL,CAEA,SAAS4V,IAAkB,CACzB,MAAM5V,EAAS,KACXA,EAAO,OAAO,eAAiBA,EAAO,UAAYA,EAAO,OAAO,UAGhEA,EAAO,YACTA,EAAO,oBAAsB,IAE/BA,EAAOA,EAAO,OAAO,oBAAsB,YAAc,KAAO,WAAW,EAAE,MAAM,OAAS,GACxFA,EAAO,WACT,sBAAsB,IAAM,CAC1BA,EAAO,oBAAsB,EAC/B,CAAC,EAEL,CAEA,IAAI6V,GAAa,CACf,cAAAH,GACA,gBAAAE,EACF,EAGA,SAASE,GAAe5U,EAAU6U,EAAM,CAClCA,IAAS,SACXA,EAAO,MAET,SAASC,EAAczX,EAAI,CACzB,GAAI,CAACA,GAAMA,IAAOhB,EAAW,GAAMgB,IAAOX,EAAS,EAAI,OAAO,KAC1DW,EAAG,eAAcA,EAAKA,EAAG,cAC7B,MAAM0X,EAAQ1X,EAAG,QAAQ2C,CAAQ,EACjC,MAAI,CAAC+U,GAAS,CAAC1X,EAAG,YACT,KAEF0X,GAASD,EAAczX,EAAG,YAAW,EAAG,IAAI,CACrD,CACA,OAAOyX,EAAcD,CAAI,CAC3B,CACA,SAASG,GAAiBlW,EAAQuH,EAAO4O,EAAQ,CAC/C,MAAM3X,EAASZ,EAAS,EAClB,CACJ,OAAA0K,CACJ,EAAMtI,EACEoW,EAAqB9N,EAAO,mBAC5B+N,EAAqB/N,EAAO,mBAClC,OAAI8N,IAAuBD,GAAUE,GAAsBF,GAAU3X,EAAO,WAAa6X,GACnFD,IAAuB,WACzB7O,EAAM,eAAc,EACb,IAEF,GAEF,EACT,CACA,SAAS+O,GAAa/O,EAAO,CAC3B,MAAMvH,EAAS,KACTsD,EAAW/F,EAAW,EAC5B,IAAIwT,EAAIxJ,EACJwJ,EAAE,gBAAeA,EAAIA,EAAE,eAC3B,MAAMjJ,EAAO9H,EAAO,gBACpB,GAAI+Q,EAAE,OAAS,cAAe,CAC5B,GAAIjJ,EAAK,YAAc,MAAQA,EAAK,YAAciJ,EAAE,UAClD,OAEFjJ,EAAK,UAAYiJ,EAAE,SACrB,MAAWA,EAAE,OAAS,cAAgBA,EAAE,cAAc,SAAW,IAC/DjJ,EAAK,QAAUiJ,EAAE,cAAc,CAAC,EAAE,YAEpC,GAAIA,EAAE,OAAS,aAAc,CAE3BmF,GAAiBlW,EAAQ+Q,EAAGA,EAAE,cAAc,CAAC,EAAE,KAAK,EACpD,MACF,CACA,KAAM,CACJ,OAAAzI,EACA,QAAAiO,EACA,QAAA9E,CACJ,EAAMzR,EAGJ,GAFI,CAACyR,GACD,CAACnJ,EAAO,eAAiByI,EAAE,cAAgB,SAC3C/Q,EAAO,WAAasI,EAAO,+BAC7B,OAEE,CAACtI,EAAO,WAAasI,EAAO,SAAWA,EAAO,MAChDtI,EAAO,QAAO,EAEhB,IAAIwW,EAAWzF,EAAE,OAMjB,GALIzI,EAAO,oBAAsB,WAC3B,CAAC9G,GAAiBgV,EAAUxW,EAAO,SAAS,GAE9C,UAAW+Q,GAAKA,EAAE,QAAU,GAC5B,WAAYA,GAAKA,EAAE,OAAS,GAC5BjJ,EAAK,WAAaA,EAAK,QAAS,OAGpC,MAAM2O,EAAuB,CAAC,CAACnO,EAAO,gBAAkBA,EAAO,iBAAmB,GAE5EoO,EAAY3F,EAAE,aAAeA,EAAE,aAAY,EAAKA,EAAE,KACpD0F,GAAwB1F,EAAE,QAAUA,EAAE,OAAO,YAAc2F,IAC7DF,EAAWE,EAAU,CAAC,GAExB,MAAMC,EAAoBrO,EAAO,kBAAoBA,EAAO,kBAAoB,IAAIA,EAAO,cAAc,GACnGsO,EAAiB,CAAC,EAAE7F,EAAE,QAAUA,EAAE,OAAO,YAG/C,GAAIzI,EAAO,YAAcsO,EAAiBd,GAAea,EAAmBH,CAAQ,EAAIA,EAAS,QAAQG,CAAiB,GAAI,CAC5H3W,EAAO,WAAa,GACpB,MACF,CACA,GAAIsI,EAAO,cACL,CAACkO,EAAS,QAAQlO,EAAO,YAAY,EAAG,OAE9CiO,EAAQ,SAAWxF,EAAE,MACrBwF,EAAQ,SAAWxF,EAAE,MACrB,MAAMoF,EAASI,EAAQ,SACjBM,EAASN,EAAQ,SAIvB,GAAI,CAACL,GAAiBlW,EAAQ+Q,EAAGoF,CAAM,EACrC,OAEF,OAAO,OAAOrO,EAAM,CAClB,UAAW,GACX,QAAS,GACT,oBAAqB,GACrB,YAAa,OACb,YAAa,MACjB,CAAG,EACDyO,EAAQ,OAASJ,EACjBI,EAAQ,OAASM,EACjB/O,EAAK,eAAiBzJ,GAAG,EACzB2B,EAAO,WAAa,GACpBA,EAAO,WAAU,EACjBA,EAAO,eAAiB,OACpBsI,EAAO,UAAY,IAAGR,EAAK,mBAAqB,IACpD,IAAIgP,EAAiB,GACjBN,EAAS,QAAQ1O,EAAK,iBAAiB,IACzCgP,EAAiB,GACbN,EAAS,WAAa,WACxB1O,EAAK,UAAY,KAGjBxE,EAAS,eAAiBA,EAAS,cAAc,QAAQwE,EAAK,iBAAiB,GAAKxE,EAAS,gBAAkBkT,IAAazF,EAAE,cAAgB,SAAWA,EAAE,cAAgB,SAAW,CAACyF,EAAS,QAAQ1O,EAAK,iBAAiB,IAChOxE,EAAS,cAAc,KAAI,EAE7B,MAAMyT,EAAuBD,GAAkB9W,EAAO,gBAAkBsI,EAAO,0BAC1EA,EAAO,+BAAiCyO,IAAyB,CAACP,EAAS,mBAC9EzF,EAAE,eAAc,EAEdzI,EAAO,UAAYA,EAAO,SAAS,SAAWtI,EAAO,UAAYA,EAAO,WAAa,CAACsI,EAAO,SAC/FtI,EAAO,SAAS,aAAY,EAE9BA,EAAO,KAAK,aAAc+Q,CAAC,CAC7B,CAEA,SAASiG,GAAYzP,EAAO,CAC1B,MAAMjE,EAAW/F,EAAW,EACtByC,EAAS,KACT8H,EAAO9H,EAAO,gBACd,CACJ,OAAAsI,EACA,QAAAiO,EACA,aAAc7N,EACd,QAAA+I,CACJ,EAAMzR,EAEJ,GADI,CAACyR,GACD,CAACnJ,EAAO,eAAiBf,EAAM,cAAgB,QAAS,OAC5D,IAAIwJ,EAAIxJ,EAER,GADIwJ,EAAE,gBAAeA,EAAIA,EAAE,eACvBA,EAAE,OAAS,gBACTjJ,EAAK,UAAY,MACViJ,EAAE,YACFjJ,EAAK,WAAW,OAE7B,IAAImP,EACJ,GAAIlG,EAAE,OAAS,aAEb,GADAkG,EAAc,CAAC,GAAGlG,EAAE,cAAc,EAAE,KAAKc,GAAKA,EAAE,aAAe/J,EAAK,OAAO,EACvE,CAACmP,GAAeA,EAAY,aAAenP,EAAK,QAAS,YAE7DmP,EAAclG,EAEhB,GAAI,CAACjJ,EAAK,UAAW,CACfA,EAAK,aAAeA,EAAK,aAC3B9H,EAAO,KAAK,oBAAqB+Q,CAAC,EAEpC,MACF,CACA,MAAMmG,EAAQD,EAAY,MACpBE,EAAQF,EAAY,MAC1B,GAAIlG,EAAE,wBAAyB,CAC7BwF,EAAQ,OAASW,EACjBX,EAAQ,OAASY,EACjB,MACF,CACA,GAAI,CAACnX,EAAO,eAAgB,CACrB+Q,EAAE,OAAO,QAAQjJ,EAAK,iBAAiB,IAC1C9H,EAAO,WAAa,IAElB8H,EAAK,YACP,OAAO,OAAOyO,EAAS,CACrB,OAAQW,EACR,OAAQC,EACR,SAAUD,EACV,SAAUC,CAClB,CAAO,EACDrP,EAAK,eAAiBzJ,GAAG,GAE3B,MACF,CACA,GAAIiK,EAAO,qBAAuB,CAACA,EAAO,KACxC,GAAItI,EAAO,cAET,GAAImX,EAAQZ,EAAQ,QAAUvW,EAAO,WAAaA,EAAO,aAAY,GAAMmX,EAAQZ,EAAQ,QAAUvW,EAAO,WAAaA,EAAO,eAAgB,CAC9I8H,EAAK,UAAY,GACjBA,EAAK,QAAU,GACf,MACF,MACK,IAAIY,IAAQwO,EAAQX,EAAQ,QAAU,CAACvW,EAAO,WAAaA,EAAO,aAAY,GAAMkX,EAAQX,EAAQ,QAAU,CAACvW,EAAO,WAAaA,EAAO,aAAY,GAC3J,OACK,GAAI,CAAC0I,IAAQwO,EAAQX,EAAQ,QAAUvW,EAAO,WAAaA,EAAO,aAAY,GAAMkX,EAAQX,EAAQ,QAAUvW,EAAO,WAAaA,EAAO,aAAY,GAC1J,OAMJ,GAHIsD,EAAS,eAAiBA,EAAS,cAAc,QAAQwE,EAAK,iBAAiB,GAAKxE,EAAS,gBAAkByN,EAAE,QAAUA,EAAE,cAAgB,SAC/IzN,EAAS,cAAc,KAAI,EAEzBA,EAAS,eACPyN,EAAE,SAAWzN,EAAS,eAAiByN,EAAE,OAAO,QAAQjJ,EAAK,iBAAiB,EAAG,CACnFA,EAAK,QAAU,GACf9H,EAAO,WAAa,GACpB,MACF,CAEE8H,EAAK,qBACP9H,EAAO,KAAK,YAAa+Q,CAAC,EAE5BwF,EAAQ,UAAYA,EAAQ,SAC5BA,EAAQ,UAAYA,EAAQ,SAC5BA,EAAQ,SAAWW,EACnBX,EAAQ,SAAWY,EACnB,MAAMC,EAAQb,EAAQ,SAAWA,EAAQ,OACnCc,EAAQd,EAAQ,SAAWA,EAAQ,OACzC,GAAIvW,EAAO,OAAO,WAAa,KAAK,KAAKoX,GAAS,EAAIC,GAAS,CAAC,EAAIrX,EAAO,OAAO,UAAW,OAC7F,GAAI,OAAO8H,EAAK,YAAgB,IAAa,CAC3C,IAAIwP,EACAtX,EAAO,aAAY,GAAMuW,EAAQ,WAAaA,EAAQ,QAAUvW,EAAO,WAAU,GAAMuW,EAAQ,WAAaA,EAAQ,OACtHzO,EAAK,YAAc,GAGfsP,EAAQA,EAAQC,EAAQA,GAAS,KACnCC,EAAa,KAAK,MAAM,KAAK,IAAID,CAAK,EAAG,KAAK,IAAID,CAAK,CAAC,EAAI,IAAM,KAAK,GACvEtP,EAAK,YAAc9H,EAAO,eAAiBsX,EAAahP,EAAO,WAAa,GAAKgP,EAAahP,EAAO,WAG3G,CASA,GARIR,EAAK,aACP9H,EAAO,KAAK,oBAAqB+Q,CAAC,EAEhC,OAAOjJ,EAAK,YAAgB,MAC1ByO,EAAQ,WAAaA,EAAQ,QAAUA,EAAQ,WAAaA,EAAQ,UACtEzO,EAAK,YAAc,IAGnBA,EAAK,aAAeiJ,EAAE,OAAS,aAAejJ,EAAK,gCAAiC,CACtFA,EAAK,UAAY,GACjB,MACF,CACA,GAAI,CAACA,EAAK,YACR,OAEF9H,EAAO,WAAa,GAChB,CAACsI,EAAO,SAAWyI,EAAE,YACvBA,EAAE,eAAc,EAEdzI,EAAO,0BAA4B,CAACA,EAAO,QAC7CyI,EAAE,gBAAe,EAEnB,IAAIqE,EAAOpV,EAAO,aAAY,EAAKoX,EAAQC,EACvCE,EAAcvX,EAAO,aAAY,EAAKuW,EAAQ,SAAWA,EAAQ,UAAYA,EAAQ,SAAWA,EAAQ,UACxGjO,EAAO,iBACT8M,EAAO,KAAK,IAAIA,CAAI,GAAK1M,EAAM,EAAI,IACnC6O,EAAc,KAAK,IAAIA,CAAW,GAAK7O,EAAM,EAAI,KAEnD6N,EAAQ,KAAOnB,EACfA,GAAQ9M,EAAO,WACXI,IACF0M,EAAO,CAACA,EACRmC,EAAc,CAACA,GAEjB,MAAMC,EAAuBxX,EAAO,iBACpCA,EAAO,eAAiBoV,EAAO,EAAI,OAAS,OAC5CpV,EAAO,iBAAmBuX,EAAc,EAAI,OAAS,OACrD,MAAME,EAASzX,EAAO,OAAO,MAAQ,CAACsI,EAAO,QACvCoP,EAAe1X,EAAO,mBAAqB,QAAUA,EAAO,gBAAkBA,EAAO,mBAAqB,QAAUA,EAAO,eACjI,GAAI,CAAC8H,EAAK,QAAS,CAQjB,GAPI2P,GAAUC,GACZ1X,EAAO,QAAQ,CACb,UAAWA,EAAO,cAC1B,CAAO,EAEH8H,EAAK,eAAiB9H,EAAO,aAAY,EACzCA,EAAO,cAAc,CAAC,EAClBA,EAAO,UAAW,CACpB,MAAM2X,EAAM,IAAI,OAAO,YAAY,gBAAiB,CAClD,QAAS,GACT,WAAY,GACZ,OAAQ,CACN,kBAAmB,EAC7B,CACA,CAAO,EACD3X,EAAO,UAAU,cAAc2X,CAAG,CACpC,CACA7P,EAAK,oBAAsB,GAEvBQ,EAAO,aAAetI,EAAO,iBAAmB,IAAQA,EAAO,iBAAmB,KACpFA,EAAO,cAAc,EAAI,EAE3BA,EAAO,KAAK,kBAAmB+Q,CAAC,CAClC,CAGA,GADA,IAAI,KAAI,EAAG,QAAO,EACdzI,EAAO,iBAAmB,IAASR,EAAK,SAAWA,EAAK,oBAAsB0P,IAAyBxX,EAAO,kBAAoByX,GAAUC,GAAgB,KAAK,IAAItC,CAAI,GAAK,EAAG,CACnL,OAAO,OAAOmB,EAAS,CACrB,OAAQW,EACR,OAAQC,EACR,SAAUD,EACV,SAAUC,EACV,eAAgBrP,EAAK,gBAC3B,CAAK,EACDA,EAAK,cAAgB,GACrBA,EAAK,eAAiBA,EAAK,iBAC3B,MACF,CACA9H,EAAO,KAAK,aAAc+Q,CAAC,EAC3BjJ,EAAK,QAAU,GACfA,EAAK,iBAAmBsN,EAAOtN,EAAK,eACpC,IAAI8P,EAAsB,GACtBC,EAAkBvP,EAAO,gBAiD7B,GAhDIA,EAAO,sBACTuP,EAAkB,GAEhBzC,EAAO,GACLqC,GAAUC,GAA8B5P,EAAK,oBAAsBA,EAAK,kBAAoBQ,EAAO,eAAiBtI,EAAO,eAAiBA,EAAO,gBAAgBA,EAAO,YAAc,CAAC,GAAKsI,EAAO,gBAAkB,QAAUtI,EAAO,OAAO,OAASsI,EAAO,eAAiB,EAAItI,EAAO,gBAAgBA,EAAO,YAAc,CAAC,EAAIA,EAAO,OAAO,aAAe,GAAKA,EAAO,OAAO,aAAeA,EAAO,aAAY,IACzZA,EAAO,QAAQ,CACb,UAAW,OACX,aAAc,GACd,iBAAkB,CAC1B,CAAO,EAEC8H,EAAK,iBAAmB9H,EAAO,aAAY,IAC7C4X,EAAsB,GAClBtP,EAAO,aACTR,EAAK,iBAAmB9H,EAAO,aAAY,EAAK,GAAK,CAACA,EAAO,aAAY,EAAK8H,EAAK,eAAiBsN,IAASyC,KAGxGzC,EAAO,IACZqC,GAAUC,GAA8B5P,EAAK,oBAAsBA,EAAK,kBAAoBQ,EAAO,eAAiBtI,EAAO,aAAY,EAAKA,EAAO,gBAAgBA,EAAO,gBAAgB,OAAS,CAAC,EAAIA,EAAO,OAAO,cAAgBsI,EAAO,gBAAkB,QAAUtI,EAAO,OAAO,OAASsI,EAAO,eAAiB,EAAItI,EAAO,gBAAgBA,EAAO,gBAAgB,OAAS,CAAC,EAAIA,EAAO,OAAO,aAAe,GAAKA,EAAO,aAAY,IAC/aA,EAAO,QAAQ,CACb,UAAW,OACX,aAAc,GACd,iBAAkBA,EAAO,OAAO,QAAUsI,EAAO,gBAAkB,OAAStI,EAAO,qBAAoB,EAAK,KAAK,KAAK,WAAWsI,EAAO,cAAe,EAAE,CAAC,EAClK,CAAO,EAECR,EAAK,iBAAmB9H,EAAO,aAAY,IAC7C4X,EAAsB,GAClBtP,EAAO,aACTR,EAAK,iBAAmB9H,EAAO,aAAY,EAAK,GAAKA,EAAO,aAAY,EAAK8H,EAAK,eAAiBsN,IAASyC,KAI9GD,IACF7G,EAAE,wBAA0B,IAI1B,CAAC/Q,EAAO,gBAAkBA,EAAO,iBAAmB,QAAU8H,EAAK,iBAAmBA,EAAK,iBAC7FA,EAAK,iBAAmBA,EAAK,gBAE3B,CAAC9H,EAAO,gBAAkBA,EAAO,iBAAmB,QAAU8H,EAAK,iBAAmBA,EAAK,iBAC7FA,EAAK,iBAAmBA,EAAK,gBAE3B,CAAC9H,EAAO,gBAAkB,CAACA,EAAO,iBACpC8H,EAAK,iBAAmBA,EAAK,gBAI3BQ,EAAO,UAAY,EACrB,GAAI,KAAK,IAAI8M,CAAI,EAAI9M,EAAO,WAAaR,EAAK,oBAC5C,GAAI,CAACA,EAAK,mBAAoB,CAC5BA,EAAK,mBAAqB,GAC1ByO,EAAQ,OAASA,EAAQ,SACzBA,EAAQ,OAASA,EAAQ,SACzBzO,EAAK,iBAAmBA,EAAK,eAC7ByO,EAAQ,KAAOvW,EAAO,aAAY,EAAKuW,EAAQ,SAAWA,EAAQ,OAASA,EAAQ,SAAWA,EAAQ,OACtG,MACF,MACK,CACLzO,EAAK,iBAAmBA,EAAK,eAC7B,MACF,CAEE,CAACQ,EAAO,cAAgBA,EAAO,WAG/BA,EAAO,UAAYA,EAAO,SAAS,SAAWtI,EAAO,UAAYsI,EAAO,uBAC1EtI,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,GAExBsI,EAAO,UAAYA,EAAO,SAAS,SAAWtI,EAAO,UACvDA,EAAO,SAAS,YAAW,EAG7BA,EAAO,eAAe8H,EAAK,gBAAgB,EAE3C9H,EAAO,aAAa8H,EAAK,gBAAgB,EAC3C,CAEA,SAASgQ,GAAWvQ,EAAO,CACzB,MAAMvH,EAAS,KACT8H,EAAO9H,EAAO,gBACpB,IAAI+Q,EAAIxJ,EACJwJ,EAAE,gBAAeA,EAAIA,EAAE,eAC3B,IAAIkG,EAEJ,GADqBlG,EAAE,OAAS,YAAcA,EAAE,OAAS,eAOvD,GADAkG,EAAc,CAAC,GAAGlG,EAAE,cAAc,EAAE,KAAKc,GAAKA,EAAE,aAAe/J,EAAK,OAAO,EACvE,CAACmP,GAAeA,EAAY,aAAenP,EAAK,QAAS,WAN5C,CAEjB,GADIA,EAAK,UAAY,MACjBiJ,EAAE,YAAcjJ,EAAK,UAAW,OACpCmP,EAAclG,CAChB,CAIA,GAAI,CAAC,gBAAiB,aAAc,eAAgB,aAAa,EAAE,SAASA,EAAE,IAAI,GAE5E,EADY,CAAC,gBAAiB,aAAa,EAAE,SAASA,EAAE,IAAI,IAAM/Q,EAAO,QAAQ,UAAYA,EAAO,QAAQ,YAE9G,OAGJ8H,EAAK,UAAY,KACjBA,EAAK,QAAU,KACf,KAAM,CACJ,OAAAQ,EACA,QAAAiO,EACA,aAAc7N,EACd,WAAAO,EACA,QAAAwI,CACJ,EAAMzR,EAEJ,GADI,CAACyR,GACD,CAACnJ,EAAO,eAAiByI,EAAE,cAAgB,QAAS,OAKxD,GAJIjJ,EAAK,qBACP9H,EAAO,KAAK,WAAY+Q,CAAC,EAE3BjJ,EAAK,oBAAsB,GACvB,CAACA,EAAK,UAAW,CACfA,EAAK,SAAWQ,EAAO,YACzBtI,EAAO,cAAc,EAAK,EAE5B8H,EAAK,QAAU,GACfA,EAAK,YAAc,GACnB,MACF,CAGIQ,EAAO,YAAcR,EAAK,SAAWA,EAAK,YAAc9H,EAAO,iBAAmB,IAAQA,EAAO,iBAAmB,KACtHA,EAAO,cAAc,EAAK,EAI5B,MAAM+X,EAAe1Z,GAAG,EAClB2Z,EAAWD,EAAejQ,EAAK,eAGrC,GAAI9H,EAAO,WAAY,CACrB,MAAMiY,EAAWlH,EAAE,MAAQA,EAAE,cAAgBA,EAAE,aAAY,EAC3D/Q,EAAO,mBAAmBiY,GAAYA,EAAS,CAAC,GAAKlH,EAAE,OAAQkH,CAAQ,EACvEjY,EAAO,KAAK,YAAa+Q,CAAC,EACtBiH,EAAW,KAAOD,EAAejQ,EAAK,cAAgB,KACxD9H,EAAO,KAAK,wBAAyB+Q,CAAC,CAE1C,CAKA,GAJAjJ,EAAK,cAAgBzJ,GAAG,EACxBF,GAAS,IAAM,CACR6B,EAAO,YAAWA,EAAO,WAAa,GAC7C,CAAC,EACG,CAAC8H,EAAK,WAAa,CAACA,EAAK,SAAW,CAAC9H,EAAO,gBAAkBuW,EAAQ,OAAS,GAAK,CAACzO,EAAK,eAAiBA,EAAK,mBAAqBA,EAAK,gBAAkB,CAACA,EAAK,cAAe,CACnLA,EAAK,UAAY,GACjBA,EAAK,QAAU,GACfA,EAAK,YAAc,GACnB,MACF,CACAA,EAAK,UAAY,GACjBA,EAAK,QAAU,GACfA,EAAK,YAAc,GACnB,IAAIoQ,EAMJ,GALI5P,EAAO,aACT4P,EAAaxP,EAAM1I,EAAO,UAAY,CAACA,EAAO,UAE9CkY,EAAa,CAACpQ,EAAK,iBAEjBQ,EAAO,QACT,OAEF,GAAIA,EAAO,UAAYA,EAAO,SAAS,QAAS,CAC9CtI,EAAO,SAAS,WAAW,CACzB,WAAAkY,CACN,CAAK,EACD,MACF,CAGA,MAAMC,EAAcD,GAAc,CAAClY,EAAO,aAAY,GAAM,CAACA,EAAO,OAAO,KAC3E,IAAIoY,EAAY,EACZzN,EAAY3K,EAAO,gBAAgB,CAAC,EACxC,QAASZ,EAAI,EAAGA,EAAI6J,EAAW,OAAQ7J,GAAKA,EAAIkJ,EAAO,mBAAqB,EAAIA,EAAO,eAAgB,CACrG,MAAMiK,EAAYnT,EAAIkJ,EAAO,mBAAqB,EAAI,EAAIA,EAAO,eAC7D,OAAOW,EAAW7J,EAAImT,CAAS,EAAM,KACnC4F,GAAeD,GAAcjP,EAAW7J,CAAC,GAAK8Y,EAAajP,EAAW7J,EAAImT,CAAS,KACrF6F,EAAYhZ,EACZuL,EAAY1B,EAAW7J,EAAImT,CAAS,EAAItJ,EAAW7J,CAAC,IAE7C+Y,GAAeD,GAAcjP,EAAW7J,CAAC,KAClDgZ,EAAYhZ,EACZuL,EAAY1B,EAAWA,EAAW,OAAS,CAAC,EAAIA,EAAWA,EAAW,OAAS,CAAC,EAEpF,CACA,IAAIoP,EAAmB,KACnBC,EAAkB,KAClBhQ,EAAO,SACLtI,EAAO,YACTsY,EAAkBhQ,EAAO,SAAWA,EAAO,QAAQ,SAAWtI,EAAO,QAAUA,EAAO,QAAQ,OAAO,OAAS,EAAIA,EAAO,OAAO,OAAS,EAChIA,EAAO,QAChBqY,EAAmB,IAIvB,MAAME,GAASL,EAAajP,EAAWmP,CAAS,GAAKzN,EAC/C4H,EAAY6F,EAAY9P,EAAO,mBAAqB,EAAI,EAAIA,EAAO,eACzE,GAAI0P,EAAW1P,EAAO,aAAc,CAElC,GAAI,CAACA,EAAO,WAAY,CACtBtI,EAAO,QAAQA,EAAO,WAAW,EACjC,MACF,CACIA,EAAO,iBAAmB,SACxBuY,GAASjQ,EAAO,gBAAiBtI,EAAO,QAAQsI,EAAO,QAAUtI,EAAO,MAAQqY,EAAmBD,EAAY7F,CAAS,EAAOvS,EAAO,QAAQoY,CAAS,GAEzJpY,EAAO,iBAAmB,SACxBuY,EAAQ,EAAIjQ,EAAO,gBACrBtI,EAAO,QAAQoY,EAAY7F,CAAS,EAC3B+F,IAAoB,MAAQC,EAAQ,GAAK,KAAK,IAAIA,CAAK,EAAIjQ,EAAO,gBAC3EtI,EAAO,QAAQsY,CAAe,EAE9BtY,EAAO,QAAQoY,CAAS,EAG9B,KAAO,CAEL,GAAI,CAAC9P,EAAO,YAAa,CACvBtI,EAAO,QAAQA,EAAO,WAAW,EACjC,MACF,CAC0BA,EAAO,aAAe+Q,EAAE,SAAW/Q,EAAO,WAAW,QAAU+Q,EAAE,SAAW/Q,EAAO,WAAW,QAQ7G+Q,EAAE,SAAW/Q,EAAO,WAAW,OACxCA,EAAO,QAAQoY,EAAY7F,CAAS,EAEpCvS,EAAO,QAAQoY,CAAS,GATpBpY,EAAO,iBAAmB,QAC5BA,EAAO,QAAQqY,IAAqB,KAAOA,EAAmBD,EAAY7F,CAAS,EAEjFvS,EAAO,iBAAmB,QAC5BA,EAAO,QAAQsY,IAAoB,KAAOA,EAAkBF,CAAS,EAO3E,CACF,CAEA,SAASI,IAAW,CAClB,MAAMxY,EAAS,KACT,CACJ,OAAAsI,EACA,GAAA/J,CACJ,EAAMyB,EACJ,GAAIzB,GAAMA,EAAG,cAAgB,EAAG,OAG5B+J,EAAO,aACTtI,EAAO,cAAa,EAItB,KAAM,CACJ,eAAAuU,EACA,eAAAD,EACA,SAAAtL,CACJ,EAAMhJ,EACE4I,EAAY5I,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAG1DA,EAAO,eAAiB,GACxBA,EAAO,eAAiB,GACxBA,EAAO,WAAU,EACjBA,EAAO,aAAY,EACnBA,EAAO,oBAAmB,EAC1B,MAAMyY,EAAgB7P,GAAaN,EAAO,MACrCA,EAAO,gBAAkB,QAAUA,EAAO,cAAgB,IAAMtI,EAAO,OAAS,CAACA,EAAO,aAAe,CAACA,EAAO,OAAO,gBAAkB,CAACyY,EAC5IzY,EAAO,QAAQA,EAAO,OAAO,OAAS,EAAG,EAAG,GAAO,EAAI,EAEnDA,EAAO,OAAO,MAAQ,CAAC4I,EACzB5I,EAAO,YAAYA,EAAO,UAAW,EAAG,GAAO,EAAI,EAEnDA,EAAO,QAAQA,EAAO,YAAa,EAAG,GAAO,EAAI,EAGjDA,EAAO,UAAYA,EAAO,SAAS,SAAWA,EAAO,SAAS,SAChE,aAAaA,EAAO,SAAS,aAAa,EAC1CA,EAAO,SAAS,cAAgB,WAAW,IAAM,CAC3CA,EAAO,UAAYA,EAAO,SAAS,SAAWA,EAAO,SAAS,QAChEA,EAAO,SAAS,OAAM,CAE1B,EAAG,GAAG,GAGRA,EAAO,eAAiBsU,EACxBtU,EAAO,eAAiBuU,EACpBvU,EAAO,OAAO,eAAiBgJ,IAAahJ,EAAO,UACrDA,EAAO,cAAa,CAExB,CAEA,SAAS0Y,GAAQ3H,EAAG,CAClB,MAAM/Q,EAAS,KACVA,EAAO,UACPA,EAAO,aACNA,EAAO,OAAO,eAAe+Q,EAAE,eAAc,EAC7C/Q,EAAO,OAAO,0BAA4BA,EAAO,YACnD+Q,EAAE,gBAAe,EACjBA,EAAE,yBAAwB,IAGhC,CAEA,SAAS4H,IAAW,CAClB,MAAM3Y,EAAS,KACT,CACJ,UAAAuI,EACA,aAAAkK,EACA,QAAAhB,CACJ,EAAMzR,EACJ,GAAI,CAACyR,EAAS,OACdzR,EAAO,kBAAoBA,EAAO,UAC9BA,EAAO,eACTA,EAAO,UAAY,CAACuI,EAAU,WAE9BvI,EAAO,UAAY,CAACuI,EAAU,UAG5BvI,EAAO,YAAc,IAAGA,EAAO,UAAY,GAC/CA,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,EAC1B,IAAIsQ,EACJ,MAAMxD,EAAiB9M,EAAO,aAAY,EAAKA,EAAO,aAAY,EAC9D8M,IAAmB,EACrBwD,EAAc,EAEdA,GAAetQ,EAAO,UAAYA,EAAO,aAAY,GAAM8M,EAEzDwD,IAAgBtQ,EAAO,UACzBA,EAAO,eAAeyS,EAAe,CAACzS,EAAO,UAAYA,EAAO,SAAS,EAE3EA,EAAO,KAAK,eAAgBA,EAAO,UAAW,EAAK,CACrD,CAEA,SAAS4Y,GAAO7H,EAAG,CACjB,MAAM/Q,EAAS,KACfmO,GAAqBnO,EAAQ+Q,EAAE,MAAM,EACjC,EAAA/Q,EAAO,OAAO,SAAWA,EAAO,OAAO,gBAAkB,QAAU,CAACA,EAAO,OAAO,aAGtFA,EAAO,OAAM,CACf,CAEA,SAAS6Y,IAAuB,CAC9B,MAAM7Y,EAAS,KACXA,EAAO,gCACXA,EAAO,8BAAgC,GACnCA,EAAO,OAAO,sBAChBA,EAAO,GAAG,MAAM,YAAc,QAElC,CAEA,MAAMkH,GAAS,CAAClH,EAAQsH,IAAW,CACjC,MAAMhE,EAAW/F,EAAW,EACtB,CACJ,OAAA+K,EACA,GAAA/J,EACA,UAAAgK,EACA,OAAAzE,CACJ,EAAM9D,EACE8Y,EAAU,CAAC,CAACxQ,EAAO,OACnByQ,EAAYzR,IAAW,KAAO,mBAAqB,sBACnD0R,EAAe1R,EACjB,CAAC/I,GAAM,OAAOA,GAAO,WAGzB+E,EAASyV,CAAS,EAAE,aAAc/Y,EAAO,qBAAsB,CAC7D,QAAS,GACT,QAAA8Y,CACJ,CAAG,EACDva,EAAGwa,CAAS,EAAE,aAAc/Y,EAAO,aAAc,CAC/C,QAAS,EACb,CAAG,EACDzB,EAAGwa,CAAS,EAAE,cAAe/Y,EAAO,aAAc,CAChD,QAAS,EACb,CAAG,EACDsD,EAASyV,CAAS,EAAE,YAAa/Y,EAAO,YAAa,CACnD,QAAS,GACT,QAAA8Y,CACJ,CAAG,EACDxV,EAASyV,CAAS,EAAE,cAAe/Y,EAAO,YAAa,CACrD,QAAS,GACT,QAAA8Y,CACJ,CAAG,EACDxV,EAASyV,CAAS,EAAE,WAAY/Y,EAAO,WAAY,CACjD,QAAS,EACb,CAAG,EACDsD,EAASyV,CAAS,EAAE,YAAa/Y,EAAO,WAAY,CAClD,QAAS,EACb,CAAG,EACDsD,EAASyV,CAAS,EAAE,gBAAiB/Y,EAAO,WAAY,CACtD,QAAS,EACb,CAAG,EACDsD,EAASyV,CAAS,EAAE,cAAe/Y,EAAO,WAAY,CACpD,QAAS,EACb,CAAG,EACDsD,EAASyV,CAAS,EAAE,aAAc/Y,EAAO,WAAY,CACnD,QAAS,EACb,CAAG,EACDsD,EAASyV,CAAS,EAAE,eAAgB/Y,EAAO,WAAY,CACrD,QAAS,EACb,CAAG,EACDsD,EAASyV,CAAS,EAAE,cAAe/Y,EAAO,WAAY,CACpD,QAAS,EACb,CAAG,GAGGsI,EAAO,eAAiBA,EAAO,2BACjC/J,EAAGwa,CAAS,EAAE,QAAS/Y,EAAO,QAAS,EAAI,EAEzCsI,EAAO,SACTC,EAAUwQ,CAAS,EAAE,SAAU/Y,EAAO,QAAQ,EAI5CsI,EAAO,qBACTtI,EAAOgZ,CAAY,EAAElV,EAAO,KAAOA,EAAO,QAAU,0CAA4C,wBAAyB0U,GAAU,EAAI,EAEvIxY,EAAOgZ,CAAY,EAAE,iBAAkBR,GAAU,EAAI,EAIvDja,EAAGwa,CAAS,EAAE,OAAQ/Y,EAAO,OAAQ,CACnC,QAAS,EACb,CAAG,EACH,EACA,SAASiZ,IAAe,CACtB,MAAMjZ,EAAS,KACT,CACJ,OAAAsI,CACJ,EAAMtI,EACJA,EAAO,aAAesW,GAAa,KAAKtW,CAAM,EAC9CA,EAAO,YAAcgX,GAAY,KAAKhX,CAAM,EAC5CA,EAAO,WAAa8X,GAAW,KAAK9X,CAAM,EAC1CA,EAAO,qBAAuB6Y,GAAqB,KAAK7Y,CAAM,EAC1DsI,EAAO,UACTtI,EAAO,SAAW2Y,GAAS,KAAK3Y,CAAM,GAExCA,EAAO,QAAU0Y,GAAQ,KAAK1Y,CAAM,EACpCA,EAAO,OAAS4Y,GAAO,KAAK5Y,CAAM,EAClCkH,GAAOlH,EAAQ,IAAI,CACrB,CACA,SAASkZ,IAAe,CAEtBhS,GADe,KACA,KAAK,CACtB,CACA,IAAIiS,GAAW,CACb,aAAAF,GACA,aAAAC,EACF,EAEA,MAAME,GAAgB,CAACpZ,EAAQsI,IACtBtI,EAAO,MAAQsI,EAAO,MAAQA,EAAO,KAAK,KAAO,EAE1D,SAAS+Q,IAAgB,CACvB,MAAMrZ,EAAS,KACT,CACJ,UAAA8O,EACA,YAAAwK,EACA,OAAAhR,EACA,GAAA/J,CACJ,EAAMyB,EACEuZ,EAAcjR,EAAO,YAC3B,GAAI,CAACiR,GAAeA,GAAe,OAAO,KAAKA,CAAW,EAAE,SAAW,EAAG,OAC1E,MAAMjW,EAAW/F,EAAW,EAGtBic,EAAkBlR,EAAO,kBAAoB,UAAY,CAACA,EAAO,gBAAkBA,EAAO,gBAAkB,YAC5GmR,EAAsB,CAAC,SAAU,WAAW,EAAE,SAASnR,EAAO,eAAe,GAAK,CAACA,EAAO,gBAAkBtI,EAAO,GAAKsD,EAAS,cAAcgF,EAAO,eAAe,EACrKoR,EAAa1Z,EAAO,cAAcuZ,EAAaC,EAAiBC,CAAmB,EACzF,GAAI,CAACC,GAAc1Z,EAAO,oBAAsB0Z,EAAY,OAE5D,MAAMC,GADuBD,KAAcH,EAAcA,EAAYG,CAAU,EAAI,SAClC1Z,EAAO,eAClD4Z,EAAcR,GAAcpZ,EAAQsI,CAAM,EAC1CuR,EAAaT,GAAcpZ,EAAQ2Z,CAAgB,EACnDG,EAAgB9Z,EAAO,OAAO,WAC9B+Z,EAAeJ,EAAiB,WAChCK,EAAa1R,EAAO,QACtBsR,GAAe,CAACC,GAClBtb,EAAG,UAAU,OAAO,GAAG+J,EAAO,sBAAsB,OAAQ,GAAGA,EAAO,sBAAsB,aAAa,EACzGtI,EAAO,qBAAoB,GAClB,CAAC4Z,GAAeC,IACzBtb,EAAG,UAAU,IAAI,GAAG+J,EAAO,sBAAsB,MAAM,GACnDqR,EAAiB,KAAK,MAAQA,EAAiB,KAAK,OAAS,UAAY,CAACA,EAAiB,KAAK,MAAQrR,EAAO,KAAK,OAAS,WAC/H/J,EAAG,UAAU,IAAI,GAAG+J,EAAO,sBAAsB,aAAa,EAEhEtI,EAAO,qBAAoB,GAEzB8Z,GAAiB,CAACC,EACpB/Z,EAAO,gBAAe,EACb,CAAC8Z,GAAiBC,GAC3B/Z,EAAO,cAAa,EAItB,CAAC,aAAc,aAAc,WAAW,EAAE,QAAQsC,GAAQ,CACxD,GAAI,OAAOqX,EAAiBrX,CAAI,EAAM,IAAa,OACnD,MAAM2X,EAAmB3R,EAAOhG,CAAI,GAAKgG,EAAOhG,CAAI,EAAE,QAChD4X,EAAkBP,EAAiBrX,CAAI,GAAKqX,EAAiBrX,CAAI,EAAE,QACrE2X,GAAoB,CAACC,GACvBla,EAAOsC,CAAI,EAAE,QAAO,EAElB,CAAC2X,GAAoBC,GACvBla,EAAOsC,CAAI,EAAE,OAAM,CAEvB,CAAC,EACD,MAAM6X,EAAmBR,EAAiB,WAAaA,EAAiB,YAAcrR,EAAO,UACvF8R,EAAc9R,EAAO,OAASqR,EAAiB,gBAAkBrR,EAAO,eAAiB6R,GACzFE,EAAU/R,EAAO,KACnB6R,GAAoBb,GACtBtZ,EAAO,gBAAe,EAExB/C,EAAO+C,EAAO,OAAQ2Z,CAAgB,EACtC,MAAMW,EAAYta,EAAO,OAAO,QAC1Bua,EAAUva,EAAO,OAAO,KAC9B,OAAO,OAAOA,EAAQ,CACpB,eAAgBA,EAAO,OAAO,eAC9B,eAAgBA,EAAO,OAAO,eAC9B,eAAgBA,EAAO,OAAO,cAClC,CAAG,EACGga,GAAc,CAACM,EACjBta,EAAO,QAAO,EACL,CAACga,GAAcM,GACxBta,EAAO,OAAM,EAEfA,EAAO,kBAAoB0Z,EAC3B1Z,EAAO,KAAK,oBAAqB2Z,CAAgB,EAC7CL,IACEc,GACFpa,EAAO,YAAW,EAClBA,EAAO,WAAW8O,CAAS,EAC3B9O,EAAO,aAAY,GACV,CAACqa,GAAWE,GACrBva,EAAO,WAAW8O,CAAS,EAC3B9O,EAAO,aAAY,GACVqa,GAAW,CAACE,GACrBva,EAAO,YAAW,GAGtBA,EAAO,KAAK,aAAc2Z,CAAgB,CAC5C,CAEA,SAASa,GAAcjB,EAAaxD,EAAM0E,EAAa,CAIrD,GAHI1E,IAAS,SACXA,EAAO,UAEL,CAACwD,GAAexD,IAAS,aAAe,CAAC0E,EAAa,OAC1D,IAAIf,EAAa,GACjB,MAAMlb,EAASZ,EAAS,EAClB8c,EAAgB3E,IAAS,SAAWvX,EAAO,YAAcic,EAAY,aACrEE,EAAS,OAAO,KAAKpB,CAAW,EAAE,IAAIqB,GAAS,CACnD,GAAI,OAAOA,GAAU,UAAYA,EAAM,QAAQ,GAAG,IAAM,EAAG,CACzD,MAAMC,EAAW,WAAWD,EAAM,OAAO,CAAC,CAAC,EAE3C,MAAO,CACL,MAFYF,EAAgBG,EAG5B,MAAAD,CACR,CACI,CACA,MAAO,CACL,MAAOA,EACP,MAAAA,CACN,CACE,CAAC,EACDD,EAAO,KAAK,CAAC,EAAGG,IAAM,SAAS,EAAE,MAAO,EAAE,EAAI,SAASA,EAAE,MAAO,EAAE,CAAC,EACnE,QAAS1b,EAAI,EAAGA,EAAIub,EAAO,OAAQvb,GAAK,EAAG,CACzC,KAAM,CACJ,MAAAwb,EACA,MAAAG,CACN,EAAQJ,EAAOvb,CAAC,EACR2W,IAAS,SACPvX,EAAO,WAAW,eAAeuc,CAAK,KAAK,EAAE,UAC/CrB,EAAakB,GAENG,GAASN,EAAY,cAC9Bf,EAAakB,EAEjB,CACA,OAAOlB,GAAc,KACvB,CAEA,IAAIH,GAAc,CAChB,cAAAF,GACA,cAAAmB,EACF,EAEA,SAASQ,GAAepV,EAASqV,EAAQ,CACvC,MAAMC,EAAgB,CAAA,EACtB,OAAAtV,EAAQ,QAAQuV,GAAQ,CAClB,OAAOA,GAAS,SAClB,OAAO,KAAKA,CAAI,EAAE,QAAQC,GAAc,CAClCD,EAAKC,CAAU,GACjBF,EAAc,KAAKD,EAASG,CAAU,CAE1C,CAAC,EACQ,OAAOD,GAAS,UACzBD,EAAc,KAAKD,EAASE,CAAI,CAEpC,CAAC,EACMD,CACT,CACA,SAASG,IAAa,CACpB,MAAMrb,EAAS,KACT,CACJ,WAAAob,EACA,OAAA9S,EACA,IAAAI,EACA,GAAAnK,EACA,OAAAuF,CACJ,EAAM9D,EAEEsb,EAAWN,GAAe,CAAC,cAAe1S,EAAO,UAAW,CAChE,YAAatI,EAAO,OAAO,UAAYsI,EAAO,SAAS,OAC3D,EAAK,CACD,WAAcA,EAAO,UACzB,EAAK,CACD,IAAOI,CACX,EAAK,CACD,KAAQJ,EAAO,MAAQA,EAAO,KAAK,KAAO,CAC9C,EAAK,CACD,cAAeA,EAAO,MAAQA,EAAO,KAAK,KAAO,GAAKA,EAAO,KAAK,OAAS,QAC/E,EAAK,CACD,QAAWxE,EAAO,OACtB,EAAK,CACD,IAAOA,EAAO,GAClB,EAAK,CACD,WAAYwE,EAAO,OACvB,EAAK,CACD,SAAYA,EAAO,SAAWA,EAAO,cACzC,EAAK,CACD,iBAAkBA,EAAO,mBAC7B,CAAG,EAAGA,EAAO,sBAAsB,EACjC8S,EAAW,KAAK,GAAGE,CAAQ,EAC3B/c,EAAG,UAAU,IAAI,GAAG6c,CAAU,EAC9Bpb,EAAO,qBAAoB,CAC7B,CAEA,SAASub,IAAgB,CACvB,MAAMvb,EAAS,KACT,CACJ,GAAAzB,EACA,WAAA6c,CACJ,EAAMpb,EACA,CAACzB,GAAM,OAAOA,GAAO,WACzBA,EAAG,UAAU,OAAO,GAAG6c,CAAU,EACjCpb,EAAO,qBAAoB,EAC7B,CAEA,IAAIjC,GAAU,CACZ,WAAAsd,GACA,cAAAE,EACF,EAEA,SAASC,IAAgB,CACvB,MAAMxb,EAAS,KACT,CACJ,SAAUyb,EACV,OAAAnT,CACJ,EAAMtI,EACE,CACJ,mBAAA0b,CACJ,EAAMpT,EACJ,GAAIoT,EAAoB,CACtB,MAAMnO,EAAiBvN,EAAO,OAAO,OAAS,EACxC2b,EAAqB3b,EAAO,WAAWuN,CAAc,EAAIvN,EAAO,gBAAgBuN,CAAc,EAAImO,EAAqB,EAC7H1b,EAAO,SAAWA,EAAO,KAAO2b,CAClC,MACE3b,EAAO,SAAWA,EAAO,SAAS,SAAW,EAE3CsI,EAAO,iBAAmB,KAC5BtI,EAAO,eAAiB,CAACA,EAAO,UAE9BsI,EAAO,iBAAmB,KAC5BtI,EAAO,eAAiB,CAACA,EAAO,UAE9Byb,GAAaA,IAAczb,EAAO,WACpCA,EAAO,MAAQ,IAEbyb,IAAczb,EAAO,UACvBA,EAAO,KAAKA,EAAO,SAAW,OAAS,QAAQ,CAEnD,CACA,IAAI4b,GAAkB,CACpB,cAAAJ,EACF,EAEIK,GAAW,CACb,KAAM,GACN,UAAW,aACX,eAAgB,GAChB,sBAAuB,mBACvB,kBAAmB,UACnB,aAAc,EACd,MAAO,IACP,QAAS,GACT,qBAAsB,GACtB,eAAgB,GAChB,OAAQ,GACR,eAAgB,GAChB,aAAc,SACd,QAAS,GACT,kBAAmB,wDAEnB,MAAO,KACP,OAAQ,KAER,+BAAgC,GAEhC,UAAW,KACX,IAAK,KAEL,mBAAoB,GACpB,mBAAoB,GAEpB,WAAY,GAEZ,eAAgB,GAEhB,iBAAkB,GAElB,OAAQ,QAIR,YAAa,OACb,gBAAiB,SAEjB,aAAc,EACd,cAAe,EACf,eAAgB,EAChB,mBAAoB,EACpB,mBAAoB,GACpB,eAAgB,GAChB,qBAAsB,GACtB,mBAAoB,EAEpB,kBAAmB,EAEnB,oBAAqB,GACrB,yBAA0B,GAE1B,cAAe,GAEf,aAAc,GAEd,WAAY,EACZ,WAAY,GACZ,cAAe,GACf,YAAa,GACb,WAAY,GACZ,gBAAiB,GACjB,aAAc,IACd,aAAc,GACd,eAAgB,GAChB,UAAW,EACX,yBAA0B,GAC1B,yBAA0B,GAC1B,8BAA+B,GAC/B,oBAAqB,GAErB,kBAAmB,GAEnB,WAAY,GACZ,gBAAiB,IAEjB,oBAAqB,GAErB,WAAY,GAEZ,cAAe,GACf,yBAA0B,GAC1B,oBAAqB,GAErB,KAAM,GACN,mBAAoB,GACpB,qBAAsB,EACtB,oBAAqB,GAErB,OAAQ,GAER,eAAgB,GAChB,eAAgB,GAChB,aAAc,KAEd,UAAW,GACX,eAAgB,oBAChB,kBAAmB,KAEnB,iBAAkB,GAClB,wBAAyB,GAEzB,uBAAwB,UAExB,WAAY,eACZ,gBAAiB,qBACjB,iBAAkB,sBAClB,kBAAmB,uBACnB,uBAAwB,6BACxB,eAAgB,oBAChB,eAAgB,oBAChB,aAAc,iBACd,mBAAoB,wBACpB,oBAAqB,EAErB,mBAAoB,GAEpB,aAAc,EAChB,EAEA,SAASC,GAAmBxT,EAAQyT,EAAkB,CACpD,OAAO,SAAsB/e,EAAK,CAC5BA,IAAQ,SACVA,EAAM,CAAA,GAER,MAAMgf,EAAkB,OAAO,KAAKhf,CAAG,EAAE,CAAC,EACpCif,EAAejf,EAAIgf,CAAe,EACxC,GAAI,OAAOC,GAAiB,UAAYA,IAAiB,KAAM,CAC7Dhf,EAAO8e,EAAkB/e,CAAG,EAC5B,MACF,CAYA,GAXIsL,EAAO0T,CAAe,IAAM,KAC9B1T,EAAO0T,CAAe,EAAI,CACxB,QAAS,EACjB,GAEQA,IAAoB,cAAgB1T,EAAO0T,CAAe,GAAK1T,EAAO0T,CAAe,EAAE,SAAW,CAAC1T,EAAO0T,CAAe,EAAE,QAAU,CAAC1T,EAAO0T,CAAe,EAAE,SAChK1T,EAAO0T,CAAe,EAAE,KAAO,IAE7B,CAAC,aAAc,WAAW,EAAE,QAAQA,CAAe,GAAK,GAAK1T,EAAO0T,CAAe,GAAK1T,EAAO0T,CAAe,EAAE,SAAW,CAAC1T,EAAO0T,CAAe,EAAE,KACtJ1T,EAAO0T,CAAe,EAAE,KAAO,IAE7B,EAAEA,KAAmB1T,GAAU,YAAa2T,GAAe,CAC7Dhf,EAAO8e,EAAkB/e,CAAG,EAC5B,MACF,CACI,OAAOsL,EAAO0T,CAAe,GAAM,UAAY,EAAE,YAAa1T,EAAO0T,CAAe,KACtF1T,EAAO0T,CAAe,EAAE,QAAU,IAE/B1T,EAAO0T,CAAe,IAAG1T,EAAO0T,CAAe,EAAI,CACtD,QAAS,EACf,GACI/e,EAAO8e,EAAkB/e,CAAG,CAC9B,CACF,CAGA,MAAMkf,GAAa,CACjB,cAAAjV,GACA,OAAA6I,GACA,UAAA3D,GACA,WAAAmF,GACA,MAAAzH,GACA,KAAA4L,GACA,WAAAI,GACA,OAAQsD,GACR,YAAAI,GACA,cAAeqC,GACf,QAAA7d,EACF,EACMoe,GAAmB,CAAA,EACzB,MAAMC,CAAO,CACX,aAAc,CACZ,IAAI7d,EACA+J,EACJ,QAASb,EAAO,UAAU,OAAQC,EAAO,IAAI,MAAMD,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,CAAI,EAAI,UAAUA,CAAI,EAEzBD,EAAK,SAAW,GAAKA,EAAK,CAAC,EAAE,aAAe,OAAO,UAAU,SAAS,KAAKA,EAAK,CAAC,CAAC,EAAE,MAAM,EAAG,EAAE,IAAM,SACvGY,EAASZ,EAAK,CAAC,EAEf,CAACnJ,EAAI+J,CAAM,EAAIZ,EAEZY,IAAQA,EAAS,CAAA,GACtBA,EAASrL,EAAO,CAAA,EAAIqL,CAAM,EACtB/J,GAAM,CAAC+J,EAAO,KAAIA,EAAO,GAAK/J,GAClC,MAAM+E,EAAW/F,EAAW,EAC5B,GAAI+K,EAAO,IAAM,OAAOA,EAAO,IAAO,UAAYhF,EAAS,iBAAiBgF,EAAO,EAAE,EAAE,OAAS,EAAG,CACjG,MAAM+T,EAAU,CAAA,EAChB,OAAA/Y,EAAS,iBAAiBgF,EAAO,EAAE,EAAE,QAAQmS,GAAe,CAC1D,MAAM6B,EAAYrf,EAAO,CAAA,EAAIqL,EAAQ,CACnC,GAAImS,CACd,CAAS,EACD4B,EAAQ,KAAK,IAAID,EAAOE,CAAS,CAAC,CACpC,CAAC,EAEMD,CACT,CAGA,MAAMrc,EAAS,KACfA,EAAO,WAAa,GACpBA,EAAO,QAAUuD,GAAU,EAC3BvD,EAAO,OAASwE,GAAU,CACxB,UAAW8D,EAAO,SACxB,CAAK,EACDtI,EAAO,QAAUoF,GAAU,EAC3BpF,EAAO,gBAAkB,CAAA,EACzBA,EAAO,mBAAqB,CAAA,EAC5BA,EAAO,QAAU,CAAC,GAAGA,EAAO,WAAW,EACnCsI,EAAO,SAAW,MAAM,QAAQA,EAAO,OAAO,GAChDtI,EAAO,QAAQ,KAAK,GAAGsI,EAAO,OAAO,EAEvC,MAAMyT,EAAmB,CAAA,EACzB/b,EAAO,QAAQ,QAAQuc,GAAO,CAC5BA,EAAI,CACF,OAAAjU,EACA,OAAAtI,EACA,aAAc8b,GAAmBxT,EAAQyT,CAAgB,EACzD,GAAI/b,EAAO,GAAG,KAAKA,CAAM,EACzB,KAAMA,EAAO,KAAK,KAAKA,CAAM,EAC7B,IAAKA,EAAO,IAAI,KAAKA,CAAM,EAC3B,KAAMA,EAAO,KAAK,KAAKA,CAAM,CACrC,CAAO,CACH,CAAC,EAGD,MAAMwc,EAAevf,EAAO,GAAI4e,GAAUE,CAAgB,EAG1D,OAAA/b,EAAO,OAAS/C,EAAO,CAAA,EAAIuf,EAAcL,GAAkB7T,CAAM,EACjEtI,EAAO,eAAiB/C,EAAO,CAAA,EAAI+C,EAAO,MAAM,EAChDA,EAAO,aAAe/C,EAAO,CAAA,EAAIqL,CAAM,EAGnCtI,EAAO,QAAUA,EAAO,OAAO,IACjC,OAAO,KAAKA,EAAO,OAAO,EAAE,EAAE,QAAQyc,GAAa,CACjDzc,EAAO,GAAGyc,EAAWzc,EAAO,OAAO,GAAGyc,CAAS,CAAC,CAClD,CAAC,EAECzc,EAAO,QAAUA,EAAO,OAAO,OACjCA,EAAO,MAAMA,EAAO,OAAO,KAAK,EAIlC,OAAO,OAAOA,EAAQ,CACpB,QAASA,EAAO,OAAO,QACvB,GAAAzB,EAEA,WAAY,CAAA,EAEZ,OAAQ,CAAA,EACR,WAAY,CAAA,EACZ,SAAU,CAAA,EACV,gBAAiB,CAAA,EAEjB,cAAe,CACb,OAAOyB,EAAO,OAAO,YAAc,YACrC,EACA,YAAa,CACX,OAAOA,EAAO,OAAO,YAAc,UACrC,EAEA,YAAa,EACb,UAAW,EAEX,YAAa,GACb,MAAO,GAEP,UAAW,EACX,kBAAmB,EACnB,SAAU,EACV,SAAU,EACV,UAAW,GACX,uBAAwB,CAGtB,OAAO,KAAK,MAAM,KAAK,UAAY,GAAK,EAAE,EAAI,GAAK,EACrD,EAEA,eAAgBA,EAAO,OAAO,eAC9B,eAAgBA,EAAO,OAAO,eAE9B,gBAAiB,CACf,UAAW,OACX,QAAS,OACT,oBAAqB,OACrB,eAAgB,OAChB,YAAa,OACb,iBAAkB,OAClB,eAAgB,OAChB,mBAAoB,OAEpB,kBAAmBA,EAAO,OAAO,kBAEjC,cAAe,EACf,aAAc,OAEd,WAAY,CAAA,EACZ,oBAAqB,OACrB,YAAa,OACb,UAAW,KACX,QAAS,IACjB,EAEM,WAAY,GAEZ,eAAgBA,EAAO,OAAO,eAC9B,QAAS,CACP,OAAQ,EACR,OAAQ,EACR,SAAU,EACV,SAAU,EACV,KAAM,CACd,EAEM,aAAc,CAAA,EACd,aAAc,CACpB,CAAK,EACDA,EAAO,KAAK,SAAS,EAGjBA,EAAO,OAAO,MAChBA,EAAO,KAAI,EAKNA,CACT,CACA,kBAAkB0c,EAAU,CAC1B,OAAI,KAAK,eACAA,EAGF,CACL,MAAS,SACT,aAAc,cACd,iBAAkB,eAClB,cAAe,aACf,eAAgB,gBAChB,eAAgB,cAChB,gBAAiB,iBACjB,YAAe,cACrB,EAAMA,CAAQ,CACZ,CACA,cAAc3b,EAAS,CACrB,KAAM,CACJ,SAAAyH,EACA,OAAAF,CACN,EAAQ,KACEQ,EAAS9H,EAAgBwH,EAAU,IAAIF,EAAO,UAAU,gBAAgB,EACxEgF,EAAkB/K,GAAauG,EAAO,CAAC,CAAC,EAC9C,OAAOvG,GAAaxB,CAAO,EAAIuM,CACjC,CACA,oBAAoB1F,EAAO,CACzB,OAAO,KAAK,cAAc,KAAK,OAAO,KAAK7G,GAAWA,EAAQ,aAAa,yBAAyB,EAAI,IAAM6G,CAAK,CAAC,CACtH,CACA,sBAAsBA,EAAO,CAC3B,OAAI,KAAK,MAAQ,KAAK,OAAO,MAAQ,KAAK,OAAO,KAAK,KAAO,IACvD,KAAK,OAAO,KAAK,OAAS,SAC5BA,EAAQ,KAAK,MAAMA,EAAQ,KAAK,OAAO,KAAK,IAAI,EACvC,KAAK,OAAO,KAAK,OAAS,QACnCA,EAAQA,EAAQ,KAAK,KAAK,KAAK,OAAO,OAAS,KAAK,OAAO,KAAK,IAAI,IAGjEA,CACT,CACA,cAAe,CACb,MAAM5H,EAAS,KACT,CACJ,SAAAwI,EACA,OAAAF,CACN,EAAQtI,EACJA,EAAO,OAASgB,EAAgBwH,EAAU,IAAIF,EAAO,UAAU,gBAAgB,CACjF,CACA,QAAS,CACP,MAAMtI,EAAS,KACXA,EAAO,UACXA,EAAO,QAAU,GACbA,EAAO,OAAO,YAChBA,EAAO,cAAa,EAEtBA,EAAO,KAAK,QAAQ,EACtB,CACA,SAAU,CACR,MAAMA,EAAS,KACVA,EAAO,UACZA,EAAO,QAAU,GACbA,EAAO,OAAO,YAChBA,EAAO,gBAAe,EAExBA,EAAO,KAAK,SAAS,EACvB,CACA,YAAYW,EAAU+K,EAAO,CAC3B,MAAM1L,EAAS,KACfW,EAAW,KAAK,IAAI,KAAK,IAAIA,EAAU,CAAC,EAAG,CAAC,EAC5C,MAAMgc,EAAM3c,EAAO,aAAY,EAEzBS,GADMT,EAAO,aAAY,EACR2c,GAAOhc,EAAWgc,EACzC3c,EAAO,YAAYS,EAAS,OAAOiL,EAAU,IAAc,EAAIA,CAAK,EACpE1L,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,CAC5B,CACA,sBAAuB,CACrB,MAAMA,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,cAAgB,CAACA,EAAO,GAAI,OAC/C,MAAM4c,EAAM5c,EAAO,GAAG,UAAU,MAAM,GAAG,EAAE,OAAOiM,GACzCA,EAAU,QAAQ,QAAQ,IAAM,GAAKA,EAAU,QAAQjM,EAAO,OAAO,sBAAsB,IAAM,CACzG,EACDA,EAAO,KAAK,oBAAqB4c,EAAI,KAAK,GAAG,CAAC,CAChD,CACA,gBAAgB7b,EAAS,CACvB,MAAMf,EAAS,KACf,OAAIA,EAAO,UAAkB,GACtBe,EAAQ,UAAU,MAAM,GAAG,EAAE,OAAOkL,GAClCA,EAAU,QAAQ,cAAc,IAAM,GAAKA,EAAU,QAAQjM,EAAO,OAAO,UAAU,IAAM,CACnG,EAAE,KAAK,GAAG,CACb,CACA,mBAAoB,CAClB,MAAMA,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,cAAgB,CAACA,EAAO,GAAI,OAC/C,MAAM6c,EAAU,CAAA,EAChB7c,EAAO,OAAO,QAAQe,GAAW,CAC/B,MAAMqa,EAAapb,EAAO,gBAAgBe,CAAO,EACjD8b,EAAQ,KAAK,CACX,QAAA9b,EACA,WAAAqa,CACR,CAAO,EACDpb,EAAO,KAAK,cAAee,EAASqa,CAAU,CAChD,CAAC,EACDpb,EAAO,KAAK,gBAAiB6c,CAAO,CACtC,CACA,qBAAqBC,EAAMC,EAAO,CAC5BD,IAAS,SACXA,EAAO,WAELC,IAAU,SACZA,EAAQ,IAEV,MAAM/c,EAAS,KACT,CACJ,OAAAsI,EACA,OAAAQ,EACA,WAAAG,EACA,gBAAAC,EACA,KAAMT,EACN,YAAAqF,CACN,EAAQ9N,EACJ,IAAIgd,EAAM,EACV,GAAI,OAAO1U,EAAO,eAAkB,SAAU,OAAOA,EAAO,cAC5D,GAAIA,EAAO,eAAgB,CACzB,IAAIqB,EAAYb,EAAOgF,CAAW,EAAI,KAAK,KAAKhF,EAAOgF,CAAW,EAAE,eAAe,EAAI,EACnFmP,EACJ,QAAS7d,EAAI0O,EAAc,EAAG1O,EAAI0J,EAAO,OAAQ1J,GAAK,EAChD0J,EAAO1J,CAAC,GAAK,CAAC6d,IAChBtT,GAAa,KAAK,KAAKb,EAAO1J,CAAC,EAAE,eAAe,EAChD4d,GAAO,EACHrT,EAAYlB,IAAYwU,EAAY,KAG5C,QAAS7d,EAAI0O,EAAc,EAAG1O,GAAK,EAAGA,GAAK,EACrC0J,EAAO1J,CAAC,GAAK,CAAC6d,IAChBtT,GAAab,EAAO1J,CAAC,EAAE,gBACvB4d,GAAO,EACHrT,EAAYlB,IAAYwU,EAAY,IAG9C,SAEMH,IAAS,UACX,QAAS1d,EAAI0O,EAAc,EAAG1O,EAAI0J,EAAO,OAAQ1J,GAAK,GAChC2d,EAAQ9T,EAAW7J,CAAC,EAAI8J,EAAgB9J,CAAC,EAAI6J,EAAW6E,CAAW,EAAIrF,EAAaQ,EAAW7J,CAAC,EAAI6J,EAAW6E,CAAW,EAAIrF,KAEhJuU,GAAO,OAKX,SAAS5d,EAAI0O,EAAc,EAAG1O,GAAK,EAAGA,GAAK,EACrB6J,EAAW6E,CAAW,EAAI7E,EAAW7J,CAAC,EAAIqJ,IAE5DuU,GAAO,GAKf,OAAOA,CACT,CACA,QAAS,CACP,MAAMhd,EAAS,KACf,GAAI,CAACA,GAAUA,EAAO,UAAW,OACjC,KAAM,CACJ,SAAAgJ,EACA,OAAAV,CACN,EAAQtI,EAEAsI,EAAO,aACTtI,EAAO,cAAa,EAEtB,CAAC,GAAGA,EAAO,GAAG,iBAAiB,kBAAkB,CAAC,EAAE,QAAQoO,GAAW,CACjEA,EAAQ,UACVD,GAAqBnO,EAAQoO,CAAO,CAExC,CAAC,EACDpO,EAAO,WAAU,EACjBA,EAAO,aAAY,EACnBA,EAAO,eAAc,EACrBA,EAAO,oBAAmB,EAC1B,SAASiQ,GAAe,CACtB,MAAMiN,EAAiBld,EAAO,aAAeA,EAAO,UAAY,GAAKA,EAAO,UACtE6Q,EAAe,KAAK,IAAI,KAAK,IAAIqM,EAAgBld,EAAO,aAAY,CAAE,EAAGA,EAAO,aAAY,CAAE,EACpGA,EAAO,aAAa6Q,CAAY,EAChC7Q,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,CAC5B,CACA,IAAImd,EACJ,GAAI7U,EAAO,UAAYA,EAAO,SAAS,SAAW,CAACA,EAAO,QACxD2H,EAAY,EACR3H,EAAO,YACTtI,EAAO,iBAAgB,MAEpB,CACL,IAAKsI,EAAO,gBAAkB,QAAUA,EAAO,cAAgB,IAAMtI,EAAO,OAAS,CAACsI,EAAO,eAAgB,CAC3G,MAAMQ,EAAS9I,EAAO,SAAWsI,EAAO,QAAQ,QAAUtI,EAAO,QAAQ,OAASA,EAAO,OACzFmd,EAAand,EAAO,QAAQ8I,EAAO,OAAS,EAAG,EAAG,GAAO,EAAI,CAC/D,MACEqU,EAAand,EAAO,QAAQA,EAAO,YAAa,EAAG,GAAO,EAAI,EAE3Dmd,GACHlN,EAAY,CAEhB,CACI3H,EAAO,eAAiBU,IAAahJ,EAAO,UAC9CA,EAAO,cAAa,EAEtBA,EAAO,KAAK,QAAQ,CACtB,CACA,gBAAgBod,EAAcC,EAAY,CACpCA,IAAe,SACjBA,EAAa,IAEf,MAAMrd,EAAS,KACTsd,EAAmBtd,EAAO,OAAO,UAKvC,OAJKod,IAEHA,EAAeE,IAAqB,aAAe,WAAa,cAE9DF,IAAiBE,GAAoBF,IAAiB,cAAgBA,IAAiB,aAG3Fpd,EAAO,GAAG,UAAU,OAAO,GAAGA,EAAO,OAAO,sBAAsB,GAAGsd,CAAgB,EAAE,EACvFtd,EAAO,GAAG,UAAU,IAAI,GAAGA,EAAO,OAAO,sBAAsB,GAAGod,CAAY,EAAE,EAChFpd,EAAO,qBAAoB,EAC3BA,EAAO,OAAO,UAAYod,EAC1Bpd,EAAO,OAAO,QAAQe,GAAW,CAC3Bqc,IAAiB,WACnBrc,EAAQ,MAAM,MAAQ,GAEtBA,EAAQ,MAAM,OAAS,EAE3B,CAAC,EACDf,EAAO,KAAK,iBAAiB,EACzBqd,GAAYrd,EAAO,OAAM,GACtBA,CACT,CACA,wBAAwBkR,EAAW,CACjC,MAAMlR,EAAS,KACXA,EAAO,KAAOkR,IAAc,OAAS,CAAClR,EAAO,KAAOkR,IAAc,QACtElR,EAAO,IAAMkR,IAAc,MAC3BlR,EAAO,aAAeA,EAAO,OAAO,YAAc,cAAgBA,EAAO,IACrEA,EAAO,KACTA,EAAO,GAAG,UAAU,IAAI,GAAGA,EAAO,OAAO,sBAAsB,KAAK,EACpEA,EAAO,GAAG,IAAM,QAEhBA,EAAO,GAAG,UAAU,OAAO,GAAGA,EAAO,OAAO,sBAAsB,KAAK,EACvEA,EAAO,GAAG,IAAM,OAElBA,EAAO,OAAM,EACf,CACA,MAAMiB,EAAS,CACb,MAAMjB,EAAS,KACf,GAAIA,EAAO,QAAS,MAAO,GAG3B,IAAIzB,EAAK0C,GAAWjB,EAAO,OAAO,GAIlC,GAHI,OAAOzB,GAAO,WAChBA,EAAK,SAAS,cAAcA,CAAE,GAE5B,CAACA,EACH,MAAO,GAETA,EAAG,OAASyB,EACRzB,EAAG,YAAcA,EAAG,WAAW,MAAQA,EAAG,WAAW,KAAK,WAAayB,EAAO,OAAO,sBAAsB,YAAW,IACxHA,EAAO,UAAY,IAErB,MAAMud,EAAqB,IAClB,KAAKvd,EAAO,OAAO,cAAgB,IAAI,KAAI,EAAG,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC,GAW3E,IAAIuI,EAREhK,GAAMA,EAAG,YAAcA,EAAG,WAAW,cAC3BA,EAAG,WAAW,cAAcgf,EAAkB,CAAE,EAIvDvc,EAAgBzC,EAAIgf,EAAkB,CAAE,EAAE,CAAC,EAIpD,MAAI,CAAChV,GAAavI,EAAO,OAAO,iBAC9BuI,EAAY1G,GAAc,MAAO7B,EAAO,OAAO,YAAY,EAC3DzB,EAAG,OAAOgK,CAAS,EACnBvH,EAAgBzC,EAAI,IAAIyB,EAAO,OAAO,UAAU,EAAE,EAAE,QAAQe,GAAW,CACrEwH,EAAU,OAAOxH,CAAO,CAC1B,CAAC,GAEH,OAAO,OAAOf,EAAQ,CACpB,GAAAzB,EACA,UAAAgK,EACA,SAAUvI,EAAO,WAAa,CAACzB,EAAG,WAAW,KAAK,WAAaA,EAAG,WAAW,KAAOgK,EACpF,OAAQvI,EAAO,UAAYzB,EAAG,WAAW,KAAOA,EAChD,QAAS,GAET,IAAKA,EAAG,IAAI,YAAW,IAAO,OAAS8D,EAAa9D,EAAI,WAAW,IAAM,MACzE,aAAcyB,EAAO,OAAO,YAAc,eAAiBzB,EAAG,IAAI,YAAW,IAAO,OAAS8D,EAAa9D,EAAI,WAAW,IAAM,OAC/H,SAAU8D,EAAakG,EAAW,SAAS,IAAM,aACvD,CAAK,EACM,EACT,CACA,KAAKhK,EAAI,CACP,MAAMyB,EAAS,KAGf,GAFIA,EAAO,aACKA,EAAO,MAAMzB,CAAE,IACf,GAAO,OAAOyB,EAC9BA,EAAO,KAAK,YAAY,EAGpBA,EAAO,OAAO,aAChBA,EAAO,cAAa,EAItBA,EAAO,WAAU,EAGjBA,EAAO,WAAU,EAGjBA,EAAO,aAAY,EACfA,EAAO,OAAO,eAChBA,EAAO,cAAa,EAIlBA,EAAO,OAAO,YAAcA,EAAO,SACrCA,EAAO,cAAa,EAIlBA,EAAO,OAAO,MAAQA,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAChEA,EAAO,QAAQA,EAAO,OAAO,aAAeA,EAAO,QAAQ,aAAc,EAAGA,EAAO,OAAO,mBAAoB,GAAO,EAAI,EAEzHA,EAAO,QAAQA,EAAO,OAAO,aAAc,EAAGA,EAAO,OAAO,mBAAoB,GAAO,EAAI,EAIzFA,EAAO,OAAO,MAChBA,EAAO,WAAW,OAAW,EAAI,EAInCA,EAAO,aAAY,EACnB,MAAMwd,EAAe,CAAC,GAAGxd,EAAO,GAAG,iBAAiB,kBAAkB,CAAC,EACvE,OAAIA,EAAO,WACTwd,EAAa,KAAK,GAAGxd,EAAO,OAAO,iBAAiB,kBAAkB,CAAC,EAEzEwd,EAAa,QAAQpP,GAAW,CAC1BA,EAAQ,SACVD,GAAqBnO,EAAQoO,CAAO,EAEpCA,EAAQ,iBAAiB,OAAQ2C,GAAK,CACpC5C,GAAqBnO,EAAQ+Q,EAAE,MAAM,CACvC,CAAC,CAEL,CAAC,EACDvC,GAAQxO,CAAM,EAGdA,EAAO,YAAc,GACrBwO,GAAQxO,CAAM,EAGdA,EAAO,KAAK,MAAM,EAClBA,EAAO,KAAK,WAAW,EAChBA,CACT,CACA,QAAQyd,EAAgBC,EAAa,CAC/BD,IAAmB,SACrBA,EAAiB,IAEfC,IAAgB,SAClBA,EAAc,IAEhB,MAAM1d,EAAS,KACT,CACJ,OAAAsI,EACA,GAAA/J,EACA,UAAAgK,EACA,OAAAO,CACN,EAAQ9I,EACJ,OAAI,OAAOA,EAAO,OAAW,KAAeA,EAAO,YAGnDA,EAAO,KAAK,eAAe,EAG3BA,EAAO,YAAc,GAGrBA,EAAO,aAAY,EAGfsI,EAAO,MACTtI,EAAO,YAAW,EAIhB0d,IACF1d,EAAO,cAAa,EAChBzB,GAAM,OAAOA,GAAO,UACtBA,EAAG,gBAAgB,OAAO,EAExBgK,GACFA,EAAU,gBAAgB,OAAO,EAE/BO,GAAUA,EAAO,QACnBA,EAAO,QAAQ/H,GAAW,CACxBA,EAAQ,UAAU,OAAOuH,EAAO,kBAAmBA,EAAO,uBAAwBA,EAAO,iBAAkBA,EAAO,eAAgBA,EAAO,cAAc,EACvJvH,EAAQ,gBAAgB,OAAO,EAC/BA,EAAQ,gBAAgB,yBAAyB,CACnD,CAAC,GAGLf,EAAO,KAAK,SAAS,EAGrB,OAAO,KAAKA,EAAO,eAAe,EAAE,QAAQyc,GAAa,CACvDzc,EAAO,IAAIyc,CAAS,CACtB,CAAC,EACGgB,IAAmB,KACjBzd,EAAO,IAAM,OAAOA,EAAO,IAAO,WACpCA,EAAO,GAAG,OAAS,MAErB/B,GAAY+B,CAAM,GAEpBA,EAAO,UAAY,IACZ,IACT,CACA,OAAO,eAAe2d,EAAa,CACjC1gB,EAAOkf,GAAkBwB,CAAW,CACtC,CACA,WAAW,kBAAmB,CAC5B,OAAOxB,EACT,CACA,WAAW,UAAW,CACpB,OAAON,EACT,CACA,OAAO,cAAcU,EAAK,CACnBH,EAAO,UAAU,cAAaA,EAAO,UAAU,YAAc,CAAA,GAClE,MAAMwB,EAAUxB,EAAO,UAAU,YAC7B,OAAOG,GAAQ,YAAcqB,EAAQ,QAAQrB,CAAG,EAAI,GACtDqB,EAAQ,KAAKrB,CAAG,CAEpB,CACA,OAAO,IAAIsB,EAAQ,CACjB,OAAI,MAAM,QAAQA,CAAM,GACtBA,EAAO,QAAQC,GAAK1B,EAAO,cAAc0B,CAAC,CAAC,EACpC1B,IAETA,EAAO,cAAcyB,CAAM,EACpBzB,EACT,CACF,CACA,OAAO,KAAKF,EAAU,EAAE,QAAQ6B,GAAkB,CAChD,OAAO,KAAK7B,GAAW6B,CAAc,CAAC,EAAE,QAAQC,GAAe,CAC7D5B,EAAO,UAAU4B,CAAW,EAAI9B,GAAW6B,CAAc,EAAEC,CAAW,CACxE,CAAC,CACH,CAAC,EACD5B,EAAO,IAAI,CAAC/W,GAAQiB,EAAQ,CAAC,EC32H7B,SAAS2X,GAA0Bje,EAAQke,EAAgB5V,EAAQ6V,EAAY,CAC7E,OAAIne,EAAO,OAAO,gBAChB,OAAO,KAAKme,CAAU,EAAE,QAAQ9gB,GAAO,CACrC,GAAI,CAACiL,EAAOjL,CAAG,GAAKiL,EAAO,OAAS,GAAM,CACxC,IAAIrH,EAAUD,EAAgBhB,EAAO,GAAI,IAAIme,EAAW9gB,CAAG,CAAC,EAAE,EAAE,CAAC,EAC5D4D,IACHA,EAAUY,GAAc,MAAOsc,EAAW9gB,CAAG,CAAC,EAC9C4D,EAAQ,UAAYkd,EAAW9gB,CAAG,EAClC2C,EAAO,GAAG,OAAOiB,CAAO,GAE1BqH,EAAOjL,CAAG,EAAI4D,EACdid,EAAe7gB,CAAG,EAAI4D,CACxB,CACF,CAAC,EAEIqH,CACT,CCfA,SAAS8V,GAAWre,EAAM,CACxB,GAAI,CACF,OAAAC,EACA,aAAAuG,EACA,GAAAjB,EACA,KAAAC,CACJ,EAAMxF,EACJwG,EAAa,CACX,WAAY,CACV,OAAQ,KACR,OAAQ,KACR,YAAa,GACb,cAAe,yBACf,YAAa,uBACb,UAAW,qBACX,wBAAyB,4BAC/B,CACA,CAAG,EACDvG,EAAO,WAAa,CAClB,OAAQ,KACR,OAAQ,IACZ,EACE,SAASqe,EAAM9f,EAAI,CACjB,IAAI+f,EACJ,OAAI/f,GAAM,OAAOA,GAAO,UAAYyB,EAAO,YACzCse,EAAMte,EAAO,GAAG,cAAczB,CAAE,GAAKyB,EAAO,OAAO,cAAczB,CAAE,EAC/D+f,GAAYA,GAEd/f,IACE,OAAOA,GAAO,WAAU+f,EAAM,CAAC,GAAG,SAAS,iBAAiB/f,CAAE,CAAC,GAC/DyB,EAAO,OAAO,mBAAqB,OAAOzB,GAAO,UAAY+f,GAAOA,EAAI,OAAS,GAAKte,EAAO,GAAG,iBAAiBzB,CAAE,EAAE,SAAW,EAClI+f,EAAMte,EAAO,GAAG,cAAczB,CAAE,EACvB+f,GAAOA,EAAI,SAAW,IAC/BA,EAAMA,EAAI,CAAC,IAGX/f,GAAM,CAAC+f,EAAY/f,EAEhB+f,EACT,CACA,SAASC,EAAShgB,EAAIigB,EAAU,CAC9B,MAAMlW,EAAStI,EAAO,OAAO,WAC7BzB,EAAKuE,EAAkBvE,CAAE,EACzBA,EAAG,QAAQkgB,GAAS,CACdA,IACFA,EAAM,UAAUD,EAAW,MAAQ,QAAQ,EAAE,GAAGlW,EAAO,cAAc,MAAM,GAAG,CAAC,EAC3EmW,EAAM,UAAY,WAAUA,EAAM,SAAWD,GAC7Cxe,EAAO,OAAO,eAAiBA,EAAO,SACxCye,EAAM,UAAUze,EAAO,SAAW,MAAQ,QAAQ,EAAEsI,EAAO,SAAS,EAG1E,CAAC,CACH,CACA,SAASwH,GAAS,CAEhB,KAAM,CACJ,OAAA4O,EACA,OAAAC,CACN,EAAQ3e,EAAO,WACX,GAAIA,EAAO,OAAO,KAAM,CACtBue,EAASI,EAAQ,EAAK,EACtBJ,EAASG,EAAQ,EAAK,EACtB,MACF,CACAH,EAASI,EAAQ3e,EAAO,aAAe,CAACA,EAAO,OAAO,MAAM,EAC5Due,EAASG,EAAQ1e,EAAO,OAAS,CAACA,EAAO,OAAO,MAAM,CACxD,CACA,SAAS4e,EAAY7N,EAAG,CACtBA,EAAE,eAAc,EACZ,EAAA/Q,EAAO,aAAe,CAACA,EAAO,OAAO,MAAQ,CAACA,EAAO,OAAO,UAChEA,EAAO,UAAS,EAChBuF,EAAK,gBAAgB,EACvB,CACA,SAASsZ,EAAY9N,EAAG,CACtBA,EAAE,eAAc,EACZ,EAAA/Q,EAAO,OAAS,CAACA,EAAO,OAAO,MAAQ,CAACA,EAAO,OAAO,UAC1DA,EAAO,UAAS,EAChBuF,EAAK,gBAAgB,EACvB,CACA,SAASuB,GAAO,CACd,MAAMwB,EAAStI,EAAO,OAAO,WAK7B,GAJAA,EAAO,OAAO,WAAaie,GAA0Bje,EAAQA,EAAO,eAAe,WAAYA,EAAO,OAAO,WAAY,CACvH,OAAQ,qBACR,OAAQ,oBACd,CAAK,EACG,EAAEsI,EAAO,QAAUA,EAAO,QAAS,OACvC,IAAIoW,EAASL,EAAM/V,EAAO,MAAM,EAC5BqW,EAASN,EAAM/V,EAAO,MAAM,EAChC,OAAO,OAAOtI,EAAO,WAAY,CAC/B,OAAA0e,EACA,OAAAC,CACN,CAAK,EACDD,EAAS5b,EAAkB4b,CAAM,EACjCC,EAAS7b,EAAkB6b,CAAM,EACjC,MAAMG,EAAa,CAACvgB,EAAIgC,IAAQ,CAC1BhC,GACFA,EAAG,iBAAiB,QAASgC,IAAQ,OAASse,EAAcD,CAAW,EAErE,CAAC5e,EAAO,SAAWzB,GACrBA,EAAG,UAAU,IAAI,GAAG+J,EAAO,UAAU,MAAM,GAAG,CAAC,CAEnD,EACAoW,EAAO,QAAQngB,GAAMugB,EAAWvgB,EAAI,MAAM,CAAC,EAC3CogB,EAAO,QAAQpgB,GAAMugB,EAAWvgB,EAAI,MAAM,CAAC,CAC7C,CACA,SAASyI,GAAU,CACjB,GAAI,CACF,OAAA0X,EACA,OAAAC,CACN,EAAQ3e,EAAO,WACX0e,EAAS5b,EAAkB4b,CAAM,EACjCC,EAAS7b,EAAkB6b,CAAM,EACjC,MAAMI,EAAgB,CAACxgB,EAAIgC,IAAQ,CACjChC,EAAG,oBAAoB,QAASgC,IAAQ,OAASse,EAAcD,CAAW,EAC1ErgB,EAAG,UAAU,OAAO,GAAGyB,EAAO,OAAO,WAAW,cAAc,MAAM,GAAG,CAAC,CAC1E,EACA0e,EAAO,QAAQngB,GAAMwgB,EAAcxgB,EAAI,MAAM,CAAC,EAC9CogB,EAAO,QAAQpgB,GAAMwgB,EAAcxgB,EAAI,MAAM,CAAC,CAChD,CACA+G,EAAG,OAAQ,IAAM,CACXtF,EAAO,OAAO,WAAW,UAAY,GAEvCgf,EAAO,GAEPlY,EAAI,EACJgJ,EAAM,EAEV,CAAC,EACDxK,EAAG,8BAA+B,IAAM,CACtCwK,EAAM,CACR,CAAC,EACDxK,EAAG,UAAW,IAAM,CAClB0B,EAAO,CACT,CAAC,EACD1B,EAAG,iBAAkB,IAAM,CACzB,GAAI,CACF,OAAAoZ,EACA,OAAAC,CACN,EAAQ3e,EAAO,WAGX,GAFA0e,EAAS5b,EAAkB4b,CAAM,EACjCC,EAAS7b,EAAkB6b,CAAM,EAC7B3e,EAAO,QAAS,CAClB8P,EAAM,EACN,MACF,CACA,CAAC,GAAG4O,EAAQ,GAAGC,CAAM,EAAE,OAAOpgB,GAAM,CAAC,CAACA,CAAE,EAAE,QAAQA,GAAMA,EAAG,UAAU,IAAIyB,EAAO,OAAO,WAAW,SAAS,CAAC,CAC9G,CAAC,EACDsF,EAAG,QAAS,CAAC2Z,EAAIlO,IAAM,CACrB,GAAI,CACF,OAAA2N,EACA,OAAAC,CACN,EAAQ3e,EAAO,WACX0e,EAAS5b,EAAkB4b,CAAM,EACjCC,EAAS7b,EAAkB6b,CAAM,EACjC,MAAMnI,EAAWzF,EAAE,OACnB,IAAImO,EAAiBP,EAAO,SAASnI,CAAQ,GAAKkI,EAAO,SAASlI,CAAQ,EAC1E,GAAIxW,EAAO,WAAa,CAACkf,EAAgB,CACvC,MAAMvP,EAAOoB,EAAE,MAAQA,EAAE,cAAgBA,EAAE,aAAY,EACnDpB,IACFuP,EAAiBvP,EAAK,KAAKC,GAAU8O,EAAO,SAAS9O,CAAM,GAAK+O,EAAO,SAAS/O,CAAM,CAAC,EAE3F,CACA,GAAI5P,EAAO,OAAO,WAAW,aAAe,CAACkf,EAAgB,CAC3D,GAAIlf,EAAO,YAAcA,EAAO,OAAO,YAAcA,EAAO,OAAO,WAAW,YAAcA,EAAO,WAAW,KAAOwW,GAAYxW,EAAO,WAAW,GAAG,SAASwW,CAAQ,GAAI,OAC3K,IAAI2I,EACAT,EAAO,OACTS,EAAWT,EAAO,CAAC,EAAE,UAAU,SAAS1e,EAAO,OAAO,WAAW,WAAW,EACnE2e,EAAO,SAChBQ,EAAWR,EAAO,CAAC,EAAE,UAAU,SAAS3e,EAAO,OAAO,WAAW,WAAW,GAG5EuF,EADE4Z,IAAa,GACV,iBAEA,gBAFgB,EAIvB,CAAC,GAAGT,EAAQ,GAAGC,CAAM,EAAE,OAAOpgB,GAAM,CAAC,CAACA,CAAE,EAAE,QAAQA,GAAMA,EAAG,UAAU,OAAOyB,EAAO,OAAO,WAAW,WAAW,CAAC,CACnH,CACF,CAAC,EACD,MAAMof,EAAS,IAAM,CACnBpf,EAAO,GAAG,UAAU,OAAO,GAAGA,EAAO,OAAO,WAAW,wBAAwB,MAAM,GAAG,CAAC,EACzF8G,EAAI,EACJgJ,EAAM,CACR,EACMkP,EAAU,IAAM,CACpBhf,EAAO,GAAG,UAAU,IAAI,GAAGA,EAAO,OAAO,WAAW,wBAAwB,MAAM,GAAG,CAAC,EACtFgH,EAAO,CACT,EACA,OAAO,OAAOhH,EAAO,WAAY,CAC/B,OAAAof,EACA,QAAAJ,EACA,OAAAlP,EACA,KAAAhJ,EACA,QAAAE,CACJ,CAAG,CACH,CCrMA,SAASqY,GAAkBthB,EAAS,CAClC,OAAIA,IAAY,SACdA,EAAU,IAEL,IAAIA,EAAQ,KAAI,EAAG,QAAQ,oBAAqB,MAAM,EAC5D,QAAQ,KAAM,GAAG,CAAC,EACrB,CCFA,SAASuhB,GAAWvf,EAAM,CACxB,GAAI,CACF,OAAAC,EACA,aAAAuG,EACA,GAAAjB,EACA,KAAAC,CACJ,EAAMxF,EACJ,MAAMwf,EAAM,oBACZhZ,EAAa,CACX,WAAY,CACV,GAAI,KACJ,cAAe,OACf,UAAW,GACX,YAAa,GACb,aAAc,KACd,kBAAmB,KACnB,eAAgB,KAChB,aAAc,KACd,oBAAqB,GACrB,KAAM,UAEN,eAAgB,GAChB,mBAAoB,EACpB,sBAAuBiZ,GAAUA,EACjC,oBAAqBA,GAAUA,EAC/B,YAAa,GAAGD,CAAG,UACnB,kBAAmB,GAAGA,CAAG,iBACzB,cAAe,GAAGA,CAAG,IACrB,aAAc,GAAGA,CAAG,WACpB,WAAY,GAAGA,CAAG,SAClB,YAAa,GAAGA,CAAG,UACnB,qBAAsB,GAAGA,CAAG,oBAC5B,yBAA0B,GAAGA,CAAG,wBAChC,eAAgB,GAAGA,CAAG,aACtB,UAAW,GAAGA,CAAG,QACjB,gBAAiB,GAAGA,CAAG,cACvB,cAAe,GAAGA,CAAG,YACrB,wBAAyB,GAAGA,CAAG,WACrC,CACA,CAAG,EACDvf,EAAO,WAAa,CAClB,GAAI,KACJ,QAAS,CAAA,CACb,EACE,IAAIyf,EACAC,EAAqB,EACzB,SAASC,GAAuB,CAC9B,MAAO,CAAC3f,EAAO,OAAO,WAAW,IAAM,CAACA,EAAO,WAAW,IAAM,MAAM,QAAQA,EAAO,WAAW,EAAE,GAAKA,EAAO,WAAW,GAAG,SAAW,CACzI,CACA,SAAS4f,EAAeC,EAAUC,EAAU,CAC1C,KAAM,CACJ,kBAAAC,CACN,EAAQ/f,EAAO,OAAO,WACb6f,IACLA,EAAWA,EAAS,GAAGC,IAAa,OAAS,WAAa,MAAM,gBAAgB,EAC5ED,IACFA,EAAS,UAAU,IAAI,GAAGE,CAAiB,IAAID,CAAQ,EAAE,EACzDD,EAAWA,EAAS,GAAGC,IAAa,OAAS,WAAa,MAAM,gBAAgB,EAC5ED,GACFA,EAAS,UAAU,IAAI,GAAGE,CAAiB,IAAID,CAAQ,IAAIA,CAAQ,EAAE,GAG3E,CACA,SAASE,EAAiBhN,EAAWzT,EAAW0gB,EAAQ,CAGtD,GAFAjN,EAAYA,EAAYiN,EACxB1gB,EAAYA,EAAY0gB,EACpB1gB,IAAcyT,EAAY,EAC5B,MAAO,OACF,GAAIzT,IAAcyT,EAAY,EACnC,MAAO,UAGX,CACA,SAASkN,EAAcnP,EAAG,CACxB,MAAM8O,EAAW9O,EAAE,OAAO,QAAQsO,GAAkBrf,EAAO,OAAO,WAAW,WAAW,CAAC,EACzF,GAAI,CAAC6f,EACH,OAEF9O,EAAE,eAAc,EAChB,MAAMnJ,EAAQrF,GAAasd,CAAQ,EAAI7f,EAAO,OAAO,eACrD,GAAIA,EAAO,OAAO,KAAM,CACtB,GAAIA,EAAO,YAAc4H,EAAO,OAChC,MAAMuY,EAAgBH,EAAiBhgB,EAAO,UAAW4H,EAAO5H,EAAO,OAAO,MAAM,EAChFmgB,IAAkB,OACpBngB,EAAO,UAAS,EACPmgB,IAAkB,WAC3BngB,EAAO,UAAS,EAEhBA,EAAO,YAAY4H,CAAK,CAE5B,MACE5H,EAAO,QAAQ4H,CAAK,CAExB,CACA,SAASkI,GAAS,CAEhB,MAAMpH,EAAM1I,EAAO,IACbsI,EAAStI,EAAO,OAAO,WAC7B,GAAI2f,EAAoB,EAAI,OAC5B,IAAIphB,EAAKyB,EAAO,WAAW,GAC3BzB,EAAKuE,EAAkBvE,CAAE,EAEzB,IAAIkC,EACAyO,EACJ,MAAMnG,EAAe/I,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAUA,EAAO,QAAQ,OAAO,OAASA,EAAO,OAAO,OAC9GogB,EAAQpgB,EAAO,OAAO,KAAO,KAAK,KAAK+I,EAAe/I,EAAO,OAAO,cAAc,EAAIA,EAAO,SAAS,OAY5G,GAXIA,EAAO,OAAO,MAChBkP,EAAgBlP,EAAO,mBAAqB,EAC5CS,EAAUT,EAAO,OAAO,eAAiB,EAAI,KAAK,MAAMA,EAAO,UAAYA,EAAO,OAAO,cAAc,EAAIA,EAAO,WACzG,OAAOA,EAAO,UAAc,KACrCS,EAAUT,EAAO,UACjBkP,EAAgBlP,EAAO,oBAEvBkP,EAAgBlP,EAAO,eAAiB,EACxCS,EAAUT,EAAO,aAAe,GAG9BsI,EAAO,OAAS,WAAatI,EAAO,WAAW,SAAWA,EAAO,WAAW,QAAQ,OAAS,EAAG,CAClG,MAAMqgB,EAAUrgB,EAAO,WAAW,QAClC,IAAIsgB,EACArN,EACAsN,EAsBJ,GArBIjY,EAAO,iBACTmX,EAAa9c,GAAiB0d,EAAQ,CAAC,EAAGrgB,EAAO,aAAY,EAAK,QAAU,QAAc,EAC1FzB,EAAG,QAAQkgB,GAAS,CAClBA,EAAM,MAAMze,EAAO,aAAY,EAAK,QAAU,QAAQ,EAAI,GAAGyf,GAAcnX,EAAO,mBAAqB,EAAE,IAC3G,CAAC,EACGA,EAAO,mBAAqB,GAAK4G,IAAkB,SACrDwQ,GAAsBjf,GAAWyO,GAAiB,GAC9CwQ,EAAqBpX,EAAO,mBAAqB,EACnDoX,EAAqBpX,EAAO,mBAAqB,EACxCoX,EAAqB,IAC9BA,EAAqB,IAGzBY,EAAa,KAAK,IAAI7f,EAAUif,EAAoB,CAAC,EACrDzM,EAAYqN,GAAc,KAAK,IAAID,EAAQ,OAAQ/X,EAAO,kBAAkB,EAAI,GAChFiY,GAAYtN,EAAYqN,GAAc,GAExCD,EAAQ,QAAQR,GAAY,CAC1B,MAAMW,EAAkB,CAAC,GAAG,CAAC,GAAI,QAAS,aAAc,QAAS,aAAc,OAAO,EAAE,IAAIC,GAAU,GAAGnY,EAAO,iBAAiB,GAAGmY,CAAM,EAAE,CAAC,EAAE,IAAItd,GAAK,OAAOA,GAAM,UAAYA,EAAE,SAAS,GAAG,EAAIA,EAAE,MAAM,GAAG,EAAIA,CAAC,EAAE,KAAI,EACzN0c,EAAS,UAAU,OAAO,GAAGW,CAAe,CAC9C,CAAC,EACGjiB,EAAG,OAAS,EACd8hB,EAAQ,QAAQK,GAAU,CACxB,MAAMC,EAAcpe,GAAame,CAAM,EACnCC,IAAgBlgB,EAClBigB,EAAO,UAAU,IAAI,GAAGpY,EAAO,kBAAkB,MAAM,GAAG,CAAC,EAClDtI,EAAO,WAChB0gB,EAAO,aAAa,OAAQ,QAAQ,EAElCpY,EAAO,iBACLqY,GAAeL,GAAcK,GAAe1N,GAC9CyN,EAAO,UAAU,IAAI,GAAG,GAAGpY,EAAO,iBAAiB,QAAQ,MAAM,GAAG,CAAC,EAEnEqY,IAAgBL,GAClBV,EAAec,EAAQ,MAAM,EAE3BC,IAAgB1N,GAClB2M,EAAec,EAAQ,MAAM,EAGnC,CAAC,MACI,CACL,MAAMA,EAASL,EAAQ5f,CAAO,EAS9B,GARIigB,GACFA,EAAO,UAAU,IAAI,GAAGpY,EAAO,kBAAkB,MAAM,GAAG,CAAC,EAEzDtI,EAAO,WACTqgB,EAAQ,QAAQ,CAACR,EAAUc,IAAgB,CACzCd,EAAS,aAAa,OAAQc,IAAgBlgB,EAAU,gBAAkB,QAAQ,CACpF,CAAC,EAEC6H,EAAO,eAAgB,CACzB,MAAMsY,EAAuBP,EAAQC,CAAU,EACzCO,EAAsBR,EAAQpN,CAAS,EAC7C,QAAS7T,EAAIkhB,EAAYlhB,GAAK6T,EAAW7T,GAAK,EACxCihB,EAAQjhB,CAAC,GACXihB,EAAQjhB,CAAC,EAAE,UAAU,IAAI,GAAG,GAAGkJ,EAAO,iBAAiB,QAAQ,MAAM,GAAG,CAAC,EAG7EsX,EAAegB,EAAsB,MAAM,EAC3ChB,EAAeiB,EAAqB,MAAM,CAC5C,CACF,CACA,GAAIvY,EAAO,eAAgB,CACzB,MAAMwY,EAAuB,KAAK,IAAIT,EAAQ,OAAQ/X,EAAO,mBAAqB,CAAC,EAC7EyY,GAAiBtB,EAAaqB,EAAuBrB,GAAc,EAAIc,EAAWd,EAClFuB,EAAatY,EAAM,QAAU,OACnC2X,EAAQ,QAAQK,GAAU,CACxBA,EAAO,MAAM1gB,EAAO,aAAY,EAAKghB,EAAa,KAAK,EAAI,GAAGD,CAAa,IAC7E,CAAC,CACH,CACF,CACAxiB,EAAG,QAAQ,CAACkgB,EAAOwC,IAAe,CAShC,GARI3Y,EAAO,OAAS,aAClBmW,EAAM,iBAAiBY,GAAkB/W,EAAO,YAAY,CAAC,EAAE,QAAQ4Y,GAAc,CACnFA,EAAW,YAAc5Y,EAAO,sBAAsB7H,EAAU,CAAC,CACnE,CAAC,EACDge,EAAM,iBAAiBY,GAAkB/W,EAAO,UAAU,CAAC,EAAE,QAAQ6Y,GAAW,CAC9EA,EAAQ,YAAc7Y,EAAO,oBAAoB8X,CAAK,CACxD,CAAC,GAEC9X,EAAO,OAAS,cAAe,CACjC,IAAI8Y,EACA9Y,EAAO,oBACT8Y,EAAuBphB,EAAO,aAAY,EAAK,WAAa,aAE5DohB,EAAuBphB,EAAO,aAAY,EAAK,aAAe,WAEhE,MAAMqhB,GAAS5gB,EAAU,GAAK2f,EAC9B,IAAIkB,EAAS,EACTC,EAAS,EACTH,IAAyB,aAC3BE,EAASD,EAETE,EAASF,EAEX5C,EAAM,iBAAiBY,GAAkB/W,EAAO,oBAAoB,CAAC,EAAE,QAAQkZ,GAAc,CAC3FA,EAAW,MAAM,UAAY,6BAA6BF,CAAM,YAAYC,CAAM,IAClFC,EAAW,MAAM,mBAAqB,GAAGxhB,EAAO,OAAO,KAAK,IAC9D,CAAC,CACH,CACIsI,EAAO,OAAS,UAAYA,EAAO,cACrCrF,GAAawb,EAAOnW,EAAO,aAAatI,EAAQS,EAAU,EAAG2f,CAAK,CAAC,EAC/Da,IAAe,GAAG1b,EAAK,mBAAoBkZ,CAAK,IAEhDwC,IAAe,GAAG1b,EAAK,mBAAoBkZ,CAAK,EACpDlZ,EAAK,mBAAoBkZ,CAAK,GAE5Bze,EAAO,OAAO,eAAiBA,EAAO,SACxCye,EAAM,UAAUze,EAAO,SAAW,MAAQ,QAAQ,EAAEsI,EAAO,SAAS,CAExE,CAAC,CACH,CACA,SAASmZ,GAAS,CAEhB,MAAMnZ,EAAStI,EAAO,OAAO,WAC7B,GAAI2f,EAAoB,EAAI,OAC5B,MAAM5W,EAAe/I,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAUA,EAAO,QAAQ,OAAO,OAASA,EAAO,MAAQA,EAAO,OAAO,KAAK,KAAO,EAAIA,EAAO,OAAO,OAAS,KAAK,KAAKA,EAAO,OAAO,KAAK,IAAI,EAAIA,EAAO,OAAO,OAC7N,IAAIzB,EAAKyB,EAAO,WAAW,GAC3BzB,EAAKuE,EAAkBvE,CAAE,EACzB,IAAImjB,EAAiB,GACrB,GAAIpZ,EAAO,OAAS,UAAW,CAC7B,IAAIqZ,EAAkB3hB,EAAO,OAAO,KAAO,KAAK,KAAK+I,EAAe/I,EAAO,OAAO,cAAc,EAAIA,EAAO,SAAS,OAChHA,EAAO,OAAO,UAAYA,EAAO,OAAO,SAAS,SAAW2hB,EAAkB5Y,IAChF4Y,EAAkB5Y,GAEpB,QAAS3J,EAAI,EAAGA,EAAIuiB,EAAiBviB,GAAK,EACpCkJ,EAAO,aACToZ,GAAkBpZ,EAAO,aAAa,KAAKtI,EAAQZ,EAAGkJ,EAAO,WAAW,EAGxEoZ,GAAkB,IAAIpZ,EAAO,aAAa,IAAItI,EAAO,UAAY,gBAAkB,EAAE,WAAWsI,EAAO,WAAW,OAAOA,EAAO,aAAa,GAGnJ,CACIA,EAAO,OAAS,aACdA,EAAO,eACToZ,EAAiBpZ,EAAO,eAAe,KAAKtI,EAAQsI,EAAO,aAAcA,EAAO,UAAU,EAE1FoZ,EAAiB,gBAAgBpZ,EAAO,YAAY,4BAAsCA,EAAO,UAAU,aAG3GA,EAAO,OAAS,gBACdA,EAAO,kBACToZ,EAAiBpZ,EAAO,kBAAkB,KAAKtI,EAAQsI,EAAO,oBAAoB,EAElFoZ,EAAiB,gBAAgBpZ,EAAO,oBAAoB,aAGhEtI,EAAO,WAAW,QAAU,CAAA,EAC5BzB,EAAG,QAAQkgB,GAAS,CACdnW,EAAO,OAAS,UAClBrF,GAAawb,EAAOiD,GAAkB,EAAE,EAEtCpZ,EAAO,OAAS,WAClBtI,EAAO,WAAW,QAAQ,KAAK,GAAGye,EAAM,iBAAiBY,GAAkB/W,EAAO,WAAW,CAAC,CAAC,CAEnG,CAAC,EACGA,EAAO,OAAS,UAClB/C,EAAK,mBAAoBhH,EAAG,CAAC,CAAC,CAElC,CACA,SAASuI,GAAO,CACd9G,EAAO,OAAO,WAAaie,GAA0Bje,EAAQA,EAAO,eAAe,WAAYA,EAAO,OAAO,WAAY,CACvH,GAAI,mBACV,CAAK,EACD,MAAMsI,EAAStI,EAAO,OAAO,WAC7B,GAAI,CAACsI,EAAO,GAAI,OAChB,IAAI/J,EACA,OAAO+J,EAAO,IAAO,UAAYtI,EAAO,YAC1CzB,EAAKyB,EAAO,GAAG,cAAcsI,EAAO,EAAE,GAEpC,CAAC/J,GAAM,OAAO+J,EAAO,IAAO,WAC9B/J,EAAK,CAAC,GAAG,SAAS,iBAAiB+J,EAAO,EAAE,CAAC,GAE1C/J,IACHA,EAAK+J,EAAO,IAEV,GAAC/J,GAAMA,EAAG,SAAW,KACrByB,EAAO,OAAO,mBAAqB,OAAOsI,EAAO,IAAO,UAAY,MAAM,QAAQ/J,CAAE,GAAKA,EAAG,OAAS,IACvGA,EAAK,CAAC,GAAGyB,EAAO,GAAG,iBAAiBsI,EAAO,EAAE,CAAC,EAE1C/J,EAAG,OAAS,IACdA,EAAKA,EAAG,KAAKkgB,GACPhc,GAAegc,EAAO,SAAS,EAAE,CAAC,IAAMze,EAAO,EAEpD,IAGD,MAAM,QAAQzB,CAAE,GAAKA,EAAG,SAAW,IAAGA,EAAKA,EAAG,CAAC,GACnD,OAAO,OAAOyB,EAAO,WAAY,CAC/B,GAAAzB,CACN,CAAK,EACDA,EAAKuE,EAAkBvE,CAAE,EACzBA,EAAG,QAAQkgB,GAAS,CACdnW,EAAO,OAAS,WAAaA,EAAO,WACtCmW,EAAM,UAAU,IAAI,IAAInW,EAAO,gBAAkB,IAAI,MAAM,GAAG,CAAC,EAEjEmW,EAAM,UAAU,IAAInW,EAAO,cAAgBA,EAAO,IAAI,EACtDmW,EAAM,UAAU,IAAIze,EAAO,aAAY,EAAKsI,EAAO,gBAAkBA,EAAO,aAAa,EACrFA,EAAO,OAAS,WAAaA,EAAO,iBACtCmW,EAAM,UAAU,IAAI,GAAGnW,EAAO,aAAa,GAAGA,EAAO,IAAI,UAAU,EACnEoX,EAAqB,EACjBpX,EAAO,mBAAqB,IAC9BA,EAAO,mBAAqB,IAG5BA,EAAO,OAAS,eAAiBA,EAAO,qBAC1CmW,EAAM,UAAU,IAAInW,EAAO,wBAAwB,EAEjDA,EAAO,WACTmW,EAAM,iBAAiB,QAASyB,CAAa,EAE1ClgB,EAAO,SACVye,EAAM,UAAU,IAAInW,EAAO,SAAS,CAExC,CAAC,EACH,CACA,SAAStB,GAAU,CACjB,MAAMsB,EAAStI,EAAO,OAAO,WAC7B,GAAI2f,EAAoB,EAAI,OAC5B,IAAIphB,EAAKyB,EAAO,WAAW,GACvBzB,IACFA,EAAKuE,EAAkBvE,CAAE,EACzBA,EAAG,QAAQkgB,GAAS,CAClBA,EAAM,UAAU,OAAOnW,EAAO,WAAW,EACzCmW,EAAM,UAAU,OAAOnW,EAAO,cAAgBA,EAAO,IAAI,EACzDmW,EAAM,UAAU,OAAOze,EAAO,aAAY,EAAKsI,EAAO,gBAAkBA,EAAO,aAAa,EACxFA,EAAO,YACTmW,EAAM,UAAU,OAAO,IAAInW,EAAO,gBAAkB,IAAI,MAAM,GAAG,CAAC,EAClEmW,EAAM,oBAAoB,QAASyB,CAAa,EAEpD,CAAC,GAEClgB,EAAO,WAAW,SAASA,EAAO,WAAW,QAAQ,QAAQye,GAASA,EAAM,UAAU,OAAO,GAAGnW,EAAO,kBAAkB,MAAM,GAAG,CAAC,CAAC,CAC1I,CACAhD,EAAG,kBAAmB,IAAM,CAC1B,GAAI,CAACtF,EAAO,YAAc,CAACA,EAAO,WAAW,GAAI,OACjD,MAAMsI,EAAStI,EAAO,OAAO,WAC7B,GAAI,CACF,GAAAzB,CACN,EAAQyB,EAAO,WACXzB,EAAKuE,EAAkBvE,CAAE,EACzBA,EAAG,QAAQkgB,GAAS,CAClBA,EAAM,UAAU,OAAOnW,EAAO,gBAAiBA,EAAO,aAAa,EACnEmW,EAAM,UAAU,IAAIze,EAAO,aAAY,EAAKsI,EAAO,gBAAkBA,EAAO,aAAa,CAC3F,CAAC,CACH,CAAC,EACDhD,EAAG,OAAQ,IAAM,CACXtF,EAAO,OAAO,WAAW,UAAY,GAEvCgf,EAAO,GAEPlY,EAAI,EACJ2a,EAAM,EACN3R,EAAM,EAEV,CAAC,EACDxK,EAAG,oBAAqB,IAAM,CACxB,OAAOtF,EAAO,UAAc,KAC9B8P,EAAM,CAEV,CAAC,EACDxK,EAAG,kBAAmB,IAAM,CAC1BwK,EAAM,CACR,CAAC,EACDxK,EAAG,uBAAwB,IAAM,CAC/Bmc,EAAM,EACN3R,EAAM,CACR,CAAC,EACDxK,EAAG,UAAW,IAAM,CAClB0B,EAAO,CACT,CAAC,EACD1B,EAAG,iBAAkB,IAAM,CACzB,GAAI,CACF,GAAA/G,CACN,EAAQyB,EAAO,WACPzB,IACFA,EAAKuE,EAAkBvE,CAAE,EACzBA,EAAG,QAAQkgB,GAASA,EAAM,UAAUze,EAAO,QAAU,SAAW,KAAK,EAAEA,EAAO,OAAO,WAAW,SAAS,CAAC,EAE9G,CAAC,EACDsF,EAAG,cAAe,IAAM,CACtBwK,EAAM,CACR,CAAC,EACDxK,EAAG,QAAS,CAAC2Z,EAAIlO,IAAM,CACrB,MAAMyF,EAAWzF,EAAE,OACbxS,EAAKuE,EAAkB9C,EAAO,WAAW,EAAE,EACjD,GAAIA,EAAO,OAAO,WAAW,IAAMA,EAAO,OAAO,WAAW,aAAezB,GAAMA,EAAG,OAAS,GAAK,CAACiY,EAAS,UAAU,SAASxW,EAAO,OAAO,WAAW,WAAW,EAAG,CACpK,GAAIA,EAAO,aAAeA,EAAO,WAAW,QAAUwW,IAAaxW,EAAO,WAAW,QAAUA,EAAO,WAAW,QAAUwW,IAAaxW,EAAO,WAAW,QAAS,OACnK,MAAMmf,EAAW5gB,EAAG,CAAC,EAAE,UAAU,SAASyB,EAAO,OAAO,WAAW,WAAW,EAE5EuF,EADE4Z,IAAa,GACV,iBAEA,gBAFgB,EAIvB5gB,EAAG,QAAQkgB,GAASA,EAAM,UAAU,OAAOze,EAAO,OAAO,WAAW,WAAW,CAAC,CAClF,CACF,CAAC,EACD,MAAMof,EAAS,IAAM,CACnBpf,EAAO,GAAG,UAAU,OAAOA,EAAO,OAAO,WAAW,uBAAuB,EAC3E,GAAI,CACF,GAAAzB,CACN,EAAQyB,EAAO,WACPzB,IACFA,EAAKuE,EAAkBvE,CAAE,EACzBA,EAAG,QAAQkgB,GAASA,EAAM,UAAU,OAAOze,EAAO,OAAO,WAAW,uBAAuB,CAAC,GAE9F8G,EAAI,EACJ2a,EAAM,EACN3R,EAAM,CACR,EACMkP,EAAU,IAAM,CACpBhf,EAAO,GAAG,UAAU,IAAIA,EAAO,OAAO,WAAW,uBAAuB,EACxE,GAAI,CACF,GAAAzB,CACN,EAAQyB,EAAO,WACPzB,IACFA,EAAKuE,EAAkBvE,CAAE,EACzBA,EAAG,QAAQkgB,GAASA,EAAM,UAAU,IAAIze,EAAO,OAAO,WAAW,uBAAuB,CAAC,GAE3FgH,EAAO,CACT,EACA,OAAO,OAAOhH,EAAO,WAAY,CAC/B,OAAAof,EACA,QAAAJ,EACA,OAAAyC,EACA,OAAA3R,EACA,KAAAhJ,EACA,QAAAE,CACJ,CAAG,CACH,CCrcA,SAAS4a,GAAS7hB,EAAM,CACtB,GAAI,CACF,OAAAC,EACA,aAAAuG,EACA,GAAAjB,EACA,KAAAC,EACA,OAAA+C,CACJ,EAAMvI,EACJC,EAAO,SAAW,CAChB,QAAS,GACT,OAAQ,GACR,SAAU,CACd,EACEuG,EAAa,CACX,SAAU,CACR,QAAS,GACT,MAAO,IACP,kBAAmB,GACnB,qBAAsB,GACtB,gBAAiB,GACjB,iBAAkB,GAClB,kBAAmB,EACzB,CACA,CAAG,EACD,IAAIsb,EACAC,EACAC,EAAqBzZ,GAAUA,EAAO,SAAWA,EAAO,SAAS,MAAQ,IACzE0Z,EAAuB1Z,GAAUA,EAAO,SAAWA,EAAO,SAAS,MAAQ,IAC3E2Z,EACAC,EAAoB,IAAI,KAAI,EAAG,QAAO,EACtCC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACJ,SAASC,EAAgB3R,EAAG,CACtB,CAAC/Q,GAAUA,EAAO,WAAa,CAACA,EAAO,WACvC+Q,EAAE,SAAW/Q,EAAO,YACxBA,EAAO,UAAU,oBAAoB,gBAAiB0iB,CAAe,EACjE,EAAAD,GAAwB1R,EAAE,QAAUA,EAAE,OAAO,oBAGjD4R,EAAM,EACR,CACA,MAAMC,EAAe,IAAM,CACzB,GAAI5iB,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAC9CA,EAAO,SAAS,OAClBmiB,EAAY,GACHA,IACTH,EAAuBC,EACvBE,EAAY,IAEd,MAAMU,EAAW7iB,EAAO,SAAS,OAASiiB,EAAmBC,EAAoBF,EAAuB,IAAI,KAAI,EAAG,QAAO,EAC1HhiB,EAAO,SAAS,SAAW6iB,EAC3Btd,EAAK,mBAAoBsd,EAAUA,EAAWd,CAAkB,EAChED,EAAM,sBAAsB,IAAM,CAChCc,EAAY,CACd,CAAC,CACH,EACME,EAAgB,IAAM,CAC1B,IAAIC,EAMJ,OALI/iB,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAC1C+iB,EAAgB/iB,EAAO,OAAO,KAAKe,GAAWA,EAAQ,UAAU,SAAS,qBAAqB,CAAC,EAE/FgiB,EAAgB/iB,EAAO,OAAOA,EAAO,WAAW,EAE7C+iB,EACqB,SAASA,EAAc,aAAa,sBAAsB,EAAG,EAAE,EADrE,MAGtB,EACMC,EAAMC,GAAc,CACxB,GAAIjjB,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAClD,qBAAqB8hB,CAAG,EACxBc,EAAY,EACZ,IAAIxkB,EAAQ,OAAO6kB,EAAe,IAAcjjB,EAAO,OAAO,SAAS,MAAQijB,EAC/ElB,EAAqB/hB,EAAO,OAAO,SAAS,MAC5CgiB,EAAuBhiB,EAAO,OAAO,SAAS,MAC9C,MAAMkjB,EAAoBJ,EAAa,EACnC,CAAC,OAAO,MAAMI,CAAiB,GAAKA,EAAoB,GAAK,OAAOD,EAAe,MACrF7kB,EAAQ8kB,EACRnB,EAAqBmB,EACrBlB,EAAuBkB,GAEzBjB,EAAmB7jB,EACnB,MAAMsN,EAAQ1L,EAAO,OAAO,MACtBmjB,GAAU,IAAM,CAChB,CAACnjB,GAAUA,EAAO,YAClBA,EAAO,OAAO,SAAS,iBACrB,CAACA,EAAO,aAAeA,EAAO,OAAO,MAAQA,EAAO,OAAO,QAC7DA,EAAO,UAAU0L,EAAO,GAAM,EAAI,EAClCnG,EAAK,UAAU,GACLvF,EAAO,OAAO,SAAS,kBACjCA,EAAO,QAAQA,EAAO,OAAO,OAAS,EAAG0L,EAAO,GAAM,EAAI,EAC1DnG,EAAK,UAAU,GAGb,CAACvF,EAAO,OAASA,EAAO,OAAO,MAAQA,EAAO,OAAO,QACvDA,EAAO,UAAU0L,EAAO,GAAM,EAAI,EAClCnG,EAAK,UAAU,GACLvF,EAAO,OAAO,SAAS,kBACjCA,EAAO,QAAQ,EAAG0L,EAAO,GAAM,EAAI,EACnCnG,EAAK,UAAU,GAGfvF,EAAO,OAAO,UAChBkiB,EAAoB,IAAI,KAAI,EAAG,QAAO,EACtC,sBAAsB,IAAM,CAC1Bc,EAAG,CACL,CAAC,GAEL,EACA,OAAI5kB,EAAQ,GACV,aAAayjB,CAAO,EACpBA,EAAU,WAAW,IAAM,CACzBsB,GAAO,CACT,EAAG/kB,CAAK,GAER,sBAAsB,IAAM,CAC1B+kB,GAAO,CACT,CAAC,EAII/kB,CACT,EACMglB,EAAQ,IAAM,CAClBlB,EAAoB,IAAI,KAAI,EAAG,QAAO,EACtCliB,EAAO,SAAS,QAAU,GAC1BgjB,EAAG,EACHzd,EAAK,eAAe,CACtB,EACM8d,EAAO,IAAM,CACjBrjB,EAAO,SAAS,QAAU,GAC1B,aAAa6hB,CAAO,EACpB,qBAAqBC,CAAG,EACxBvc,EAAK,cAAc,CACrB,EACM+d,EAAQ,CAAC1S,EAAU2S,IAAU,CACjC,GAAIvjB,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAClD,aAAa6hB,CAAO,EACfjR,IACH4R,EAAsB,IAExB,MAAMW,EAAU,IAAM,CACpB5d,EAAK,eAAe,EAChBvF,EAAO,OAAO,SAAS,kBACzBA,EAAO,UAAU,iBAAiB,gBAAiB0iB,CAAe,EAElEC,EAAM,CAEV,EAEA,GADA3iB,EAAO,SAAS,OAAS,GACrBujB,EAAO,CACLhB,IACFN,EAAmBjiB,EAAO,OAAO,SAAS,OAE5CuiB,EAAe,GACfY,EAAO,EACP,MACF,CAEAlB,GADcA,GAAoBjiB,EAAO,OAAO,SAAS,QAC7B,IAAI,KAAI,EAAG,QAAO,EAAKkiB,GAC/C,EAAAliB,EAAO,OAASiiB,EAAmB,GAAK,CAACjiB,EAAO,OAAO,QACvDiiB,EAAmB,IAAGA,EAAmB,GAC7CkB,EAAO,EACT,EACMR,EAAS,IAAM,CACf3iB,EAAO,OAASiiB,EAAmB,GAAK,CAACjiB,EAAO,OAAO,MAAQA,EAAO,WAAa,CAACA,EAAO,SAAS,UACxGkiB,EAAoB,IAAI,KAAI,EAAG,QAAO,EAClCM,GACFA,EAAsB,GACtBQ,EAAIf,CAAgB,GAEpBe,EAAG,EAELhjB,EAAO,SAAS,OAAS,GACzBuF,EAAK,gBAAgB,EACvB,EACMie,EAAqB,IAAM,CAC/B,GAAIxjB,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAClD,MAAMsD,EAAW/F,EAAW,EACxB+F,EAAS,kBAAoB,WAC/Bkf,EAAsB,GACtBc,EAAM,EAAI,GAERhgB,EAAS,kBAAoB,WAC/Bqf,EAAM,CAEV,EACMc,EAAiB1S,GAAK,CACtBA,EAAE,cAAgB,UACtByR,EAAsB,GACtBC,EAAuB,GACnB,EAAAziB,EAAO,WAAaA,EAAO,SAAS,SACxCsjB,EAAM,EAAI,EACZ,EACMI,EAAiB3S,GAAK,CACtBA,EAAE,cAAgB,UACtB0R,EAAuB,GACnBziB,EAAO,SAAS,QAClB2iB,EAAM,EAEV,EACMgB,EAAoB,IAAM,CAC1B3jB,EAAO,OAAO,SAAS,oBACzBA,EAAO,GAAG,iBAAiB,eAAgByjB,CAAc,EACzDzjB,EAAO,GAAG,iBAAiB,eAAgB0jB,CAAc,EAE7D,EACME,EAAoB,IAAM,CAC1B5jB,EAAO,IAAM,OAAOA,EAAO,IAAO,WACpCA,EAAO,GAAG,oBAAoB,eAAgByjB,CAAc,EAC5DzjB,EAAO,GAAG,oBAAoB,eAAgB0jB,CAAc,EAEhE,EACMG,EAAuB,IAAM,CAChBtmB,EAAW,EACnB,iBAAiB,mBAAoBimB,CAAkB,CAClE,EACMM,EAAuB,IAAM,CAChBvmB,EAAW,EACnB,oBAAoB,mBAAoBimB,CAAkB,CACrE,EACAle,EAAG,OAAQ,IAAM,CACXtF,EAAO,OAAO,SAAS,UACzB2jB,EAAiB,EACjBE,EAAoB,EACpBT,EAAK,EAET,CAAC,EACD9d,EAAG,UAAW,IAAM,CAClBse,EAAiB,EACjBE,EAAoB,EAChB9jB,EAAO,SAAS,SAClBqjB,EAAI,CAER,CAAC,EACD/d,EAAG,yBAA0B,IAAM,EAC7B+c,GAAiBG,IACnBG,EAAM,CAEV,CAAC,EACDrd,EAAG,6BAA8B,IAAM,CAChCtF,EAAO,OAAO,SAAS,qBAG1BqjB,EAAI,EAFJC,EAAM,GAAM,EAAI,CAIpB,CAAC,EACDhe,EAAG,wBAAyB,CAAC2Z,EAAIvT,EAAOkF,IAAa,CAC/C5Q,EAAO,WAAa,CAACA,EAAO,SAAS,UACrC4Q,GAAY,CAAC5Q,EAAO,OAAO,SAAS,qBACtCsjB,EAAM,GAAM,EAAI,EAEhBD,EAAI,EAER,CAAC,EACD/d,EAAG,kBAAmB,IAAM,CAC1B,GAAI,EAAAtF,EAAO,WAAa,CAACA,EAAO,SAAS,SACzC,IAAIA,EAAO,OAAO,SAAS,qBAAsB,CAC/CqjB,EAAI,EACJ,MACF,CACAjB,EAAY,GACZC,EAAgB,GAChBG,EAAsB,GACtBF,EAAoB,WAAW,IAAM,CACnCE,EAAsB,GACtBH,EAAgB,GAChBiB,EAAM,EAAI,CACZ,EAAG,GAAG,EACR,CAAC,EACDhe,EAAG,WAAY,IAAM,CACnB,GAAI,EAAAtF,EAAO,WAAa,CAACA,EAAO,SAAS,SAAW,CAACoiB,GAGrD,IAFA,aAAaE,CAAiB,EAC9B,aAAaT,CAAO,EAChB7hB,EAAO,OAAO,SAAS,qBAAsB,CAC/CqiB,EAAgB,GAChBD,EAAY,GACZ,MACF,CACIC,GAAiBriB,EAAO,OAAO,SAAS2iB,EAAM,EAClDN,EAAgB,GAChBD,EAAY,GACd,CAAC,EACD9c,EAAG,cAAe,IAAM,CAClBtF,EAAO,WAAa,CAACA,EAAO,SAAS,UACzCuiB,EAAe,GACjB,CAAC,EACD,OAAO,OAAOviB,EAAO,SAAU,CAC7B,MAAAojB,EACA,KAAAC,EACA,MAAAC,EACA,OAAAX,CACJ,CAAG,CACH,CC7SA,SAASoB,GAAWzb,EAAQ,CAC1B,KAAM,CACJ,OAAA0b,EACA,OAAAhkB,EACA,GAAAsF,EACA,aAAA2K,EACA,cAAAe,EACA,gBAAAiT,EACA,YAAAC,EACA,gBAAAC,EACA,gBAAAC,CACJ,EAAM9b,EACJhD,EAAG,aAAc,IAAM,CACrB,GAAItF,EAAO,OAAO,SAAWgkB,EAAQ,OACrChkB,EAAO,WAAW,KAAK,GAAGA,EAAO,OAAO,sBAAsB,GAAGgkB,CAAM,EAAE,EACrEE,GAAeA,KACjBlkB,EAAO,WAAW,KAAK,GAAGA,EAAO,OAAO,sBAAsB,IAAI,EAEpE,MAAMqkB,EAAwBJ,EAAkBA,EAAe,EAAK,CAAA,EACpE,OAAO,OAAOjkB,EAAO,OAAQqkB,CAAqB,EAClD,OAAO,OAAOrkB,EAAO,eAAgBqkB,CAAqB,CAC5D,CAAC,EACD/e,EAAG,+BAAgC,IAAM,CACnCtF,EAAO,OAAO,SAAWgkB,GAC7B/T,EAAY,CACd,CAAC,EACD3K,EAAG,gBAAiB,CAAC2Z,EAAI3e,IAAa,CAChCN,EAAO,OAAO,SAAWgkB,GAC7BhT,EAAc1Q,CAAQ,CACxB,CAAC,EACDgF,EAAG,gBAAiB,IAAM,CACxB,GAAItF,EAAO,OAAO,SAAWgkB,GACzBG,EAAiB,CACnB,GAAI,CAACC,GAAmB,CAACA,EAAe,EAAG,aAAc,OAEzDpkB,EAAO,OAAO,QAAQe,GAAW,CAC/BA,EAAQ,iBAAiB,8GAA8G,EAAE,QAAQujB,GAAYA,EAAS,QAAQ,CAChL,CAAC,EAEDH,EAAe,CACjB,CACF,CAAC,EACD,IAAII,EACJjf,EAAG,gBAAiB,IAAM,CACpBtF,EAAO,OAAO,SAAWgkB,IACxBhkB,EAAO,OAAO,SACjBukB,EAAyB,IAE3B,sBAAsB,IAAM,CACtBA,GAA0BvkB,EAAO,QAAUA,EAAO,OAAO,SAC3DiQ,EAAY,EACZsU,EAAyB,GAE7B,CAAC,EACH,CAAC,CACH,CCrDA,SAASC,GAAaC,EAAc1jB,EAAS,CAC3C,MAAM2jB,EAAc5jB,GAAoBC,CAAO,EAC/C,OAAI2jB,IAAgB3jB,IAClB2jB,EAAY,MAAM,mBAAqB,SACvCA,EAAY,MAAM,6BAA6B,EAAI,UAE9CA,CACT,CCPA,SAASC,GAAalE,EAAQ1f,EAASb,EAAM,CAC3C,MAAM0kB,EAAc,sBAAsB1kB,EAAO,IAAIA,CAAI,GAAK,EAAE,GAAY,wBAAwBugB,CAAM,EAAO,GAC3GoE,EAAkB/jB,GAAoBC,CAAO,EACnD,IAAIujB,EAAWO,EAAgB,cAAc,IAAID,EAAY,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC,EAAE,EACnF,OAAKN,IACHA,EAAWziB,GAAc,MAAO+iB,EAAY,MAAM,GAAG,CAAC,EACtDC,EAAgB,OAAOP,CAAQ,GAE1BA,CACT,CCNA,SAASQ,GAAgB/kB,EAAM,CAC7B,GAAI,CACF,OAAAC,EACA,aAAAuG,EACA,GAAAjB,CACJ,EAAMvF,EACJwG,EAAa,CACX,gBAAiB,CACf,OAAQ,GACR,QAAS,EACT,MAAO,IACP,MAAO,EACP,SAAU,EACV,aAAc,EACpB,CACA,CAAG,EAsEDwd,GAAW,CACT,OAAQ,YACR,OAAA/jB,EACA,GAAAsF,EACA,aAzEmB,IAAM,CACzB,KAAM,CACJ,MAAOyf,EACP,OAAQC,EACR,OAAAlc,EACA,gBAAAI,CACN,EAAQlJ,EACEsI,EAAStI,EAAO,OAAO,gBACvBilB,EAAejlB,EAAO,aAAY,EAClCklB,EAAYllB,EAAO,UACnBmlB,EAASF,EAAe,CAACC,EAAYH,EAAc,EAAI,CAACG,EAAYF,EAAe,EACnFI,EAASH,EAAe3c,EAAO,OAAS,CAACA,EAAO,OAChD6D,EAAY7D,EAAO,MACnB+c,EAAItiB,GAAa/C,CAAM,EAE7B,QAASZ,EAAI,EAAG6gB,EAASnX,EAAO,OAAQ1J,EAAI6gB,EAAQ7gB,GAAK,EAAG,CAC1D,MAAM2B,EAAU+H,EAAO1J,CAAC,EAClBuK,EAAYT,EAAgB9J,CAAC,EAC7BiN,EAActL,EAAQ,kBACtBukB,GAAgBH,EAAS9Y,EAAc1C,EAAY,GAAKA,EACxD4b,EAAmB,OAAOjd,EAAO,UAAa,WAAaA,EAAO,SAASgd,CAAY,EAAIA,EAAehd,EAAO,SACvH,IAAIkd,EAAUP,EAAeG,EAASG,EAAmB,EACrDE,EAAUR,EAAe,EAAIG,EAASG,EAEtCG,EAAa,CAACvZ,EAAY,KAAK,IAAIoZ,CAAgB,EACnDI,EAAUrd,EAAO,QAEjB,OAAOqd,GAAY,UAAYA,EAAQ,QAAQ,GAAG,IAAM,KAC1DA,EAAU,WAAWrd,EAAO,OAAO,EAAI,IAAMqB,GAE/C,IAAIic,EAAaX,EAAe,EAAIU,EAAUJ,EAC1CM,EAAaZ,EAAeU,EAAUJ,EAAmB,EACzDlE,EAAQ,GAAK,EAAI/Y,EAAO,OAAS,KAAK,IAAIid,CAAgB,EAG1D,KAAK,IAAIM,CAAU,EAAI,OAAOA,EAAa,GAC3C,KAAK,IAAID,CAAU,EAAI,OAAOA,EAAa,GAC3C,KAAK,IAAIF,CAAU,EAAI,OAAOA,EAAa,GAC3C,KAAK,IAAIF,CAAO,EAAI,OAAOA,EAAU,GACrC,KAAK,IAAIC,CAAO,EAAI,OAAOA,EAAU,GACrC,KAAK,IAAIpE,CAAK,EAAI,OAAOA,EAAQ,GACrC,MAAMyE,EAAiB,eAAeD,CAAU,MAAMD,CAAU,MAAMF,CAAU,gBAAgBL,EAAEI,CAAO,CAAC,gBAAgBJ,EAAEG,CAAO,CAAC,cAAcnE,CAAK,IACjJ7K,EAAWgO,GAAalc,EAAQvH,CAAO,EAG7C,GAFAyV,EAAS,MAAM,UAAYsP,EAC3B/kB,EAAQ,MAAM,OAAS,CAAC,KAAK,IAAI,KAAK,MAAMwkB,CAAgB,CAAC,EAAI,EAC7Djd,EAAO,aAAc,CAEvB,IAAIyd,EAAiBd,EAAelkB,EAAQ,cAAc,2BAA2B,EAAIA,EAAQ,cAAc,0BAA0B,EACrIilB,EAAgBf,EAAelkB,EAAQ,cAAc,4BAA4B,EAAIA,EAAQ,cAAc,6BAA6B,EACvIglB,IACHA,EAAiBpB,GAAa,YAAa5jB,EAASkkB,EAAe,OAAS,KAAK,GAE9Ee,IACHA,EAAgBrB,GAAa,YAAa5jB,EAASkkB,EAAe,QAAU,QAAQ,GAElFc,IAAgBA,EAAe,MAAM,QAAUR,EAAmB,EAAIA,EAAmB,GACzFS,IAAeA,EAAc,MAAM,QAAU,CAACT,EAAmB,EAAI,CAACA,EAAmB,EAC/F,CACF,CACF,EAeE,cAdoBjlB,GAAY,CACNN,EAAO,OAAO,IAAIe,GAAWD,GAAoBC,CAAO,CAAC,EACjE,QAAQxC,GAAM,CAC9BA,EAAG,MAAM,mBAAqB,GAAG+B,CAAQ,KACzC/B,EAAG,iBAAiB,8GAA8G,EAAE,QAAQ+lB,GAAY,CACtJA,EAAS,MAAM,mBAAqB,GAAGhkB,CAAQ,IACjD,CAAC,CACH,CAAC,CACH,EAOE,YAAa,IAAM,GACnB,gBAAiB,KAAO,CACtB,oBAAqB,EAC3B,EACA,CAAG,CACH,CChGO,MAAM2lB,GAAmB,CAC9B,wBAAyB,EACzB,yBAA0B,CAC5B,EAGaC,GAAgB,CAC3B,OAAQ,CACN,cAAe,GACf,gBAAiB,CAGnB,EACA,QAAS,CACP,cAAe,GACf,gBAAiB,CAGnB,CAEF,EAGaC,EAAiB,CAC5B,sBAAuB,GAEvB,iBAAkB,EAClB,iBAAkB,GAClB,oBAAqB,IACrB,oBAAqB,IACrB,sBAAuB,EACvB,sBAAuB,EACzB,EAuBaC,GAAgB,CAC3B,wBAAyB,EACzB,oBAAqB,GACrB,4BAA6B,IAC/B,EAGaC,EAAsB,CACjC,gBAAiB,EACjB,oBAAqB,EACrB,wBAAyB,CAC3B,EAGaC,EAAkB,CAC7B,aAAc,EACd,YAAa,EACb,gBAAiB,GACjB,iBAAkB,EAClB,iBAAkB,EACpB,EAGaC,GAAiB,CAC5B,YAAa,CACf,EAGaC,GAAS,CACpB,eAAgB,GAChB,oBAAqB,IACrB,wBAAyB,GAC3B,EAGaC,GAAe,CAC1B,uBAAwB,oBAGxB,eAAgB,6BAClB,EAGaC,GAAc,CAUzB,UAAW,UAUb,EAGaC,GAAgB,CAQ3B,qBAAsB,qBACtB,sBAAuB,sBACvB,sBAAuB,qBACzB,EAGaC,GAAmB,CAC9B,GAAI,gCACJ,mBAAoB,qBACpB,WAAY,GACZ,gBAAiB,0CACnB,ECpIa/kB,EAAgB,CACzBglB,EACAC,EAAqC,CAAA,EACrCC,EAAY,KACE,CACd,MAAM9lB,EAAU,SAAS,cAAc4lB,CAAO,EAE9C,SAAW,CAACxpB,EAAK0d,CAAK,IAAK,OAAO,QAAQ+L,CAAU,EAC5CzpB,IAAQ,YACR4D,EAAQ,UAAY8Z,EACb1d,IAAQ,QACf4D,EAAQ,aAAa,QAAS8Z,CAAK,EAEnC9Z,EAAQ,aAAa5D,EAAK0d,CAAK,EAIvC,OAAIgM,IACA9lB,EAAQ,UAAY8lB,GAGjB9lB,CACX,EAOa+lB,GAAkBrpB,GAC3B,SAAS,eAAeA,CAAE,EAQjBspB,GAAgB,CAAC/lB,EAAkBO,EAA6B,WAA6B,CACtG,GAAI,CACA,GAAI,CAACA,GAAU,CAACP,EACZ,MAAM,IAAI,MAAM,4BAA4B,EAEhD,OAAOO,EAAO,cAAcP,CAAQ,CACxC,MAAQ,CACJ,OAAO,SAAS,cAAc,EAAE,CACpC,CACJ,EAQagmB,GAAmB,CAAChmB,EAAkBO,EAA6B,WAAkC,CAC9G,GAAI,CACA,MAAI,CAACA,GAAU,CAACP,EACL,SAAS,iBAAiB,EAAE,EAEhCO,EAAO,iBAAiBP,CAAQ,CAC3C,MAAQ,CACJ,OAAO,SAAS,iBAAiB,EAAE,CACvC,CACJ,EA+CaimB,GAAY,CAAClmB,EAAsBmmB,IAAyC,CACrF,GAAI,GAACnmB,GAAW,CAACmmB,GAGjB,SAAW,CAAC1K,EAAU3B,CAAK,IAAK,OAAO,QAAQqM,CAAM,EACjD,GAAI,CACAnmB,EAAQ,MAAM,YAAYyb,EAAU3B,CAAK,CAC7C,MAAQ,CAER,CAER,EAOasM,EAAc,CAAC5lB,EAAiBe,IAAyB,CAClE,GAAI,CACIf,GAAUe,GACVf,EAAO,YAAYe,CAAK,CAEhC,MAAQ,CAER,CACJ,EAOa8kB,GAAe,CAAC7lB,EAAiBe,IAAyB,CACnE,GAAI,CACIf,GAAUe,GAASf,EAAO,YAC1BA,EAAO,WAAW,OAAOe,CAAK,CAEtC,MAAQ,CAER,CACJ,EAMa+kB,GAAiBtmB,GAA2B,CACrD,GAAI,CACIA,GAAWA,EAAQ,YACnBA,EAAQ,WAAW,YAAYA,CAAO,CAE9C,MAAQ,CAER,CACJ,EC3KMumB,GAAuB,CACzB,OAAQ,GACR,SAAU,EACd,EAMaC,GAAiB,IAAe,CACzC,GAAID,GAAqB,OACrB,OAAOA,GAAqB,SAGhC,IAAIE,EAAQ,GACZ,MAAM/jB,EAAY,UAAU,WAAa,UAAU,QAAW,WAA6C,MAGrGgkB,EAAc,2TAEdC,EAAe,4hDAEfC,EAAkBlkB,EAAU,OAC9BsiB,GAAiB,wBACjBA,GAAiB,wBAAA,EAGrB,OAAI0B,EAAY,KAAKhkB,CAAS,GAAKikB,EAAa,KAAKC,CAAe,KAChEH,EAAQ,IAGZF,GAAqB,SAAWE,EAChCF,GAAqB,OAAS,GACvBE,CACX,EAiBaI,GAAkB,IACVL,GAAA,EAGN,CACH,aAAcvB,GAAc,OAAO,cACnC,cAAeA,GAAc,OAAO,eAAA,EAIrC,CACH,aAAcA,GAAc,QAAQ,cACpC,cAAeA,GAAc,QAAQ,eAAA,EC9DhC6B,EAA4B,CACvC,IAAM,aACN,IAAK,CACH,YAAanB,GAAiB,GAC9B,kBAAmBA,GAAiB,kBAAA,EAEtC,OAAQ,CACN,UAAWA,GAAiB,WAC5B,sBAAuBJ,GAAO,wBAC9B,UAAWA,GAAO,eAClB,kBAAmBA,GAAO,oBAC1B,IAAK,CACH,YAAaC,GAAa,uBAC1B,YAAaC,GAAY,SAAA,EAE3B,OAAQ,CACN,aAAc,GACd,OAAQ,YACR,eAAgB,GAChB,cAAe,EACf,gBAAiB,CACf,OAAQ,EACR,MAAO,IACP,SAAU,EACV,aAAc,GACd,QAAS,CAAA,EAEX,WAAY,CACV,GAAIC,GAAc,qBAClB,KAAM,SAAA,EAER,WAAY,CACV,OAAQA,GAAc,sBACtB,OAAQA,GAAc,qBAAA,CACxB,CACF,EAEF,GAAI,CACF,aAAcF,GAAa,eAC3B,cAAeG,GAAiB,eAAA,EAElC,KAAM,CACJ,aAAc,CAAA,CAEd,CAEJ,EC3CO,MAAMoB,EAAiB,CAAvB,aAAA,CAGH,KAAiB,UAAYD,EAAc,OAAO,UAClD,KAAiB,UAAYA,EAAc,OAAO,SAAA,CAK1C,kBAAkB1qB,EAAsB,CAC5C,GAAI,CACA,MAAM4qB,EAAQC,GAAOA,EAAI,MACnBC,EAASF,GAASA,EAAM,UAC9B,OAAI,OAAOE,GAAW,WACXA,EAAO,KAAKF,EAAO5qB,CAAG,EAEjC,MACJ,MAAQ,CACJ,MACJ,CACJ,CAKA,sBAAsB+qB,EAA0B,CAC5C,GAAI,CACA,KAAK,QAAA,EAEL,MAAMC,EAAY,KAAK,gBAAA,EACjBroB,EAAS,KAAK,oBAAoBqoB,CAAS,EAC3CC,EAAU,KAAK,oBAAoBtoB,CAAM,EAE/C,KAAK,eAAesoB,CAAO,EAC3B,KAAK,iBAAiBtoB,CAAM,EAC5B,KAAK,iBAAiBA,CAAM,EAE5B,KAAK,UAAYqoB,EACjB,KAAK,YAAYA,CAAS,EAG1B,WAAW,IAAM,CACb,KAAK,iBAAiB,KAAK,mBAAmB,CAClD,EAAG,KAAK,SAAS,CACrB,MAAQ,CAER,CACJ,CAKQ,0BAAiC,CACrC,MAAME,EAAoBC,GAAuB,IAAIT,EAAc,OAAO,IAAI,WAAW,EAAE,EACvFQ,GACAE,GAAuBF,CAAiB,EAG5C,MAAMG,EAAcC,GAA0B,WAAW,EACzD,UAAW1nB,KAAWynB,EAClBD,GAAuBxnB,CAAO,CAEtC,CAMQ,iBAA+B,CACnC,KAAK,yBAAA,EAEL,MAAMonB,EAAYO,EAAuB,MAAO,CAC5C,GAAIb,EAAc,OAAO,IAAI,YAC7B,UAAW,aAAA,CACd,EAED,YAAK,kBAAkBM,CAAS,EACzBA,CACX,CAMQ,kBAAkBA,EAA8B,CACpD,GAAIZ,KAAkB,CAElB,MAAMoB,EADc,WAAW,WACEzC,GAAc,wBAA0BA,GAAc,oBACvF0C,GAAmBT,EAAW,CAC1B,MAAS,GAAGQ,CAAU,KACtB,cAAe,GAAG,EAAEA,EAAazC,GAAc,4BAA4B,IAAA,CAC9E,CACL,CACJ,CAOQ,oBAAoBiC,EAAqC,CAC7D,MAAMroB,EAAS4oB,EAAuB,MAAO,CACzC,UAAW,UAAUb,EAAc,OAAO,IAAI,WAAW,EAAA,CAC5D,EACDgB,OAAAA,EAAqBV,EAAWroB,CAAM,EAC/BA,CACX,CAOQ,oBAAoBA,EAAkC,CAC1D,MAAMsoB,EAAUM,EAAuB,MAAO,CAC1C,UAAW,gBAAA,CACd,EACDG,OAAAA,EAAqB/oB,EAAQsoB,CAAO,EAC7BA,CACX,CAMQ,mBAA4B,CAChC,MAAMU,EAAiB,KAAK,kBAAkB,gCAAgC,EAC9E,OAAIA,EACO,OAAO,SAAS,OAAOA,CAAc,EAAG,EAAE,EAE9CjB,EAAc,OAAO,qBAChC,CAMQ,eAAeO,EAA4B,CAC/C,QAASzd,EAAawb,EAAoB,oBAAqBxb,GAAc,KAAK,UAAWA,GAAcwb,EAAoB,gBAAiB,CAC5I,MAAM4C,EAAW,KAAK,kBAAkB,wBAAwBpe,CAAU,EAAE,EACtEqe,EAAY,KAAK,kBAAkB,uBAAuBre,CAAU,EAAE,EAE5E,GAAIoe,EAAU,CACV,MAAMpf,EAAQ,KAAK,YAAY,OAAOof,CAAQ,EAAG,OAAOC,GAAa,EAAE,CAAC,EACxEH,EAAqBT,EAASze,CAAK,CACvC,CACJ,CACJ,CAQQ,YAAYof,EAAkBC,EAAgC,CAClE,MAAMrf,EAAQ+e,EAAuB,MAAO,CACxC,UAAW,cAAA,CACd,EAED,IAAIO,EAAe,GACnB,OAAID,IACAC,EAAe,yBAAyBD,CAAS,KAErDrf,EAAM,UAAY,iBAAiBsf,CAAY,UAAUF,CAAQ,OAE1Dpf,CACX,CAMQ,iBAAiB7J,EAA2B,CAChD,MAAMopB,EAAaR,EAAuB,MAAO,CAC7C,UAAW,mBAAA,CACd,EACDG,EAAqB/oB,EAAQopB,CAAU,CAC3C,CAMQ,iBAAiBppB,EAA2B,CAChD,MAAMqpB,EAAaT,EAAuB,MAAO,CAC7C,UAAW,oBAAA,CACd,EACKU,EAAaV,EAAuB,MAAO,CAC7C,UAAW,oBAAA,CACd,EAEDG,EAAqB/oB,EAAQqpB,CAAU,EACvCN,EAAqB/oB,EAAQspB,CAAU,CAC3C,CAMQ,YAAYjB,EAA8B,CAC9C,MAAMkB,EAAmBf,GAAuB,qBAAqB,EACjEe,GACAC,GAAsBD,EAAkBlB,CAAS,CAEzD,CAMQ,iBAAiBW,EAA8B,CACnD,GAAI,CACA,KAAK,OAAS,IAAI5M,EAAO,IAAI2L,EAAc,OAAO,IAAI,WAAW,GAAI,CACjE,SAAU,CACN,MAAOiB,CAAA,EAEX,aAAcjB,EAAc,OAAO,OAAO,aAC1C,OAAQA,EAAc,OAAO,OAAO,OACpC,eAAgBA,EAAc,OAAO,OAAO,eAC5C,cAAeA,EAAc,OAAO,OAAO,cAC3C,gBAAiB,CACb,OAAQA,EAAc,OAAO,OAAO,gBAAgB,OACpD,MAAOA,EAAc,OAAO,OAAO,gBAAgB,MACnD,SAAUA,EAAc,OAAO,OAAO,gBAAgB,SACtD,aAAcA,EAAc,OAAO,OAAO,gBAAgB,aAC1D,QAASA,EAAc,OAAO,OAAO,gBAAgB,OAAA,EAEzD,WAAY,CACR,GAAIA,EAAc,OAAO,OAAO,WAAW,GAC3C,KAAMA,EAAc,OAAO,OAAO,WAAW,IAAA,EAEjD,WAAY,CACR,OAAQA,EAAc,OAAO,OAAO,WAAW,OAC/C,OAAQA,EAAc,OAAO,OAAO,WAAW,MAAA,EAEnD,QAAS,CAACjD,GAAiB1G,GAAYkB,GAAYsC,EAAQ,CAAA,CAC9D,CACL,MAAQ,CAER,CACJ,CAKA,SAAgB,CACR,KAAK,SACL,KAAK,OAAO,QAAQ,GAAM,EAAI,EAC9B,OAAO,KAAK,QAGZ,KAAK,YACL6G,GAAuB,KAAK,SAAS,EACrC,OAAO,KAAK,UAEpB,CACJ,CClQO,MAAMgB,EAAU,CAKnB,sBAA6B,CACzB,GAAI,CACA,GAAIC,GAAwB,oBAAoB,EAC5C,OAGJ,MAAMC,EAAWhB,GAA0B,UAAU,EACrD,GAAIgB,EAAS,SAAWrD,EAAgB,aACpC,OAGJ,MAAM+B,EAAY,KAAK,yBAAA,EACvB,GAAI,CAACA,EACD,OAGJ,MAAMroB,EAAS,KAAK,gBAAgBqoB,CAAS,EAC7C,GAAI,CAACroB,EACD,OAGJ,MAAMsoB,EAAU,KAAK,uBAAuBtoB,CAAM,EAClD,GAAI,CAACsoB,EACD,OAGJ,KAAK,kBAAkBA,EAASqB,CAAQ,EACxC,KAAK,mBAAmBtB,CAAS,EACjC,KAAK,oBAAoBA,CAAS,EAClC,KAAK,uBAAA,EACL,KAAK,kBAAA,EACL,KAAK,oBAAA,CACT,MAAQ,CAER,CACJ,CAKQ,0BAAwC,CAC5C,MAAMA,EAAYO,EAAuB,MAAO,CAC5C,UAAW,qBACX,GAAI,oBAAA,CACP,EAEKgB,EAAgBhB,EAAuB,MAAO,CAChD,UAAW,uBAAA,CACd,EAEDG,OAAAA,EAAqBV,EAAWuB,CAAa,EACtCvB,CACX,CAKQ,gBAAgBA,EAAqC,CACzD,MAAMuB,EAAgBvB,EAAU,cAAc,wBAAwB,EAChEroB,EAAS4oB,EAAuB,MAAO,CACzC,UAAW,kBAAA,CACd,EAED,OAAIgB,GACAb,EAAqBa,EAAe5pB,CAAM,EAGvCA,CACX,CAKQ,uBAAuBA,EAAkC,CAC7D,MAAMsoB,EAAUM,EAAuB,MAAO,CAC1C,UAAW,iBACX,GAAI,kBAAA,CACP,EACDG,OAAAA,EAAqB/oB,EAAQsoB,CAAO,EAC7BA,CACX,CAKQ,kBAAkBA,EAAsBqB,EAAqC,CACjF,MAAME,EAAWpC,GAAA,EAEjB,UAAW3lB,KAAO6nB,EAAU,CACxB,MAAMG,EAAahoB,EACbioB,EAAU,KAAK,eAAeD,CAAU,EAE9C,GAAIC,EAAS,CACT,MAAMlgB,EAAQ,KAAK,eAAekgB,EAASF,CAAQ,EACnDd,EAAqBT,EAASze,CAAK,CACvC,CACJ,CACJ,CAKQ,eAAe/H,EAAkC,CACrD,MAAMkoB,EAAcloB,EAAI,cAAc,GAAG,EACnCmoB,EAAcnoB,EAAI,cAAc,eAAe,EAC/CooB,EAAcpoB,EAAI,cAAc,sBAAsB,EAE5D,GAAI,CAACkoB,GAAe,CAACC,EACjB,OAIJ,MAAME,EAAkB,KAAK,sBAAsBH,EAAY,KAAMloB,CAAG,EAClEsoB,EAAgB,WAAW,iBAAiBtoB,CAAG,EAC/CuoB,EAAaF,GAAmBC,EAAc,WAEpD,IAAIE,EAAc,GACdC,EAAY,GAChB,OAAIL,IACAI,EAAcJ,EAAY,aAAe,GACzCK,EAAY,WAAW,iBAAiBL,CAAW,EAAE,OAGlD,CACH,IAAKF,EAAY,KACjB,WAAAK,EACA,KAAMJ,EAAY,aAAe,GACjC,UAAW,WAAW,iBAAiBA,CAAW,EAAE,MACpD,YAAAK,EACA,UAAAC,CAAA,CAER,CAKQ,sBAAsBC,EAAgBV,EAAwC,CAClF,GAAI,CAGA,MAAMW,EADM,IAAI,IAAID,EAAQ,WAAW,SAAS,MAAM,EACpC,SAAS,MAAM,GAAG,EAAE,OAAO,OAAO,EAC9CE,EAASD,EAAM,QAAQ,GAAG,EAC1BE,EAAYF,EAAM,QAAQ,MAAM,EAEtC,IAAIG,EAAO,GAUX,GARIF,IAAWpE,EAAgB,iBAAmBmE,EAAMC,EAASpE,EAAgB,gBAAgB,EAC7FsE,EAAOH,EAAMC,EAASpE,EAAgB,gBAAgB,EAC/CqE,IAAcrE,EAAgB,iBAAmBmE,EAAME,EAAYrE,EAAgB,gBAAgB,EAC1GsE,EAAOH,EAAME,EAAYrE,EAAgB,gBAAgB,EAClDmE,EAAM,OAASnE,EAAgB,eACtCsE,EAAOH,EAAMA,EAAM,OAASnE,EAAgB,gBAAgB,GAG5D,CAACsE,EACD,OAKJ,MAAMC,EADO3C,EAAI,MAAM,IAAI,MAAM,EACX,KAAM4C,GAAqB,CAC7C,MAAMC,EAAYD,EAClB,IAAIE,EAAU,GAEd,OAAI,OAAOD,EAAU,MAAS,WAC1BC,EAAUD,EAAU,KAAA,EACbA,EAAU,WAAa,OAAOA,EAAU,WAAc,aAC7DC,EAAUD,EAAU,UAAU,MAAM,GAGjCC,IAAYJ,CACvB,CAAC,EAED,GAAI,CAACC,EACD,OAIJ,MAAME,EAAYF,EAClB,IAAII,EAAQ,GAMZ,OAJIF,EAAU,WAAa,OAAOA,EAAU,WAAc,aACtDE,EAAQF,EAAU,UAAU,yBAAyB,GAGrDE,EACO,OAAOA,CAAK,IAGvB,MACJ,MAAQ,CAEJ,MAAMC,EAAmBpB,EAAW,MAAM,WAC1C,OAAIoB,GAAoBA,EAAiB,SAAS,MAAM,EAC7CA,EAEX,MACJ,CACJ,CAKQ,eAAenB,EAAkBF,EAAgC,CACrE,MAAMhgB,EAAQ+e,EAAuB,MAAO,CACxC,UAAW,+BAAA,CACd,EAED,IAAIuC,EAAa,yBACbtB,IACAsB,EAAa,iCAGjB,MAAMC,EAAkB,cAAcrB,EAAQ,UAAU,oFAGlDsB,EAAqB,KAAK,mBAAmBtB,EAAQ,UAAU,EAGrE,IAAIuB,EAAc,GAClB,OAAKD,IACDC,EAAc;AAAA,gEACsCvB,EAAQ,SAAS;AAAA,kBAC/DA,EAAQ,IAAI;AAAA;AAAA,WAKtBlgB,EAAM,UAAY;AAAA,uBACHkgB,EAAQ,GAAG;AAAA,8BACJoB,CAAU,YAAYC,CAAe;AAAA,sBAC7CE,CAAW;AAAA;AAAA;AAAA,UAKlBzhB,CACX,CAKQ,mBAAmBwgB,EAA6B,CACpD,OAAKA,EAKEA,EAAW,SAAS,MAAM,GAAK,CAACA,EAAW,SAAS,OAAO,EAJvD,EAKf,CAKQ,mBAAmBhC,EAA8B,CACrD,MAAMkD,EAAiB/C,GAAuB,uCAAuC,EACjF+C,GACA/B,GAAsB+B,EAAgBlD,CAAS,CAEvD,CAKQ,oBAAoBA,EAA8B,CACtD,MAAMuB,EAAgBvB,EAAU,cAAc,wBAAwB,EACtE,GAAIuB,EAAe,CACf,MAAM4B,EAAe5C,EAAuB,MAAO,CAC/C,UAAW,kBAAA,EACZ,yCAAyC,EAE5CY,GAAsBI,EAAe4B,CAAY,EAEjD,MAAMC,EAAgB,KAAK,wBAAA,EAC3B7B,EAAc,mBAAmB,YAAa6B,CAAa,CAC/D,CACJ,CAKQ,yBAAkC,CACtC,MAAMC,EAAc,gCAgCdD,EA7BkB,CACpB,CACI,OAAQ,GAAGC,CAAW,iBACtB,QAAS,GAAGA,CAAW,kBACvB,YAAa,EAAA,EAEjB,CACI,OAAQ,GAAGA,CAAW,qBACtB,QAAS,GAAGA,CAAW,sBACvB,YAAa,EAAA,EAEjB,CACI,OAAQ,GAAGA,CAAW,oBACtB,QAAS,GAAGA,CAAW,qBACvB,YAAa,EAAA,EAEjB,CACI,OAAQ,GAAGA,CAAW,oBACtB,QAAS,GAAGA,CAAW,qBACvB,YAAa,EAAA,EAEjB,CACI,OAAQ,GAAGA,CAAW,sBACtB,QAAS,GAAGA,CAAW,uBACvB,YAAa,EAAA,CACjB,EAKC,IAAI,CAAC9nB,EAAUgE,IAAU,CACtB,MAAM+jB,EAAMzD,EAAI,MAAM,UAAUtkB,EAAS,MAAM,GAAK,GAC9CgoB,EAAU1D,EAAI,MAAM,UAAUtkB,EAAS,OAAO,GAAKA,EAAS,YAGlE,GAAI,CAAC+nB,EAAI,KAAA,GAAU,CAACC,EAAQ,OACxB,MAAO,GAGX,IAAIC,EAAc,GAClB,OAAIjkB,EAAQ0e,EAAgB,cACxBuF,EAAc,sBAEX,8BAA8BF,CAAG,oCAAoCE,CAAW,UAAUD,CAAO,IAC5G,CAAC,EACA,OAAOE,GAAUA,IAAW,EAAE,EAC9B,KAAK,EAAE,EAGZ,OAAKL,EAIE;AAAA;AAAA;AAAA;AAAA,0BAIWA,CAAa;AAAA;AAAA;AAAA;AAAA,UAPpB,EAYf,CAKQ,wBAA+B,CACnC,MAAM9B,EAAWnB,GAAuB,WAAW,EAC/CmB,GACAlB,GAAuBkB,CAAQ,CAEvC,CAKQ,mBAA0B,CAC9B,GAAIlC,KAAkB,CAClB,MAAMS,EAAMwB,GAAwB,KAAK,EACnCqC,EAAavD,GAAuB,cAAc,EAEpDN,GACAY,GAAmBZ,EAAK,CAAE,aAAc,SAAU,EAGlD6D,GACAjD,GAAmBiD,EAAY,CAC3B,aAAc,OACd,WAAc,EAAA,CACjB,CAET,CACJ,CAKQ,qBAA4B,CAChC,GAAI,CACA,MAAMC,EAASlE,GAAA,EACTmE,EAAiB,IACjBC,EAAiB,IAAI9P,EAAO,aAAc,CAC5C,KAAM,GACN,aAAc4P,EAAO,aACrB,cAAeA,EAAO,cACtB,SAAU,CACN,MAAOC,EACP,qBAAsB,EAAA,EAE1B,QAAS,CAACrK,EAAQ,CAAA,CACrB,CAKL,MAAQ,CAER,CACJ,CACJ,CC7ZO,MAAMuK,CAAc,CAKf,aAAc,CAHtB,KAAQ,WAAa,IACrB,KAAQ,YAA0BpE,EAG9B,KAAK,kBAAA,CACT,CAKA,OAAO,aAA6B,CAChC,OAAKoE,EAAc,WACfA,EAAc,SAAW,IAAIA,GAE1BA,EAAc,QACzB,CAKQ,mBAA0B,CAC9B,KAAK,OAAO,IAAI,YAAapE,EAAc,OAAO,SAAS,EAC3D,KAAK,OAAO,IAAI,wBAAyBA,EAAc,OAAO,qBAAqB,EACnF,KAAK,OAAO,IAAI,YAAaA,EAAc,OAAO,SAAS,EAC3D,KAAK,OAAO,IAAI,oBAAqBA,EAAc,OAAO,iBAAiB,CAC/E,CAQA,IAAI1qB,EAAa+uB,EAAiC,CAC9C,OAAO,KAAK,OAAO,IAAI/uB,CAAG,GAAK+uB,CACnC,CAOA,IAAI/uB,EAAa0d,EAAsB,CACnC,KAAK,OAAO,IAAI1d,EAAK0d,CAAK,CAC9B,CAMQ,kBAAkB1d,EAAsB,CAC5C,GAAI,CACA,MAAM4qB,EAAQC,GAAOA,EAAI,MACnBC,EAASF,GAASA,EAAM,UAC9B,OAAI,OAAOE,GAAW,WACXA,EAAO,KAAKF,EAAO5qB,CAAG,EAEjC,MACJ,MAAQ,CACJ,MACJ,CACJ,CAMA,mBAA4B,CACxB,MAAM2rB,EAAiB,KAAK,kBAAkB,gCAAgC,EAC9E,OAAIA,EACO,OAAO,SAAS,OAAOA,CAAc,EAAG,EAAE,EAE9C,KAAK,IAAI,uBAAuB,CAC3C,CAOA,cAAcqD,EAA6B,CAEvC,OADe,KAAK,kBAAkB,wBAAwBA,CAAW,EAAE,GAC1D,EACrB,CAOA,aAAaA,EAA6B,CAEtC,OADe,KAAK,kBAAkB,uBAAuBA,CAAW,EAAE,GACzD,EACrB,CAMA,cAA4B,CACxB,MAAMvjB,EAAsB,CAAA,EACtBwjB,EAAY,KAAK,IAAI,WAAW,EAEtC,QAASzhB,EAAawb,EAAoB,oBAAqBxb,GAAcyhB,EAAWzhB,GAAcwb,EAAoB,gBAAiB,CACvI,MAAMkG,EAAQ,KAAK,cAAc1hB,CAAU,EACrC2hB,EAAO,KAAK,aAAa3hB,CAAU,EAErC0hB,GACAzjB,EAAO,KAAK,CACR,YAAa+B,EACb,MAAA0hB,EACA,KAAMC,GAAQ,GAAA,CACjB,CAET,CAEA,OAAO1jB,CACX,CAMA,kBAA2B,CACvB,OAAO,OAAO,KAAK,kBAAkB,+BAA+B,GAAK,EAAE,CAC/E,CAOA,kBAAkBlF,EAA0B,CACxC,OAAO,OAAO,KAAK,kBAAkB,uCAAuCA,CAAQ,KAAK,GAAK,EAAE,CACpG,CAOA,mBAAmBA,EAA0B,CACzC,OAAO,OAAO,KAAK,kBAAkB,uCAAuCA,CAAQ,MAAM,GAAK,EAAE,CACrG,CAQA,eAAevG,EAAaovB,EAAsC,GAAY,CAC1E,MAAMC,EAAU,KAAK,kBAAkBrvB,CAAG,EACpCsvB,EAAazE,GAAOA,EAAI,WAC9B,GAAIyE,GAAcA,EAAW,MAAO,CAChC,MAAMC,EAASD,EAAW,MAAMD,EAASD,CAAU,EACnD,OAAOI,GAAYD,CAAM,CAC7B,CACA,OAAOvvB,CACX,CAOQ,kBAAkBA,EAAqB,CAC3C,MAAO,GAAG0qB,EAAc,IAAI,iBAAiB,IAAI1qB,CAAG,EACxD,CAMA,gBAA0B,CACtB,MAAO,GAAQ6qB,GAAOA,EAAI,SAAWA,EAAI,QAAQ,KACrD,CAMA,iBAA0B,CACtB,MAAMznB,EAAUynB,GAAOA,EAAI,QAE3B,OADkBznB,GAAWA,EAAQ,KAAOA,EAAQ,IAAI,WAAW,GAC/C,EACxB,CAMA,YAAsB,CAClB,MAAMqsB,EAAQ,KAAK,gBAAA,EACnB,OAAOA,IAAU,QAAUA,IAAU,KACzC,CAMA,kBAA4B,CAExB,OADc,KAAK,gBAAA,IACF,YACrB,CAMA,YAAsB,CAElB,OADc,KAAK,gBAAA,IACF,MACrB,CAOA,aAAaT,EAA8B,CACvC,MAAME,EAAQ,KAAK,cAAcF,CAAW,EAC5C,OAAO,OAAOE,GAAU,UAAYA,EAAM,OAAO,OAASlG,EAAoB,uBAClF,CAMA,oBAA6B,CACzB,MAAMiG,EAAY,KAAK,IAAI,WAAW,EACtC,IAAIS,EAAQ1G,EAAoB,wBAEhC,QAASxb,EAAawb,EAAoB,oBAAqBxb,GAAcyhB,EAAWzhB,GAAcwb,EAAoB,gBAClH,KAAK,aAAaxb,CAAU,IAC5BkiB,GAAS1G,EAAoB,iBAIrC,OAAO0G,CACX,CAMA,wBAAkC,CAC9B,OAAO,KAAK,WAAA,GAAgB,KAAK,mBAAA,EAAuB1G,EAAoB,uBAChF,CAMA,cAAuB,CACnB,MAAM2G,EAAwC,CAAA,EAC9C,SAAW,CAAC3vB,EAAK0d,CAAK,IAAK,KAAK,OAC5BiS,EAAa3vB,CAAG,EAAI0d,EAExB,OAAO,KAAK,UAAUiS,EAAc,CAAC3vB,EAAK0d,IAAUA,EAAOwL,GAAe,WAAW,CACzF,CAOA,aAAa0G,EAA6B,CACtC,GAAI,CACA,MAAMD,EAAe,KAAK,MAAMC,CAAU,EAC1C,SAAW,CAAC5vB,EAAK0d,CAAK,IAAK,OAAO,QAAQiS,CAAY,EAClD,KAAK,OAAO,IAAI3vB,EAAK0d,CAAK,EAE9B,MAAO,EACX,MAAQ,CACJ,MAAO,EACX,CACJ,CAKA,iBAAwB,CACpB,KAAK,OAAO,MAAA,EACZ,KAAK,kBAAA,CACT,CACJ,CC1RO,MAAMmS,CAAa,CAKd,aAAc,CAFtB,KAAQ,SAA4B,CAAA,EAGhC,KAAK,cAAgBf,EAAc,YAAA,EACnC,KAAK,wBAAA,CACT,CAKA,OAAO,aAA4B,CAC/B,OAAKe,EAAa,WACdA,EAAa,SAAW,IAAIA,GAEzBA,EAAa,QACxB,CAKQ,yBAAgC,CACpC,WAAW,iBAAiB,QAAU3lB,GAAU,CAC5C,KAAK,SAASA,EAAM,MAAO,sBAAsB,CACrD,CAAC,EAED,WAAW,iBAAiB,qBAAuBA,GAAU,CACzD,KAAK,SAAS,IAAI,MAAMA,EAAM,MAAM,EAAG,6BAA6B,CACxE,CAAC,CACL,CAOA,SAAS4lB,EAAcplB,EAAU,UAAiB,CAC9C,MAAMqlB,EAAa,CACf,cAAe,KACf,MAAAD,EACA,QAAAplB,CAAA,EAGJ,KAAK,SAAS,KAAKqlB,CAAU,EAGzB,KAAK,SAAS,OAASjH,EAAe,uBACtC,KAAK,SAAS,MAAA,CAItB,CASA,YACIkH,EACAtlB,EACAulB,EAC4B,CAC5B,OAAOD,IAAY,KACfT,GAAUA,EACVO,IACI,KAAK,SAASA,EAAgBplB,CAAO,EAC9BulB,EACX,CAER,CASA,WACID,EACAtlB,EACAulB,EACmB,CACnB,GAAI,CACA,OAAOD,EAAA,CACX,OAASF,EAAO,CACZ,YAAK,SAASA,EAAgBplB,CAAO,EAC9BulB,CACX,CACJ,CAOA,WAAW3B,EAAsB,CAC7B,GAAI,CAACA,GAAO,OAAOA,GAAQ,SACvB,MAAO,GAGX,GAAI,CAEA,MAAO,EADQ,IAAI,IAAIA,CAAG,CAE9B,MAAQ,CACJ,MAAO,EACX,CACJ,CASA,oBAAoBU,EAAqBkB,EAAkBC,EAAmC,CAC1F,MAAMC,EAAmB,CAAA,EAEzB,OAAI,CAAC,OAAO,UAAUpB,CAAW,GAC7BA,EAAclG,EAAe,kBAC7BkG,EAAclG,EAAe,mBAC7BsH,EAAO,KAAK,gCAAgCtH,EAAe,gBAAgB,QAAQA,EAAe,gBAAgB,EAAE,GAGpH,CAACoH,GAAY,CAAC,KAAK,WAAWA,CAAQ,IACtCE,EAAO,KAAK,yCAAyC,EAGrDD,GAAW,CAAC,KAAK,WAAWA,CAAO,GACnCC,EAAO,KAAK,oCAAoC,EAG7C,CACH,QAASA,EAAO,SAAWpH,EAAoB,wBAC/C,OAAAoH,CAAA,CAER,CAQA,gBAAgBvsB,EAAkB6G,EAAU,qBAAsC,CAC9E,GAAI,CACA,MAAM9G,EAAU,SAAS,cAAcC,CAAQ,EAC/C,GAAI,CAACD,EAAS,CACV,KAAK,SAAS,IAAI,MAAM,sBAAsBC,CAAQ,EAAE,EAAG6G,CAAO,EAClE,MACJ,CACA,OAAO9G,CACX,OAASksB,EAAO,CACZ,KAAK,SAASA,EAAgBplB,CAAO,EACrC,MACJ,CACJ,CAQA,iBAA0BslB,EAA0BtlB,EAAsC,CACtF,OAAO,KAAK,WAAWslB,EAAW,kBAAkBtlB,CAAO,EAAE,CACjE,CAOA,sBAAsB2lB,EAAiBC,EAAqC,QAAe,CACvF,GAAI,CAEIzF,EAAI,QAAUA,EAAI,OAAO,MACzBA,EAAI,OAAO,KAAK,CACZ,KAAMyF,EACN,QAASD,CAAA,CACH,CAIlB,MAAQ,CAER,CACJ,CAMA,aAA+B,CAC3B,MAAO,CAAC,GAAG,KAAK,QAAQ,CAC5B,CAKA,eAAsB,CAClB,KAAK,SAAW,CAAA,CACpB,CAMA,gBAAyB,CACrB,MAAME,EAAa,KAAK,SAAS,IAAIC,IAAU,CAC3C,UAAWA,EAAM,UAAU,YAAA,EAC3B,QAASA,EAAM,MAAM,QACrB,MAAOA,EAAM,MAAM,MACnB,QAASA,EAAM,OAAA,EACjB,EAGF,IAAIjB,EAAS,KAAK,UAAUgB,CAAU,EAGtC,GAAI,CACA,MAAME,EAAS,KAAK,MAAMlB,CAAM,EAChCA,EAAS,KAAK,qBAAqBkB,EAAQvH,GAAe,WAAW,CACzE,MAAQ,CAER,CAEA,OAAOqG,CACX,CAKQ,qBAAqB5vB,EAAc+wB,EAAwB,CAC/D,MAAMC,EAAS,IAAI,OAAOD,CAAM,EAChC,OAAO,KAAK,UAAU/wB,EAAK,CAACK,EAAK0d,IAAUA,EAAOiT,CAAM,CAC5D,CAMA,mBAA2C,CACvC,MAAMC,EAAoB,CAAA,EAG1B,OAAI,OAAO/F,EAAQ,KACf+F,EAAQ,KAAK,mBAAmB,EAU7B,CACH,QAASA,EAAQ,SAAW5H,EAAoB,wBAChD,QAAA4H,CAAA,CAER,CAMA,uBAAkD,CAC9C,MAAMC,EAAmB,CAAA,EACnBlC,EAAS,KAAK,cAAc,mBAAA,EAE7BA,EAAO,aACRkC,EAAO,KAAK,gCAAgC,EAGhD,MAAM5B,EAAYN,EAAO,WACrBM,EAAYnG,EAAe,uBAAyBmG,EAAYnG,EAAe,wBAC/E+H,EAAO,KAAK,gCAAgC/H,EAAe,qBAAqB,QAAQA,EAAe,qBAAqB,EAAE,EAGlI,MAAM6C,EAAiBgD,EAAO,eAC9B,OAAIhD,EAAiB7C,EAAe,qBAAuB6C,EAAiB7C,EAAe,sBACvF+H,EAAO,KAAK,qCAAqC/H,EAAe,mBAAmB,QAAQA,EAAe,mBAAmB,eAAe,EAOzI,CACH,QAAS+H,EAAO,SAFF,EAGd,OAAAA,CAAA,CAER,CAMA,YAAsB,CAClB,GAAI,CACA,MAAMC,EAAW,KAAK,kBAAA,EACtB,GAAI,CAACA,EAAS,QACV,YAAK,SACD,IAAI,MAAM,yBAAyBA,EAAS,QAAQ,KAAK,IAAI,CAAC,EAAE,EAChE,kBAAA,EAEG,GAGX,MAAMC,EAAc,KAAK,sBAAA,EACzB,OAAKA,EAAY,SACb,KAAK,SACD,IAAI,MAAM,yBAAyBA,EAAY,OAAO,KAAK,IAAI,CAAC,EAAE,EAClE,qBAAA,EAKD,EACX,OAASjB,EAAO,CACZ,YAAK,SAASA,EAAgB,8BAA8B,EACrD,EACX,CACJ,CACJ,CC5UAjF,EAAI,aAAa,IAAIH,EAAc,IAAI,YAAa,IAAM,CACtD,MAAMsG,EAAenB,EAAa,YAAA,EAC5BoB,EAAgBnC,EAAc,YAAA,EAGpC,GAAI,CAACkC,EAAa,aACd,OAGJ,MAAME,EAAmB,IAAIvG,GACvBwG,EAAY,IAAI/E,GAEtBxsB,GAAAA,OAAOwxB,GAAc,UAAW,OAAQ,SAAoCC,EAAgB,CACxFL,EAAa,WAAW,IAAM,CACtBC,EAAc,cACdK,GAAoBD,EAAOH,EAAkBC,CAAS,CAE9D,EAAG,8BAA8B,CACrC,CAAC,CACL,CAAC,EAKD,MAAMG,GAAsB,CACxBD,EACAH,EACAC,IACO,CACP,GAAI,CAEAD,EAAiB,sBAAsBG,CAAK,EAG5CE,GAAkBJ,CAAS,EAGtBtG,EAAI,QAAQ,MACb2G,GAAA,CAGR,MAAQ,CAER,CACJ,EAKMD,GAAqBJ,GAA+B,CACtD,GAAI,CACK,SAAS,eAAe,oBAAoB,GAC7CA,EAAU,qBAAA,CAGlB,MAAQ,CAER,CACJ,EAKMK,GAAgB,IAAY,CAC9B,IAAIC,EAAsB,SAAS,eAAe/G,EAAc,GAAG,YAAY,EAE/E,GAAI+G,IAAwB,KAAM,CAE9B,MAAMC,EAAgB7G,EAAI,MAAM,UAAU,+BAA+B,GAAKH,EAAc,GAAG,cAE/F+G,EAAsB,SAAS,cAAc,KAAK,EAClDA,EAAoB,GAAK/G,EAAc,GAAG,aAC1C+G,EAAoB,MAAM,QAAU,eACpCA,EAAoB,MAAM,UAAY,MACtCA,EAAoB,UAAY,aAAaC,CAAa,6BAE1D,MAAMC,EAAc,SAAS,cAAc,kCAAkC,EACzEA,GAAeA,EAAY,YAC3BA,EAAY,WAAW,OAAOF,CAAmB,CAEzD,CACJ", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}