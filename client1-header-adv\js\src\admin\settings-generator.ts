import app from 'flarum/admin/app';
import DynamicSlideSettingsComponent from './components/dynamic-slide-settings-component';
// oxlint-disable-next-line id-length
import m from 'mithril';
import { ADMIN_CONSTANTS } from '../common/config/constants';
import type { ExtensionData } from '../common/config/types';

/**
 * Settings generator utility for admin interface
 */
export class SettingsGenerator {
    private extensionId: string;
    private extensionData: ExtensionData;

    constructor(extensionId: string) {
        this.extensionId = extensionId;
        this.extensionData = app.extensionData.for(extensionId) as ExtensionData;
    }

    /**
     * Register transition time setting
     */
    registerTransitionTimeSetting(): this {
        this.extensionData.registerSetting({
            setting: `${this.extensionId}.TransitionTime`,
            type: 'number',
            label: String(app.translator.trans('wusong8899-client1.admin.TransitionTime')),
        });
        return this;
    }

    /**
     * Register header icon URL setting
     */
    registerHeaderIconUrlSetting(): this {
        this.extensionData.registerSetting({
            setting: `${this.extensionId}.HeaderIconUrl`,
            type: 'url',
            label: String(app.translator.trans('wusong8899-client1.admin.HeaderIconUrl')),
            help: String(app.translator.trans('wusong8899-client1.admin.HeaderIconUrlHelp')),
        });
        return this;
    }

    /**
     * Register social media settings
     */
    registerSocialMediaSettings(): this {
        const socialPlatforms = ['Kick', 'Facebook', 'Twitter', 'YouTube', 'Instagram'];

        for (const platform of socialPlatforms) {
            // URL setting
            this.extensionData.registerSetting({
                setting: `${this.extensionId}.Social${platform}Url`,
                type: 'url',
                label: String(app.translator.trans(`wusong8899-client1.admin.Social${platform}Url`)),
                help: String(app.translator.trans(`wusong8899-client1.admin.Social${platform}UrlHelp`)),
            });

            // Icon setting
            this.extensionData.registerSetting({
                setting: `${this.extensionId}.Social${platform}Icon`,
                type: 'text',
                label: String(app.translator.trans(`wusong8899-client1.admin.Social${platform}Icon`)),
                help: String(app.translator.trans(`wusong8899-client1.admin.Social${platform}IconHelp`)),
            });
        }

        return this;
    }

    /**
     * Register dynamic slide settings component
     * @param maxSlides - Maximum number of slides to configure
     */
    registerSlideSettings(maxSlides = ADMIN_CONSTANTS.DEFAULT_MAX_SLIDES): this {
        this.extensionData.registerSetting(() =>
            m(DynamicSlideSettingsComponent, {
                extensionId: this.extensionId,
                maxSlides: maxSlides
            })
        );
        return this;
    }

    /**
     * Register all settings for the extension
     * @param maxSlides - Maximum number of slides to configure
     */
    registerAllSettings(maxSlides = ADMIN_CONSTANTS.DEFAULT_MAX_SLIDES): this {
        return this
            .registerTransitionTimeSetting()
            .registerHeaderIconUrlSetting()
            .registerSocialMediaSettings()
            .registerSlideSettings(maxSlides);
    }
}

/**
 * Configuration constants
 */
// Centralized config is in js/src/common/config.
// Kept for backward compatibility; prefer importing from '../../common/config'.
export const EXTENSION_CONFIG = {
    EXTENSION_ID: 'wusong8899-client1-header-adv',
    MAX_SLIDES: ADMIN_CONSTANTS.DEFAULT_MAX_SLIDES,
    DEFAULT_TRANSITION_TIME: 5000,
};

/**
 * Initialize admin settings
 * @param extensionId - The extension identifier
 * @param maxSlides - Maximum number of slides
 */
export const initializeAdminSettings = (
    extensionId = EXTENSION_CONFIG.EXTENSION_ID,
    maxSlides = EXTENSION_CONFIG.MAX_SLIDES
): void => {
    const generator = new SettingsGenerator(extensionId);
    generator.registerAllSettings(maxSlides);
};
