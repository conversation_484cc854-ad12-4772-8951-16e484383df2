import type { RootConfig, Environment } from './types';
import {
  EXTENSION_CONFIG,
  TIMING,
  DOM_ELEMENTS,
  CSS_CLASSES,
  CSS_SELECTORS
} from './constants';

export const defaultConfig: RootConfig = {
  env: (process.env.NODE_ENV as Environment) || 'production',
  app: {
    extensionId: EXTENSION_CONFIG.ID,
    translationPrefix: EXTENSION_CONFIG.TRANSLATION_PREFIX,
  },
  slider: {
    maxSlides: EXTENSION_CONFIG.MAX_SLIDES,
    defaultTransitionTime: TIMING.DEFAULT_TRANSITION_TIME,
    checkTime: TIMING.CHECK_INTERVAL,
    dataCheckInterval: TIMING.DATA_CHECK_INTERVAL,
    dom: {
      containerId: DOM_ELEMENTS.SWIPER_AD_CONTAINER_ID,
      swiperClass: CSS_CLASSES.AD_SWIPER,
    },
    swiper: {
      spaceBetween: 30,
      effect: 'coverflow',
      centeredSlides: true,
      slidesPerView: 2,
      coverflowEffect: {
        rotate: 0,
        depth: 100,
        modifier: 1,
        slideShadows: true,
        stretch: 0,
      },
      pagination: {
        el: CSS_SELECTORS.SWIPER_PAGINATION_EL,
        type: 'bullets',
      },
      navigation: {
        nextEl: CSS_SELECTORS.SWIPER_BUTTON_NEXT_EL,
        prevEl: CSS_SELECTORS.SWIPER_BUTTON_PREV_EL,
      },
    },
  },
  ui: {
    headerIconId: DOM_ELEMENTS.HEADER_ICON_ID,
    headerIconUrl: EXTENSION_CONFIG.HEADER_ICON_URL,
  },
  data: {
    apiResources: {
      // Links queue functionality moved to client1-links-queue extension
    },
  },
};

