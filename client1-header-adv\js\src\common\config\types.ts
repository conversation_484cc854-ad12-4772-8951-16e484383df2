export type Environment = 'development' | 'staging' | 'production' | 'test';

export interface AppConfig {
  extensionId: string;
  translationPrefix: string;
}

export interface SliderDomConfig {
  containerId: string; // e.g., 'swiperAdContainer'
  swiperClass: string; // e.g., 'adSwiper'
}

export interface SwiperCoverflowConfig {
  rotate: number;
  depth: number;
  modifier: number;
  slideShadows: boolean;
  stretch: number;
}

export interface SwiperPaginationConfig {
  el: string;
  type: 'bullets' | 'fraction' | 'progressbar' | 'custom';
}

export interface SwiperNavigationConfig {
  nextEl: string;
  prevEl: string;
}

export interface SwiperOptionsConfig {
  spaceBetween: number;
  effect: 'slide' | 'fade' | 'cube' | 'coverflow' | 'flip' | string;
  centeredSlides: boolean;
  slidesPerView: number | 'auto';
  coverflowEffect: SwiperCoverflowConfig;
  pagination: SwiperPaginationConfig;
  navigation: SwiperNavigationConfig;
}

export interface SliderConfig {
  maxSlides: number;
  defaultTransitionTime: number; // ms
  checkTime: number; // ms, small polling interval
  dataCheckInterval: number; // ms, UI/data polling
  dom: SliderDomConfig;
  swiper: SwiperOptionsConfig;
}

export interface UIConfig {
  headerIconId: string;
  headerIconUrl: string;
}

export interface DataConfig {
  apiResources: Record<string, unknown>;
}

export interface RootConfig {
  env: Environment;
  app: AppConfig;
  slider: SliderConfig;
  ui: UIConfig;
  data: DataConfig;
}

// Additional types for better type safety
export interface SlideData {
  slideNumber: number;
  image: string;
  link: string;
}

export interface TagData {
  url: string;
  background: string;
  name: string;
  nameColor: string;
  description: string;
  descColor: string;
}

export interface ErrorLogEntry {
  timestamp: Date;
  error: Error;
  context: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface DependencyCheckResult {
  isValid: boolean;
  missing: string[];
}

export interface ConfigurationCheckResult {
  isValid: boolean;
  issues: string[];
}

export interface MobileConfig {
  spaceBetween: number;
  slidesPerView: number;
}

export type EventType = 'touchend' | 'click';

export type NotificationType = 'error' | 'warning' | 'info';

// Flarum-specific types
export interface FlarumVnode {
  dom?: HTMLElement;
  [key: string]: unknown;
}

export type FlarumComponentAttrs = Record<string, unknown>;

export interface FlarumApp {
  forum: {
    attribute: (key: string) => unknown;
  };
  data: {
    settings: Record<string, string>;
  };
  session: {
    user?: unknown;
  };
  translator: {
    trans: (key: string, params?: Record<string, unknown>) => string;
  };
  request: (options: RequestOptions) => Promise<unknown>;
  alerts: {
    show: (alert: AlertOptions) => void;
  };
  extensionData: {
    for: (extensionId: string) => ExtensionData;
  };
}

export interface RequestOptions {
  method: string;
  url: string;
  body?: Record<string, unknown>;
}

export interface AlertOptions {
  type: string;
  content: string;
}

export interface ExtensionData {
  registerSetting: (config: SettingConfig | (() => unknown)) => void;
}

export interface SettingConfig {
  setting: string;
  type: string;
  label: string;
  help?: string;
}

// Component-specific types
export interface HeaderIconSettingComponentAttrs extends FlarumComponentAttrs {
  setting: string;
  label: string;
  help?: string;
}

export interface DynamicSlideSettingsComponentAttrs extends FlarumComponentAttrs {
  extensionId: string;
  maxSlides?: number;
}

export interface SlideDataInternal {
  id: number;
  link: string;
  image: string;
}

// Timeout management types
export interface ComponentWithTimeouts {
  [key: string]: NodeJS.Timeout | undefined;
  saveTimeout?: NodeJS.Timeout;
}

// DOM utility types
export interface DOMElementOptions {
  className?: string;
  id?: string;
  [key: string]: unknown;
}

export interface StylesObject {
  [property: string]: string | number;
}

// Swiper-related types
export interface SwiperInstance {
  destroy: (deleteInstance?: boolean, cleanStyles?: boolean) => void;
  [key: string]: unknown;
}

