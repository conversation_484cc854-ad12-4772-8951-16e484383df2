import { extend } from 'flarum/common/extend';
import app from 'flarum/forum/app';
import HeaderPrimary from 'flarum/forum/components/HeaderPrimary';
import TagsPage from 'flarum/tags/components/TagsPage';

import { SlideshowManager } from './components/slideshow-manager';
import { UIManager } from './components/ui-manager';
import { ErrorHandler } from './utils/error-handler';
import { ConfigManager } from './utils/config-manager';
import { defaultConfig } from '../common/config';

/**
 * Main extension initializer
 */
app.initializers.add(defaultConfig.app.extensionId, () => {
    const errorHandler = ErrorHandler.getInstance();
    const configManager = ConfigManager.getInstance();

    // Initialize error handling
    if (!errorHandler.initialize()) {
        return;
    }

    const slideshowManager = new SlideshowManager();
    const uiManager = new UIManager();

    extend(HeaderPrimary.prototype, 'view', function headerPrimaryViewExtension(vnode: unknown) {
        errorHandler.handleSync(() => {
            if (configManager.isTagsPage()) {
                initializeExtension(vnode, slideshowManager, uiManager);
            }
        }, 'HeaderPrimary view extension');
    });

    // Also extend TagsPage to ensure UI components are set up when the page loads
    extend(TagsPage.prototype, 'oncreate', function tagsPageOnCreateExtension(_vnode: unknown) {
        errorHandler.handleSync(() => {
            // Force UI components setup regardless of other conditions
            const DOM_READY_DELAY = 100;
            setTimeout(() => {
                // Always try to setup UI components
                uiManager.changeCategoryLayout();
            }, DOM_READY_DELAY);
        }, 'TagsPage oncreate extension');
    });

    extend(TagsPage.prototype, 'onupdate', function tagsPageOnUpdateExtension(_vnode: unknown) {
        errorHandler.handleSync(() => {
            // Check if swiper container doesn't exist and create it
            if (!document.getElementById("swiperTagContainer")) {
                const DOM_READY_DELAY = 100;
                setTimeout(() => {
                    uiManager.changeCategoryLayout();
                }, DOM_READY_DELAY);
            }
        }, 'TagsPage onupdate extension');
    });
});

/**
 * Initialize extension components
 */
const initializeExtension = (
    vnode: unknown,
    slideshowManager: SlideshowManager,
    uiManager: UIManager
): void => {
    try {
        // Setup UI components first (TagTiles conversion)
        setupUIComponents(uiManager);

        // Setup slideshow (only if configured)
        try {
            slideshowManager.attachAdvertiseHeader(vnode);
        } catch {
            // Slideshow setup failed, but continue with other features
        }

        // Add header icon for non-logged users
        if (!app.session.user) {
            addHeaderIcon();
        }

    } catch {
        // Silently handle initialization errors
    }
}

/**
 * Setup UI components
 */
const setupUIComponents = (uiManager: UIManager): void => {
    try {
        if (!document.getElementById("swiperTagContainer")) {
            uiManager.changeCategoryLayout();
            // Additional UI setup would go here
        }
    } catch {
        // Silently handle UI setup errors
    }
};

/**
 * Add header icon for branding
 */
const addHeaderIcon = (): void => {
    let headerIconContainer = document.getElementById(defaultConfig.ui.headerIconId);

    if (headerIconContainer === null) {
        // Get header icon URL from settings, fallback to default config
        const headerIconUrl = app.forum.attribute('Client1HeaderAdvHeaderIconUrl') || defaultConfig.ui.headerIconUrl;

        headerIconContainer = document.createElement("div");
        headerIconContainer.id = defaultConfig.ui.headerIconId;
        headerIconContainer.style.display = 'inline-block';
        headerIconContainer.style.marginTop = '8px';
        headerIconContainer.innerHTML = `<img src="${headerIconUrl}" style="height: 24px;" />`;

        const backControl = document.querySelector("#app-navigation .App-backControl");
        if (backControl && backControl.firstChild) {
            backControl.firstChild.before(headerIconContainer);
        }
    }
}


