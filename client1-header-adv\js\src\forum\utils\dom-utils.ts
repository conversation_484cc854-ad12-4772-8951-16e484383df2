/**
 * DOM manipulation utilities - functional approach
 */

import { ERROR_HANDLING } from '../../common/config/constants';

/**
 * Create a DOM element with specified attributes
 * @param tagName - HTML tag name
 * @param attributes - Element attributes
 * @param innerHTML - Inner HTML content
 * @returns Created element
 */
export const createElement = (
    tagName: string,
    attributes: Record<string, string> = {},
    innerHTML = ''
): HTMLElement => {
    const element = document.createElement(tagName);

    for (const [key, value] of Object.entries(attributes)) {
        if (key === 'className') {
            element.className = value;
        } else if (key === 'style') {
            element.setAttribute('style', value);
        } else {
            element.setAttribute(key, value);
        }
    }

    if (innerHTML) {
        element.innerHTML = innerHTML;
    }

    return element;
};

/**
 * Safely get element by ID
 * @param id - Element ID
 * @returns Element or null if not found
 */
export const getElementById = (id: string): HTMLElement | null =>
    document.getElementById(id);

/**
 * Safely query selector
 * @param selector - CSS selector
 * @param parent - Parent element (default: document)
 * @returns Element or null if not found
 */
export const querySelector = (selector: string, parent: Element | Document = document): Element | null => {
    try {
        if (!parent || !selector) {
            throw new Error('Invalid selector or parent');
        }
        return parent.querySelector(selector);
    } catch {
        return document.querySelector('') as null; // Return null safely
    }
};

/**
 * Safely query all elements
 * @param selector - CSS selector
 * @param parent - Parent element (default: document)
 * @returns NodeList of elements
 */
export const querySelectorAll = (selector: string, parent: Element | Document = document): NodeListOf<Element> => {
    try {
        if (!parent || !selector) {
            return document.querySelectorAll(''); // Return empty NodeList
        }
        return parent.querySelectorAll(selector);
    } catch {
        return document.querySelectorAll(''); // Return empty NodeList
    }
};

/**
 * Add event listener with error handling
 * @param element - Target element
 * @param event - Event type
 * @param handler - Event handler
 */
export const addEventListener = (
    element: Element,
    event: string,
    handler: EventListener
): void => {
    try {
        if (element && event && handler) {
            element.addEventListener(event, handler);
        }
    } catch {
        // Silently handle event listener errors
    }
};

/**
 * Remove event listener with error handling
 * @param element - Target element
 * @param event - Event type
 * @param handler - Event handler
 */
export const removeEventListener = (
    element: Element,
    event: string,
    handler: EventListener
): void => {
    try {
        if (element && event && handler) {
            element.removeEventListener(event, handler);
        }
    } catch {
        // Silently handle event listener removal errors
    }
};

/**
 * Set CSS styles on element
 * @param element - Target element
 * @param styles - Style properties
 */
export const setStyles = (element: HTMLElement, styles: Record<string, string>): void => {
    if (!element || !styles) {
        return;
    }
    for (const [property, value] of Object.entries(styles)) {
        try {
            element.style.setProperty(property, value);
        } catch {
            // Silently handle style setting errors
        }
    }
};

/**
 * Append element to parent with error handling
 * @param parent - Parent element
 * @param child - Child element to append
 */
export const appendChild = (parent: Element, child: Element): void => {
    try {
        if (parent && child) {
            parent.appendChild(child);
        }
    } catch {
        // Silently handle append errors
    }
};

/**
 * Prepend element to parent with error handling
 * @param parent - Parent element
 * @param child - Child element to prepend
 */
export const prependChild = (parent: Element, child: Element): void => {
    try {
        if (parent && child && parent.firstChild) {
            parent.firstChild.before(child);
        }
    } catch {
        // Silently handle prepend errors
    }
};

/**
 * Remove element safely
 * @param element - Element to remove
 */
export const removeElement = (element: Element): void => {
    try {
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
        }
    } catch {
        // Silently handle element removal errors
    }
};

/**
 * Check if element is visible
 * @param element - Element to check
 * @returns True if element is visible
 */
export const isElementVisible = (element: Element): boolean => {
    try {
        if (!element) {
            return false;
        }
        const style = globalThis.getComputedStyle(element);
        return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
    } catch {
        return false;
    }
};

/**
 * Get element dimensions safely
 * @param element - Element to measure
 * @returns Element dimensions or undefined
 */
export const getElementDimensions = (element: Element): { width: number; height: number } | undefined => {
    try {
        if (!element) {
            throw new Error('Invalid element');
        }
        const rect = element.getBoundingClientRect();
        return {
            width: rect.width,
            height: rect.height
        };
    } catch {
        return; // Return undefined implicitly
    }
};

/**
 * Scroll element into view safely
 * @param element - Element to scroll to
 * @param behavior - Scroll behavior
 */
export const scrollIntoView = (element: Element, behavior: ScrollBehavior = 'smooth'): void => {
    try {
        if (element && element.scrollIntoView) {
            element.scrollIntoView({ behavior, block: 'nearest' });
        }
    } catch {
        // Silently handle scroll errors
    }
};

/**
 * Wait for element to appear in DOM
 * @param selector - CSS selector
 * @param timeout - Timeout in milliseconds
 * @returns Promise that resolves with element or null
 */
export const waitForElement = (selector: string, timeout = ERROR_HANDLING.DOM_READY_TIMEOUT): Promise<Element | null> =>
    new Promise((resolve) => {
        const element = querySelector(selector);
        if (element) {
            resolve(element);
            return;
        }

        const observer = new MutationObserver(() => {
            const foundElement = querySelector(selector);
            if (foundElement) {
                observer.disconnect();
                resolve(foundElement);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        setTimeout(() => {
            observer.disconnect();
            resolve(document.querySelector('') as null); // Return null safely
        }, timeout);
    });
