{"private": true, "name": "@wusong8899/flarum-tag-background", "devDependencies": {"flarum-tsconfig": "^1.0.0", "flarum-webpack-config": "^2.0.0", "oxlint": "^1.11.2", "vite": "^7.1.2"}, "scripts": {"dev": "vite -c vite.config.admin.mts build --mode development && vite -c vite.config.forum.mts build --mode development", "dev:admin": "vite -c vite.config.admin.mts build --mode development", "dev:forum": "vite -c vite.config.forum.mts build --mode development", "dev:watch": "vite -c vite.config.admin.mts build --watch --mode development && vite -c vite.config.forum.mts build --watch --mode development", "build": "vite -c vite.config.admin.mts build --mode production && vite -c vite.config.forum.mts build --mode production", "lint": "oxlint .", "lint:fix": "oxlint . --fix"}}